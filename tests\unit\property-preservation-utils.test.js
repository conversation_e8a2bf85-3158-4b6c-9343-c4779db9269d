import { describe, test, expect, beforeEach, vi } from 'vitest';

// Mock dependencies
vi.mock('../../src/lib/hubspot-api.js', () => ({
  getDealProperties: vi.fn()
}));

vi.mock('../../src/lib/validation-utils.js', () => ({
  processVatStatus: vi.fn(),
  processLanguageProperty: vi.fn()
}));

import {
  preserveVatAndLanguageSettings,
  preserveVatSettings,
  preserveLanguageSettings
} from '../../src/lib/property-preservation-utils.js';

import { getDealProperties } from '../../src/lib/hubspot-api.js';
import { processVatStatus, processLanguageProperty } from '../../src/lib/validation-utils.js';

describe('Property Preservation Utils', () => {
  const mockDealId = 'test-deal-123';
  const mockAccessToken = 'test-token';

  beforeEach(() => {
    vi.clearAllMocks();
    // Mock console.log to avoid noise in tests
    vi.spyOn(console, 'log').mockImplementation(() => {});
  });

  describe('preserveVatAndLanguageSettings', () => {
    test('should preserve both VAT and language settings successfully', async () => {
      // Mock HubSpot API response
      getDealProperties.mockResolvedValue({
        'vat___status_podatnika': 'VAT EU;VAT OSS',
        'jezyk_obslugi': 'English'
      });

      // Mock validation utils responses
      processVatStatus.mockReturnValue({
        shouldHaveBR00032: true,
        shouldHaveBR00033: true,
        br00032Quantity: 1,
        br00033Quantity: 1
      });

      processLanguageProperty.mockReturnValue({
        shouldHaveBR00129: true
      });

      const result = await preserveVatAndLanguageSettings(mockDealId, mockAccessToken);

      expect(getDealProperties).toHaveBeenCalledWith(
        mockDealId,
        mockAccessToken,
        ['vat___status_podatnika', 'jezyk_obslugi']
      );
      expect(processVatStatus).toHaveBeenCalledWith('VAT EU;VAT OSS');
      expect(processLanguageProperty).toHaveBeenCalledWith('English');

      expect(result).toEqual({
        shouldHaveBR00032: true,
        shouldHaveBR00033: true,
        br00032Quantity: 1,
        br00033Quantity: 1,
        shouldHaveBR00129: true
      });
    });

    test('should handle empty VAT status and language', async () => {
      getDealProperties.mockResolvedValue({
        'vat___status_podatnika': '',
        'jezyk_obslugi': ''
      });

      processVatStatus.mockReturnValue({
        shouldHaveBR00032: false,
        shouldHaveBR00033: false,
        br00032Quantity: 0,
        br00033Quantity: 0
      });

      processLanguageProperty.mockReturnValue({
        shouldHaveBR00129: true // Empty language should require BR00129
      });

      const result = await preserveVatAndLanguageSettings(mockDealId, mockAccessToken);

      expect(processVatStatus).toHaveBeenCalledWith('');
      expect(processLanguageProperty).toHaveBeenCalledWith('');

      expect(result).toEqual({
        shouldHaveBR00032: false,
        shouldHaveBR00033: false,
        br00032Quantity: 0,
        br00033Quantity: 0,
        shouldHaveBR00129: true
      });
    });

    test('should handle missing properties with fallback to empty strings', async () => {
      getDealProperties.mockResolvedValue({});

      processVatStatus.mockReturnValue({
        shouldHaveBR00032: false,
        shouldHaveBR00033: false,
        br00032Quantity: 0,
        br00033Quantity: 0
      });

      processLanguageProperty.mockReturnValue({
        shouldHaveBR00129: true
      });

      const result = await preserveVatAndLanguageSettings(mockDealId, mockAccessToken);

      expect(processVatStatus).toHaveBeenCalledWith('');
      expect(processLanguageProperty).toHaveBeenCalledWith('');

      expect(result).toEqual({
        shouldHaveBR00032: false,
        shouldHaveBR00033: false,
        br00032Quantity: 0,
        br00033Quantity: 0,
        shouldHaveBR00129: true
      });
    });

    test('should handle Polish language correctly', async () => {
      getDealProperties.mockResolvedValue({
        'vat___status_podatnika': 'VAT 8',
        'jezyk_obslugi': 'Polski'
      });

      processVatStatus.mockReturnValue({
        shouldHaveBR00032: true,
        shouldHaveBR00033: false,
        br00032Quantity: 1,
        br00033Quantity: 0
      });

      processLanguageProperty.mockReturnValue({
        shouldHaveBR00129: false // Polish should not require BR00129
      });

      const result = await preserveVatAndLanguageSettings(mockDealId, mockAccessToken);

      expect(processLanguageProperty).toHaveBeenCalledWith('Polski');
      expect(result.shouldHaveBR00129).toBe(false);
    });

    test('should propagate errors from getDealProperties', async () => {
      const error = new Error('HubSpot API error');
      getDealProperties.mockRejectedValue(error);

      await expect(preserveVatAndLanguageSettings(mockDealId, mockAccessToken))
        .rejects.toThrow('HubSpot API error');
    });
  });

  describe('preserveVatSettings', () => {
    test('should preserve only VAT settings successfully', async () => {
      getDealProperties.mockResolvedValue({
        'vat___status_podatnika': 'VAT EU;VAT 9M'
      });

      processVatStatus.mockReturnValue({
        shouldHaveBR00032: true,
        shouldHaveBR00033: false,
        br00032Quantity: 2,
        br00033Quantity: 0
      });

      const result = await preserveVatSettings(mockDealId, mockAccessToken);

      expect(getDealProperties).toHaveBeenCalledWith(
        mockDealId,
        mockAccessToken,
        ['vat___status_podatnika']
      );
      expect(processVatStatus).toHaveBeenCalledWith('VAT EU;VAT 9M');

      expect(result).toEqual({
        shouldHaveBR00032: true,
        shouldHaveBR00033: false,
        br00032Quantity: 2,
        br00033Quantity: 0
      });
    });

    test('should handle empty VAT status', async () => {
      getDealProperties.mockResolvedValue({
        'vat___status_podatnika': ''
      });

      processVatStatus.mockReturnValue({
        shouldHaveBR00032: false,
        shouldHaveBR00033: false,
        br00032Quantity: 0,
        br00033Quantity: 0
      });

      const result = await preserveVatSettings(mockDealId, mockAccessToken);

      expect(processVatStatus).toHaveBeenCalledWith('');
      expect(result).toEqual({
        shouldHaveBR00032: false,
        shouldHaveBR00033: false,
        br00032Quantity: 0,
        br00033Quantity: 0
      });
    });

    test('should handle missing VAT property', async () => {
      getDealProperties.mockResolvedValue({});

      processVatStatus.mockReturnValue({
        shouldHaveBR00032: false,
        shouldHaveBR00033: false,
        br00032Quantity: 0,
        br00033Quantity: 0
      });

      const result = await preserveVatSettings(mockDealId, mockAccessToken);

      expect(processVatStatus).toHaveBeenCalledWith('');
      expect(result).toEqual({
        shouldHaveBR00032: false,
        shouldHaveBR00033: false,
        br00032Quantity: 0,
        br00033Quantity: 0
      });
    });

    test('should handle complex VAT combinations', async () => {
      getDealProperties.mockResolvedValue({
        'vat___status_podatnika': 'VAT OSS;VAT IOSS'
      });

      processVatStatus.mockReturnValue({
        shouldHaveBR00032: false,
        shouldHaveBR00033: true,
        br00032Quantity: 0,
        br00033Quantity: 2 // VAT OSS + extra for IOSS combination
      });

      const result = await preserveVatSettings(mockDealId, mockAccessToken);

      expect(processVatStatus).toHaveBeenCalledWith('VAT OSS;VAT IOSS');
      expect(result.br00033Quantity).toBe(2);
    });
  });

  describe('preserveLanguageSettings', () => {
    test('should preserve only language settings successfully', async () => {
      getDealProperties.mockResolvedValue({
        'jezyk_obslugi': 'German'
      });

      processLanguageProperty.mockReturnValue({
        shouldHaveBR00129: true
      });

      const result = await preserveLanguageSettings(mockDealId, mockAccessToken);

      expect(getDealProperties).toHaveBeenCalledWith(
        mockDealId,
        mockAccessToken,
        ['jezyk_obslugi']
      );
      expect(processLanguageProperty).toHaveBeenCalledWith('German');

      expect(result).toEqual({
        shouldHaveBR00129: true
      });
    });

    test('should handle Polish language', async () => {
      getDealProperties.mockResolvedValue({
        'jezyk_obslugi': 'Polski'
      });

      processLanguageProperty.mockReturnValue({
        shouldHaveBR00129: false
      });

      const result = await preserveLanguageSettings(mockDealId, mockAccessToken);

      expect(processLanguageProperty).toHaveBeenCalledWith('Polski');
      expect(result).toEqual({
        shouldHaveBR00129: false
      });
    });

    test('should handle empty language', async () => {
      getDealProperties.mockResolvedValue({
        'jezyk_obslugi': ''
      });

      processLanguageProperty.mockReturnValue({
        shouldHaveBR00129: true
      });

      const result = await preserveLanguageSettings(mockDealId, mockAccessToken);

      expect(processLanguageProperty).toHaveBeenCalledWith('');
      expect(result).toEqual({
        shouldHaveBR00129: true
      });
    });

    test('should handle missing language property', async () => {
      getDealProperties.mockResolvedValue({});

      processLanguageProperty.mockReturnValue({
        shouldHaveBR00129: true
      });

      const result = await preserveLanguageSettings(mockDealId, mockAccessToken);

      expect(processLanguageProperty).toHaveBeenCalledWith('');
      expect(result).toEqual({
        shouldHaveBR00129: true
      });
    });

    test('should propagate errors from getDealProperties', async () => {
      const error = new Error('Network error');
      getDealProperties.mockRejectedValue(error);

      await expect(preserveLanguageSettings(mockDealId, mockAccessToken))
        .rejects.toThrow('Network error');
    });
  });
});
