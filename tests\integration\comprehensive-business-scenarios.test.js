import { describe, test, expect, beforeEach, vi } from 'vitest';

// ============================================================================
// IMPORTS - All business logic modules and utilities
// ============================================================================
import {
  handleComprehensiveUpdate
} from '../../src/lib/business-logic-handlers.js';

// ============================================================================
// MOCKS - Centralized mock setup for all dependencies
// ============================================================================
vi.mock('../../src/lib/line-item-manager.js', () => ({
  findLineItemBySku: vi.fn(),
  findLineItemBySkuSafe: vi.fn(),
  validateSkuInProductLibrary: vi.fn(),
  createLineItemFromProduct: vi.fn(),
  createLineItem: vi.fn(),
  createLineItemWithQuantity: vi.fn(),
  createLineItemFromProductWithQuantity: vi.fn(),
  updateLineItemQuantity: vi.fn(),
  deleteLineItem: vi.fn(),
  associateLineItemWithDeal: vi.fn(),
  createAndAssociateLineItem: vi.fn(),
  manageLineItemQuantity: vi.fn(),
  manageBooleanLineItem: vi.fn(),
  createLineItemWithCustomPrice: vi.fn(),
  updateLineItemPrice: vi.fn(),
  manageLineItemWithCustomPrice: vi.fn(),
  updateAccountingPackagesToOptimal: vi.fn(),
  calculateBR00069CustomPrice: vi.fn()
}));

vi.mock('../../src/lib/price-fetcher.js', () => ({
  fetchPricesFromHubSpot: vi.fn(),
  getAccountingPackagePrices: vi.fn(),
  getPayrollPrices: vi.fn(),
  getEcommercePackagePrices: vi.fn(),
  getProductPrice: vi.fn(),
  getMultipleProductPrices: vi.fn()
}));

vi.mock('../../src/lib/pit-packages.js', () => ({
  getPitPackagePrices: vi.fn(),
  selectPitPackageForSimplifiedAccounting: vi.fn()
}));

vi.mock('../../src/lib/hubspot-api.js', () => ({
  getDealProperties: vi.fn(),
  updateLineItemsForDeal: vi.fn(),
  getLineItemsForDeal: vi.fn(),
  findProductBySku: vi.fn(),
  updateDealProperties: vi.fn(),
  getDealLineItemsWithDetails: vi.fn()
}));

import { fetchPricesFromHubSpot, getAccountingPackagePrices, getPayrollPrices, getEcommercePackagePrices, getProductPrice, getMultipleProductPrices } from '../../src/lib/price-fetcher.js';
import { getDealProperties, updateLineItemsForDeal, getLineItemsForDeal, findProductBySku, updateDealProperties, getDealLineItemsWithDetails } from '../../src/lib/hubspot-api.js';
import { getPitPackagePrices, selectPitPackageForSimplifiedAccounting } from '../../src/lib/pit-packages.js';

// ============================================================================
// SHARED TEST UTILITIES - Common helpers and mock data
// ============================================================================

/**
 * Creates mock deal properties with default values and optional overrides
 * Used across all test scenarios to ensure consistency
 */
function createMockDealProperties(overrides = {}) {
  return {
    'faktury_rachunki_sprzedazowe___ile_': '0',
    'faktury_rachunki_zakupu___ile_': '0',
    'faktury_walutowe___ile_miesiecznie_': '0',
    'dokumenty_wewnetrzne_wdt__wnt_itp': '0',
    'operacje_kp_kw_walutowe': '0',
    'kp_kw___banki_': '0',
    'kp_kw_gotowka': '0',
    'rodzaj_ksiegowosci': 'Pełna księgowość',
    'jezyk_obslugi': 'Polski',
    'vat___status_podatnika': '',
    'pakiet_kadrowo_placowy': '',
    'umowa_o_prace___liczba_osob': '0',
    'umowy_cywilnoprawne___liczba_pracownikow': '0',
    'ppk___ile_osob_': '0',
    'pfron___ile_osob_': '0',
    'a1___czy_wystepuja_': '0',
    'kto_robi_import_do_moje_ppk': 'Klient',
    'srodki_trwale_i_wartosci_niematerialne_i_prawne___ile_': '0',
    'kasy_fiskalne___ile_': '0',
    'pytania_do_msp': '',
    'ilosc_kont_bankowych___raporty_dzienne': '0',
    'ilosc_kont_bankowych___raporty_miesieczne': '0',
    'ile_kanalow_platniczych_': '0',
    'dodatkowe_skladniki_wynagrodzenia': '0',
    'ile_transakcji_sprzedazy_w_miesiacu_': '0',
    'branza': 'E-commerce', // Default to E-commerce to prevent payment channels field clearing
    'uslugi_do_wyceny': 'Księgowość',
    ...overrides
  };
}

/**
 * Creates standardized mock prices for accounting packages
 * Ensures consistent pricing across all tests
 */
function createMockPrices() {
  return {
    'BR00003': 499,    // BASE full accounting
    'BR00004': 764,    // SILVER full accounting
    'BR00005': 1374,   // GOLD full accounting
    'BR00006': 2499,   // PLATINUM full accounting
    'BR00007': 234,    // BASE simplified accounting
    'BR00008': 294,    // SILVER simplified accounting
    'BR00009': 524,    // GOLD simplified accounting
    'BR00010': 999,    // PLATINUM simplified accounting
    'BR00022': 12.9,   // Extra documents (50 pack)
    'BR00023': 49.9,   // Extra documents (200 pack)
    // Financial statement package SKUs
    'BR00099': 699,    // BASE financial statement
    'BR00100': 899,    // SILVER financial statement
    'BR00101': 1599,   // GOLD financial statement
    'BR00102': 2599    // PLATINUM financial statement
  };
}

// ============================================================================
// COMPREHENSIVE BUSINESS SCENARIO MOCK DATA - Simulating real-world conditions
// ============================================================================

/**
 * Creates mock data for small business scenario
 * Simulates a small business with basic accounting needs
 */
function createSmallBusinessScenario() {
  return createMockDealProperties({
    'faktury_rachunki_sprzedazowe___ile_': '7',
    'faktury_rachunki_zakupu___ile_': '3',
    'faktury_walutowe___ile_miesiecznie_': '1',
    'dokumenty_wewnetrzne_wdt__wnt_itp': '1',
    'operacje_kp_kw_walutowe': '2',
    'kp_kw___banki_': '5',
    'kp_kw_gotowka': '3',
    'rodzaj_ksiegowosci': 'Pełna księgowość',
    'jezyk_obslugi': 'Polski',
    'vat___status_podatnika': '',
    'pakiet_kadrowo_placowy': '',
    'uslugi_do_wyceny': 'Księgowość'
  });
}

/**
 * Creates mock data for medium business scenario
 * Simulates a medium business with payroll and VAT needs
 */
function createMediumBusinessScenario() {
  return createMockDealProperties({
    'faktury_rachunki_sprzedazowe___ile_': '23',
    'faktury_rachunki_zakupu___ile_': '15',
    'faktury_walutowe___ile_miesiecznie_': '3',
    'dokumenty_wewnetrzne_wdt__wnt_itp': '5',
    'operacje_kp_kw_walutowe': '8',
    'kp_kw___banki_': '12',
    'kp_kw_gotowka': '6',
    'rodzaj_ksiegowosci': 'Pełna księgowość',
    'jezyk_obslugi': 'Angielski',
    'vat___status_podatnika': 'VAT EU',
    'pakiet_kadrowo_placowy': 'Płace',
    'umowa_o_prace___liczba_osob': '5',
    'umowy_cywilnoprawne___liczba_pracownikow': '2',
    'ppk___ile_osob_': '3',
    'pfron___ile_osob_': '1',
    'a1___czy_wystepuja_': '1',
    'kto_robi_import_do_moje_ppk': 'Biuro',
    'uslugi_do_wyceny': 'Księgowość;Kadry'
  });
}

/**
 * Creates mock data for large business scenario
 * Simulates a large business with complex VAT and premium payroll
 */
function createLargeBusinessScenario() {
  return createMockDealProperties({
    'faktury_rachunki_sprzedazowe___ile_': '105',
    'faktury_rachunki_zakupu___ile_': '67',
    'faktury_walutowe___ile_miesiecznie_': '12',
    'dokumenty_wewnetrzne_wdt__wnt_itp': '8',
    'operacje_kp_kw_walutowe': '25',
    'kp_kw___banki_': '45',
    'kp_kw_gotowka': '15',
    'rodzaj_ksiegowosci': 'Pełna księgowość',
    'jezyk_obslugi': 'Polski',
    'vat___status_podatnika': 'VAT EU;VAT OSS',
    'pakiet_kadrowo_placowy': 'Kadry i płace PREMIUM',
    'umowa_o_prace___liczba_osob': '15',
    'umowy_cywilnoprawne___liczba_pracownikow': '8',
    'ppk___ile_osob_': '12',
    'pfron___ile_osob_': '3',
    'a1___czy_wystepuja_': '2',
    'kto_robi_import_do_moje_ppk': 'Klient',
    'srodki_trwale_i_wartosci_niematerialne_i_prawne___ile_': '2',
    'kasy_fiskalne___ile_': '1',
    'uslugi_do_wyceny': 'Księgowość;Kadry',
    'dodatkowe_skladniki_wynagrodzenia': '2',
    'pytania_do_msp': '',
    'ilosc_kont_bankowych___raporty_dzienne': '0',
    'ilosc_kont_bankowych___raporty_miesieczne': '0',
    'ile_kanalow_platniczych_': '0'
  });
}

/**
 * Creates mock data for enterprise scenario
 * Simulates an enterprise with maximum complexity
 */
function createEnterpriseScenario() {
  return createMockDealProperties({
    'faktury_rachunki_sprzedazowe___ile_': '250',
    'faktury_rachunki_zakupu___ile_': '180',
    'faktury_walutowe___ile_miesiecznie_': '25',
    'dokumenty_wewnetrzne_wdt__wnt_itp': '15',
    'operacje_kp_kw_walutowe': '50',
    'kp_kw___banki_': '80',
    'kp_kw_gotowka': '25',
    'rodzaj_ksiegowosci': 'Pełna księgowość',
    'jezyk_obslugi': 'Niemiecki',
    'vat___status_podatnika': 'VAT EU;VAT OSS;VAT 8;VAT 9M',
    'pakiet_kadrowo_placowy': 'Ryczałt',
    'umowa_o_prace___liczba_osob': '25',
    'umowy_cywilnoprawne___liczba_pracownikow': '15',
    'ppk___ile_osob_': '20',
    'pfron___ile_osob_': '5',
    'a1___czy_wystepuja_': '3',
    'kto_robi_import_do_moje_ppk': 'Biuro',
    'srodki_trwale_i_wartosci_niematerialne_i_prawne___ile_': '5',
    'kasy_fiskalne___ile_': '3',
    'pytania_do_msp': 'Biegły rewident?;Konta zespołu 5?',
    'ilosc_kont_bankowych___raporty_dzienne': '10',
    'ilosc_kont_bankowych___raporty_miesieczne': '15',
    'ile_kanalow_platniczych_': '8',
    'ile_transakcji_sprzedazy_w_miesiacu_': '1500',
    'branza': 'E-commerce',
    'uslugi_do_wyceny': 'Księgowość;Kadry;E-commerce',
    'dodatkowe_skladniki_wynagrodzenia': '4'
  });
}

// ============================================================================
// MAIN TEST SUITE - Comprehensive Business Scenarios 
// ============================================================================
describe('Comprehensive Business Scenarios ', () => {
  const mockAccessToken = 'test-token';
  const mockDealId = '*********';

  // Mock fetch for unit tests only
  const mockFetch = vi.fn();
  global.fetch = mockFetch;

  beforeEach(() => {
    // Clear all mocks and setup console mocking
    vi.clearAllMocks();
    vi.spyOn(console, 'log').mockImplementation(() => { });
    vi.spyOn(console, 'warn').mockImplementation(() => { });
    vi.spyOn(console, 'error').mockImplementation(() => { });

    // Reset fetch mock
    mockFetch.mockClear();

    // Setup fetch mock for this test suite only
    global.fetch = mockFetch;

    // Setup standard mock implementations
    updateLineItemsForDeal.mockResolvedValue({ success: true });
    fetchPricesFromHubSpot.mockResolvedValue(createMockPrices());

    getAccountingPackagePrices.mockImplementation((isFullAccounting) => {
      if (isFullAccounting) {
        return Promise.resolve({
          packages: {
            BASE: { price: 499 },
            SILVER: { price: 764 },
            GOLD: { price: 1374 },
            PLATINUM: { price: 2000 }
          },
          individualPrices: {
            BASE: 12.9,
            SILVER: 15.3,
            GOLD: 18.7,
            PLATINUM: 22.1
          },
          additionalPackages: {
            goldPack50: { price: 100 },
            platinumPack50: { price: 120 },
            platinumPack200: { price: 400 }
          },
          prices: createMockPrices()
        });
      } else {
        // Simplified accounting pricing
        return Promise.resolve({
          packages: {
            BASE: { price: 234 },
            SILVER: { price: 294 },
            GOLD: { price: 524 },
            PLATINUM: { price: 999 }
          },
          individualPrices: {
            BASE: 12.9,
            SILVER: 15.3,
            GOLD: 18.7,
            PLATINUM: 22.1
          },
          additionalPackages: {
            goldPack50: { price: 100 },
            platinumPack50: { price: 120 },
            platinumPack200: { price: 400 }
          },
          prices: createMockPrices()
        });
      }
    });

    getPayrollPrices.mockResolvedValue({
      'BR00070': 80,
      'BR00071': 70,
      'BR00080': 50,
      'BR00165': 120,
      'BR00076': 50   // Mobility package price needed for Ryczałt calculation
    });

    getEcommercePackagePrices.mockImplementation((isFullAccounting) => {
      if (isFullAccounting) {
        return Promise.resolve({
          packages: {
            'BR00058': { price: 250, maxTransactions: 200, additionalCost: 1.25 },
            'BR00059': { price: 450, maxTransactions: 1000, additionalCost: 0.45 },
            'BR00060': { price: 1100, maxTransactions: 5000, additionalCost: 0.22 },
            'BR00061': { price: 2200, maxTransactions: 20000, additionalCost: 0.11 }
          },
          additionalSkus: {
            'BR00170': 1.25,
            'BR00171': 0.45,
            'BR00172': 0.22,
            'BR00173': 0.11
          },
          prices: {
            'BR00058': 250,
            'BR00059': 450,
            'BR00060': 1100,
            'BR00061': 2200,
            'BR00170': 1.25,
            'BR00171': 0.45,
            'BR00172': 0.22,
            'BR00173': 0.11
          }
        });
      } else {
        return Promise.resolve({
          packages: {
            'BR00090': { price: 125, maxTransactions: 200, additionalCost: 0.63 },
            'BR00091': { price: 250, maxTransactions: 1000, additionalCost: 0.25 },
            'BR00092': { price: 500, maxTransactions: 5000, additionalCost: 0.10 },
            'BR00093': { price: 1000, maxTransactions: 20000, additionalCost: 0.05 }
          },
          additionalSkus: {
            'BR00166': 0.63,
            'BR00167': 0.25,
            'BR00168': 0.10,
            'BR00169': 0.05
          },
          prices: {
            'BR00090': 125,
            'BR00091': 250,
            'BR00092': 500,
            'BR00093': 1000,
            'BR00166': 0.63,
            'BR00167': 0.25,
            'BR00168': 0.10,
            'BR00169': 0.05
          }
        });
      }
    });

    findProductBySku.mockResolvedValue({
      id: 'product123',
      properties: {
        name: 'Test Product',
        hs_sku: 'BR00013',
        price: '15'
      }
    });

    // Mock PIT package functions
    getPitPackagePrices.mockResolvedValue({
      packages: {
        BASE: { sku: 'BR00094', price: 234 },
        SILVER: { sku: 'BR00095', price: 294 },
        GOLD: { sku: 'BR00096', price: 524 },
        PLATINUM: { sku: 'BR00097', price: 684 }
      }
    });

    selectPitPackageForSimplifiedAccounting.mockImplementation((selectedPackageName) => {
      const pitMapping = {
        'BASE': [{ sku: 'BR00094', quantity: 1, description: 'PIT BASE package for simplified accounting', price: 234 }],
        'SILVER': [{ sku: 'BR00095', quantity: 1, description: 'PIT SILVER package for simplified accounting', price: 294 }],
        'GOLD': [{ sku: 'BR00096', quantity: 1, description: 'PIT GOLD package for simplified accounting', price: 524 }],
        'PLATINUM': [{ sku: 'BR00097', quantity: 1, description: 'PIT PLATINUM package for simplified accounting', price: 684 }]
      };
      return Promise.resolve(pitMapping[selectedPackageName] || []);
    });

    // Mock price fetcher functions for financial statement packages
    const mockPrices = createMockPrices();
    getMultipleProductPrices.mockResolvedValue(mockPrices);
    getProductPrice.mockImplementation((sku) => {
      return Promise.resolve(mockPrices[sku] || 0);
    });
  });

  // ============================================================================
  // REAL-WORLD BUSINESS SCENARIOS
  // ============================================================================
  describe('Real-World Business Scenarios with Comprehensive Updates', () => {
    test('Scenario 1: Small business with comprehensive data update', async () => {
      const mockProperties = createSmallBusinessScenario();
      getDealProperties.mockResolvedValue(mockProperties);

      const result = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      // Verify the result structure
      expect(result).toHaveProperty('br00013Quantity');
      expect(result).toHaveProperty('baseDocumentQuantity');
      expect(result).toHaveProperty('selectedPackageName');
      expect(result).toHaveProperty('completeRecalculationPerformed');

      // Verify comprehensive recalculation was performed
      expect(result.completeRecalculationPerformed).toBe(true);
      expect(result.financialStatementRefreshError).toBe(null);
      expect(result.pitRefreshError).toBe(null);

      // Small business should use BASE package
      expect(result.selectedPackageName).toBe('BASE');
      expect(result.br00013Quantity).toBe(Math.max(12, 5)); // max(document_sum, BASE_minimum)
      expect(result.baseDocumentQuantity).toBe(12); // 7+3+1+1 = 12

      // COMPREHENSIVE LINE ITEM EXPECTATIONS - All line items that should be added based on configuration

      // Accounting package line items (BASE package for full accounting)
      expect(result.selectedPackageName).toBe('BASE');
      expect(result.isFullAccounting).toBe(true);

      // BR00013 line items (banking operations)
      expect(result.br00013Quantity).toBe(12); // Document sum = 12, BASE minimum = 5, so 12

      // VAT line items (empty VAT status = no VAT line items)
      expect(result.shouldHaveBR00032).toBe(false); // No VAT EU/8/9M
      expect(result.shouldHaveBR00033).toBe(false); // No VAT OSS
      expect(result.br00032Quantity).toBe(0);
      expect(result.br00033Quantity).toBe(0);

      // Language line items (Polish = no BR00129)
      expect(result.shouldHaveBR00129).toBe(false); // Polish language

      // Payroll line items (no payroll package = no payroll items)
      expect(result.br00069Quantity).toBe(0);
      expect(result.br00070Quantity).toBe(0);
      expect(result.br00071Quantity).toBe(0);
      expect(result.br00072Quantity).toBe(0);
      expect(result.br00073Quantity).toBe(0);
      expect(result.br00074Quantity).toBe(0);
      expect(result.br00075Quantity).toBe(0);
      expect(result.br00077Quantity).toBe(0);
      expect(result.br00080Quantity).toBe(0);
      expect(result.shouldHaveBR00078).toBe(false);
      expect(result.shouldHaveBR00079).toBe(false);

      // Quantity-based line items (all 0 in small business scenario)
      expect(result.br00030Quantity).toBe(0); // Fixed assets
      expect(result.br00031Quantity).toBe(0); // Cash registers
      expect(result.br00165Quantity).toBe(0); // A1 certificates

      // MSP line items (no MSP questions = no BR00111)
      expect(result.shouldHaveBR00111).toBe(false);

      // E-commerce line items (not E-commerce industry = no e-commerce items)
      expect(result.ecommerceRefreshed).toBe(false);
      expect(result.ecommerceTransactionCount).toBe(0);

      // Banking line items (full accounting requires minimum 1 monthly report)
      expect(result.br00012Quantity).toBe(1); // Full accounting: min(0,1) + 0 payment channels = 1
      expect(result.br00130Quantity).toBe(0); // Daily reports = 0

      console.log('Small business comprehensive update result:', {
        br00013Quantity: result.br00013Quantity,
        baseDocumentQuantity: result.baseDocumentQuantity,
        selectedPackageName: result.selectedPackageName,
        isFullAccounting: result.isFullAccounting,
        shouldHaveBR00032: result.shouldHaveBR00032,
        shouldHaveBR00033: result.shouldHaveBR00033,
        shouldHaveBR00129: result.shouldHaveBR00129
      });
    });

    test('Scenario 2: Medium business with comprehensive data update', async () => {
      const mockProperties = createMediumBusinessScenario();
      getDealProperties.mockResolvedValue(mockProperties);

      const result = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      // Verify the result structure and that it contains real data
      expect(result).toHaveProperty('completeRecalculationPerformed');
      expect(result.completeRecalculationPerformed).toBe(true);
      expect(result.financialStatementRefreshError).toBe(null);
      expect(result.pitRefreshError).toBe(null);

      // Verify that all business logic areas were processed
      expect(result).toHaveProperty('br00013Quantity');
      expect(result).toHaveProperty('isFullAccounting');

      // VAT settings should be defined (even if false)
      expect(result).toHaveProperty('shouldHaveBR00032');
      expect(result).toHaveProperty('shouldHaveBR00033');

      // Language settings should be defined
      expect(result).toHaveProperty('shouldHaveBR00129');

      // COMPREHENSIVE LINE ITEM EXPECTATIONS - All line items that should be added based on configuration

      // Accounting package line items (SILVER package for full accounting)
      expect(result.selectedPackageName).toBe('SILVER');
      expect(result.isFullAccounting).toBe(true);

      // New BR00013 calculation logic: max of Sum A (invoices+employees) or Sum B (cash-bank)
      // Sum A: 23 + 15 + 3 + 5 + 5 + 2 = 53 (employees included in calculation)
      // Sum B: 12 + 6 + 8 = 26
      // BR00013 document quantity = max(53, 26) = 53
      // With SILVER package minimum (50), BR00013 quantity = max(53, 50) = 53
      expect(result.br00013Quantity).toBe(53);
      expect(result.baseDocumentQuantity).toBe(46); // Bookings only for package selection: 23+15+3+5 = 46

      // VAT line items (VAT EU = BR00032)
      expect(result.shouldHaveBR00032).toBe(true); // VAT EU
      expect(result.shouldHaveBR00033).toBe(false); // No VAT OSS
      expect(result.br00032Quantity).toBe(1);
      expect(result.br00033Quantity).toBe(0);

      // Language line items (Angielski = BR00129)
      expect(result.shouldHaveBR00129).toBe(true); // Non-Polish language

      // Payroll line items (Płace package with employees)
      expect(result.br00069Quantity).toBe(0); // Not Ryczałt package
      expect(result.br00070Quantity).toBe(0); // Not PREMIUM package
      expect(result.br00071Quantity).toBe(0); // Not PREMIUM package
      expect(result.br00072Quantity).toBe(5); // Employment contracts for Płace
      expect(result.br00073Quantity).toBe(2); // Civil contracts for Płace
      expect(result.br00074Quantity).toBe(0); // Not z teczkami package
      expect(result.br00075Quantity).toBe(0); // No BR00075 in Płace
      expect(result.br00077Quantity).toBe(3); // PPK persons
      expect(result.br00080Quantity).toBe(1); // PFRON persons
      expect(result.shouldHaveBR00078).toBe(true); // PPK presence
      expect(result.shouldHaveBR00079).toBe(true); // Biuro import

      // Quantity-based line items
      expect(result.br00030Quantity).toBe(0); // Fixed assets = 0
      expect(result.br00031Quantity).toBe(0); // Cash registers = 0
      expect(result.br00165Quantity).toBe(1); // A1 certificates = 1

      // MSP line items (no MSP questions = no BR00111)
      expect(result.shouldHaveBR00111).toBe(false);

      // E-commerce line items (not E-commerce industry = no e-commerce items)
      expect(result.ecommerceRefreshed).toBe(false);
      expect(result.ecommerceTransactionCount).toBe(0);

      // Banking line items (full accounting requires minimum 1 monthly report)
      expect(result.br00012Quantity).toBe(1); // Full accounting: min(0,1) + 0 payment channels = 1
      expect(result.br00130Quantity).toBe(0); // Daily reports = 0

      console.log('Medium business comprehensive update result:', {
        completeRecalculationPerformed: result.completeRecalculationPerformed,
        br00013Quantity: result.br00013Quantity,
        isFullAccounting: result.isFullAccounting,
        vatSettings: {
          shouldHaveBR00032: result.shouldHaveBR00032,
          shouldHaveBR00033: result.shouldHaveBR00033
        },
        languageSettings: {
          shouldHaveBR00129: result.shouldHaveBR00129
        }
      });
    });

    test('Scenario 3: Large business with comprehensive data update', async () => {
      const mockProperties = createLargeBusinessScenario();
      getDealProperties.mockResolvedValue(mockProperties);

      const result = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      // Verify the result structure
      expect(result).toHaveProperty('br00013Quantity');
      expect(result).toHaveProperty('baseDocumentQuantity');
      expect(result).toHaveProperty('completeRecalculationPerformed');

      // Verify comprehensive recalculation was performed
      expect(result.completeRecalculationPerformed).toBe(true);

      // COMPREHENSIVE LINE ITEM EXPECTATIONS - All line items that should be added based on configuration

      // Accounting package line items (PLATINUM package for full accounting)
      expect(result.selectedPackageName).toBe('PLATINUM');
      expect(result.isFullAccounting).toBe(true);

      // New BR00013 calculation logic: max of Sum A (invoices+employees) or Sum B (cash-bank)
      // Sum A: 105 + 67 + 12 + 8 + 15 + 8 = 215 (employees included in calculation)
      // Sum B: 45 + 15 + 25 = 85
      // BR00013 document quantity = max(215, 85) = 215
      // With PLATINUM package minimum (200), BR00013 quantity = max(215, 200) = 215
      expect(result.br00013Quantity).toBe(215);
      expect(result.baseDocumentQuantity).toBe(192); // Bookings only for package selection: 105+67+12+8 = 192

      // VAT line items (VAT EU;VAT OSS = both BR00032 and BR00033)
      expect(result.shouldHaveBR00032).toBe(true); // VAT EU
      expect(result.shouldHaveBR00033).toBe(true); // VAT OSS
      expect(result.br00032Quantity).toBe(1); // VAT EU
      expect(result.br00033Quantity).toBe(1); // VAT OSS

      // Language line items (Polish = no BR00129)
      expect(result.shouldHaveBR00129).toBe(false); // Polish language

      // Payroll line items (Kadry i płace PREMIUM package with employees)
      expect(result.br00069Quantity).toBe(0); // Not Ryczałt package
      expect(result.br00070Quantity).toBe(15); // Employment contracts for PREMIUM
      expect(result.br00071Quantity).toBe(8); // Civil contracts for PREMIUM
      expect(result.br00072Quantity).toBe(0); // Not Płace package
      expect(result.br00073Quantity).toBe(0); // Not Płace package
      expect(result.br00074Quantity).toBe(0); // Not z teczkami package
      expect(result.br00075Quantity).toBe(0); // No BR00075 in PREMIUM
      expect(result.br00077Quantity).toBe(0); // PPK not added for PREMIUM
      expect(result.br00080Quantity).toBe(3); // PFRON persons
      expect(result.shouldHaveBR00078).toBe(false); // No PPK for PREMIUM
      expect(result.shouldHaveBR00079).toBe(false); // No Biuro import for PREMIUM

      // Quantity-based line items
      expect(result.br00030Quantity).toBe(2); // Fixed assets = 2
      expect(result.br00031Quantity).toBe(1); // Cash registers = 1
      expect(result.br00165Quantity).toBe(2); // A1 certificates = 2

      // MSP line items (no MSP questions = no BR00111)
      expect(result.shouldHaveBR00111).toBe(false);

      // E-commerce line items (not E-commerce industry = no e-commerce items)
      expect(result.ecommerceRefreshed).toBe(false);
      expect(result.ecommerceTransactionCount).toBe(0);

      // Banking line items (full accounting requires minimum 1 monthly report)
      expect(result.br00012Quantity).toBe(1); // Full accounting: min(0,1) + 0 payment channels = 1
      expect(result.br00130Quantity).toBe(0); // Daily reports = 0

      console.log('Large business comprehensive update result:', {
        br00013Quantity: result.br00013Quantity,
        baseDocumentQuantity: result.baseDocumentQuantity,
        isFullAccounting: result.isFullAccounting,
        completeRecalculationPerformed: result.completeRecalculationPerformed
      });
    });

    test('Scenario 4: Enterprise with comprehensive data update', async () => {
      const mockProperties = createEnterpriseScenario();
      getDealProperties.mockResolvedValue(mockProperties);

      const result = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      // Verify the result structure
      expect(result).toHaveProperty('br00013Quantity');
      expect(result).toHaveProperty('baseDocumentQuantity');
      expect(result).toHaveProperty('completeRecalculationPerformed');

      // Verify comprehensive recalculation was performed
      expect(result.completeRecalculationPerformed).toBe(true);
      expect(result.financialStatementRefreshError).toBe(null);
      expect(result.pitRefreshError).toBe(null);

      // COMPREHENSIVE LINE ITEM EXPECTATIONS - All line items that should be added based on configuration

      // Accounting package line items (PLATINUM package for full accounting)
      expect(result.selectedPackageName).toBe('PLATINUM');
      expect(result.isFullAccounting).toBe(true);

      // New BR00013 calculation logic: max of Sum A (invoices+employees) or Sum B (cash-bank)
      // Sum A: 250 + 180 + 25 + 15 + 25 + 15 = 510 (employees included in calculation)
      // Sum B: 80 + 25 + 50 = 155
      // BR00013 document quantity = max(510, 155) = 510
      // With PLATINUM package minimum (200), BR00013 quantity = max(510, 200) = 510
      expect(result.br00013Quantity).toBe(510);
      expect(result.baseDocumentQuantity).toBe(470); // Bookings only for package selection: 250+180+25+15 = 470

      // VAT line items (VAT EU;VAT OSS;VAT 8;VAT 9M = multiple BR00032 + BR00033)
      expect(result.shouldHaveBR00032).toBe(true); // VAT EU + VAT 8 + VAT 9M
      expect(result.shouldHaveBR00033).toBe(true); // VAT OSS
      expect(result.br00032Quantity).toBe(3); // VAT EU + VAT 8 + VAT 9M = 3
      expect(result.br00033Quantity).toBe(1); // VAT OSS = 1

      // Language line items (Niemiecki = BR00129)
      expect(result.shouldHaveBR00129).toBe(true); // German language

      // Payroll line items (Ryczałt package with employees)
      expect(result.br00069Quantity).toBe(1); // Always quantity 1 for Ryczałt
      expect(result.br00069OverridePrice).toBeGreaterThan(0); // Should have custom price calculation
      expect(result.br00070Quantity).toBe(0); // Removed for Ryczałt
      expect(result.br00071Quantity).toBe(0); // Removed for Ryczałt
      expect(result.br00072Quantity).toBe(0); // Removed for Ryczałt
      expect(result.br00073Quantity).toBe(0); // Removed for Ryczałt
      expect(result.br00074Quantity).toBe(0); // Not z teczkami package
      expect(result.br00075Quantity).toBe(0); // Removed for Ryczałt
      expect(result.br00077Quantity).toBe(0); // PPK persons removed for Ryczałt (included in BR00069)
      expect(result.br00080Quantity).toBe(0); // PFRON removed for Ryczałt (included in BR00069)
      expect(result.shouldHaveBR00078).toBe(false); // PPK presence removed for Ryczałt (included in BR00069)
      expect(result.shouldHaveBR00079).toBe(false); // Biuro import removed for Ryczałt (included in BR00069)

      // Quantity-based line items
      expect(result.br00030Quantity).toBe(5); // Fixed assets = 5
      expect(result.br00031Quantity).toBe(3); // Cash registers = 3
      expect(result.br00165Quantity).toBe(0); // A1 certificates removed for Ryczałt (included in BR00069)

      // MSP line items (Biegły rewident = BR00111)
      expect(result.shouldHaveBR00111).toBe(true); // MSP audit question

      // E-commerce line items (E-commerce industry with 1500 transactions)
      // Note: E-commerce processing depends on transaction count > 0 and proper pricing setup
      if (result.ecommerceTransactionCount > 0) {
        expect(result.ecommerceRefreshed).toBe(true);
        expect(result.ecommerceTransactionCount).toBe(1500);
      } else {
        // If no transactions or pricing issues, e-commerce won't be processed
        expect(result.ecommerceRefreshed).toBe(false);
        expect(result.ecommerceTransactionCount).toBe(0);
      }

      // Banking line items (daily reports and monthly reports/payment channels)
      expect(result.br00012Quantity).toBe(23); // Full accounting: monthly reports(15) + payment channels(8) = 23
      expect(result.br00130Quantity).toBe(10); // Daily bank reports = 10

      console.log('Enterprise comprehensive update result:', {
        br00013Quantity: result.br00013Quantity,
        baseDocumentQuantity: result.baseDocumentQuantity,
        isFullAccounting: result.isFullAccounting,
        completeRecalculationPerformed: result.completeRecalculationPerformed
      });
    });
  });

  // ============================================================================
  // COMPREHENSIVE UPDATE TESTING - Core functionality validation
  // ============================================================================
  describe('Comprehensive Update Testing', () => {
    test('Should perform comprehensive recalculation with mock data', async () => {
      const mockProperties = createMediumBusinessScenario();
      getDealProperties.mockResolvedValue(mockProperties);

      const result = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      expect(result.completeRecalculationPerformed).toBe(true);
      expect(result.financialStatementRefreshError).toBe(null);
      expect(result.pitRefreshError).toBe(null);
      
      // Verify all business logic areas are processed with comprehensive expectations
      expect(result).toHaveProperty('br00013Quantity');
      expect(result).toHaveProperty('isFullAccounting');
      expect(result).toHaveProperty('shouldHaveBR00032');
      expect(result).toHaveProperty('shouldHaveBR00033');
      expect(result).toHaveProperty('shouldHaveBR00129');

      // COMPREHENSIVE VERIFICATION - All line item categories should be defined

      // Accounting package properties
      expect(result).toHaveProperty('selectedPackageName');
      expect(result).toHaveProperty('baseDocumentQuantity');

      // VAT line item properties
      expect(result).toHaveProperty('br00032Quantity');
      expect(result).toHaveProperty('br00033Quantity');

      // Payroll line item properties
      expect(result).toHaveProperty('br00069Quantity');
      expect(result).toHaveProperty('br00070Quantity');
      expect(result).toHaveProperty('br00071Quantity');
      expect(result).toHaveProperty('br00072Quantity');
      expect(result).toHaveProperty('br00073Quantity');
      expect(result).toHaveProperty('br00074Quantity');
      expect(result).toHaveProperty('br00075Quantity');
      expect(result).toHaveProperty('br00077Quantity');
      expect(result).toHaveProperty('br00080Quantity');
      expect(result).toHaveProperty('shouldHaveBR00078');
      expect(result).toHaveProperty('shouldHaveBR00079');

      // Quantity-based line item properties
      expect(result).toHaveProperty('br00030Quantity');
      expect(result).toHaveProperty('br00031Quantity');
      expect(result).toHaveProperty('br00165Quantity');

      // MSP line item properties
      expect(result).toHaveProperty('shouldHaveBR00111');

      // Banking line item properties (if applicable)
      expect(result).toHaveProperty('br00012Quantity');
      expect(result).toHaveProperty('br00130Quantity');

      // E-commerce properties (if applicable)
      expect(result).toHaveProperty('ecommerceRefreshed');
      expect(result).toHaveProperty('ecommerceTransactionCount');

      console.log('Comprehensive update result:', {
        completeRecalculationPerformed: result.completeRecalculationPerformed,
        br00013Quantity: result.br00013Quantity,
        isFullAccounting: result.isFullAccounting,
        vatSettings: {
          shouldHaveBR00032: result.shouldHaveBR00032,
          shouldHaveBR00033: result.shouldHaveBR00033
        },
        languageSettings: {
          shouldHaveBR00129: result.shouldHaveBR00129
        }
      });
    });

    test('Should handle comprehensive update consistently', async () => {
      const mockProperties = createSmallBusinessScenario();
      getDealProperties.mockResolvedValue(mockProperties);

      // Run the same comprehensive update multiple times to ensure consistency
      const result1 = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      const result2 = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      // Results should be consistent across ALL line item categories
      expect(result1.completeRecalculationPerformed).toBe(result2.completeRecalculationPerformed);
      expect(result1.isFullAccounting).toBe(result2.isFullAccounting);

      // Accounting package consistency
      expect(result1.selectedPackageName).toBe(result2.selectedPackageName);
      expect(result1.br00013Quantity).toBe(result2.br00013Quantity);
      expect(result1.baseDocumentQuantity).toBe(result2.baseDocumentQuantity);

      // VAT line item consistency
      expect(result1.shouldHaveBR00032).toBe(result2.shouldHaveBR00032);
      expect(result1.shouldHaveBR00033).toBe(result2.shouldHaveBR00033);
      expect(result1.br00032Quantity).toBe(result2.br00032Quantity);
      expect(result1.br00033Quantity).toBe(result2.br00033Quantity);

      // Language line item consistency
      expect(result1.shouldHaveBR00129).toBe(result2.shouldHaveBR00129);

      // Payroll line item consistency
      expect(result1.br00069Quantity).toBe(result2.br00069Quantity);
      expect(result1.br00070Quantity).toBe(result2.br00070Quantity);
      expect(result1.br00071Quantity).toBe(result2.br00071Quantity);
      expect(result1.br00072Quantity).toBe(result2.br00072Quantity);
      expect(result1.br00073Quantity).toBe(result2.br00073Quantity);
      expect(result1.br00074Quantity).toBe(result2.br00074Quantity);
      expect(result1.br00075Quantity).toBe(result2.br00075Quantity);
      expect(result1.br00077Quantity).toBe(result2.br00077Quantity);
      expect(result1.br00080Quantity).toBe(result2.br00080Quantity);
      expect(result1.shouldHaveBR00078).toBe(result2.shouldHaveBR00078);
      expect(result1.shouldHaveBR00079).toBe(result2.shouldHaveBR00079);

      // Quantity-based line item consistency
      expect(result1.br00030Quantity).toBe(result2.br00030Quantity);
      expect(result1.br00031Quantity).toBe(result2.br00031Quantity);
      expect(result1.br00165Quantity).toBe(result2.br00165Quantity);

      // MSP line item consistency
      expect(result1.shouldHaveBR00111).toBe(result2.shouldHaveBR00111);

      // Banking line item consistency
      expect(result1.br00012Quantity).toBe(result2.br00012Quantity);
      expect(result1.br00130Quantity).toBe(result2.br00130Quantity);

      // E-commerce consistency
      expect(result1.ecommerceRefreshed).toBe(result2.ecommerceRefreshed);
      expect(result1.ecommerceTransactionCount).toBe(result2.ecommerceTransactionCount);

      console.log('Consistency test passed - both results match across all line item categories');
    });
  });

  // ============================================================================
  // PIT PACKAGE TESTING - Personal Income Tax packages for simplified accounting
  // ============================================================================
  describe('PIT Package Testing', () => {
    test('Should add correct PIT package for simplified accounting BASE', async () => {
      const mockProperties = createMockDealProperties({
        'faktury_rachunki_sprzedazowe___ile_': '3',
        'faktury_rachunki_zakupu___ile_': '2',
        'rodzaj_ksiegowosci': 'Księgowość uproszczona', // Simplified accounting
        'uslugi_do_wyceny': 'Księgowość'
      });
      getDealProperties.mockResolvedValue(mockProperties);

      const result = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      // Should select BASE package for simplified accounting
      expect(result.selectedPackageName).toBe('BASE');
      expect(result.isFullAccounting).toBe(false);

      // Should have PIT packages for simplified accounting
      expect(result.pitRefreshed).toBe(true);
      expect(result.pitPackages).toBeDefined();
      expect(result.pitPackages.length).toBe(1);
      expect(result.pitPackages[0].sku).toBe('BR00094'); // BASE PIT package
      expect(result.pitPackages[0].quantity).toBe(1);
      expect(result.selectedPitPackage).toBe('BASE');

      console.log('PIT BASE package test result:', {
        selectedPackageName: result.selectedPackageName,
        isFullAccounting: result.isFullAccounting,
        pitRefreshed: result.pitRefreshed,
        selectedPitPackage: result.selectedPitPackage,
        pitPackages: result.pitPackages
      });
    });

    test('Should add correct PIT package for simplified accounting SILVER', async () => {
      const mockProperties = createMockDealProperties({
        'faktury_rachunki_sprzedazowe___ile_': '15',
        'faktury_rachunki_zakupu___ile_': '10',
        'rodzaj_ksiegowosci': 'Księgowość uproszczona', // Simplified accounting
        'uslugi_do_wyceny': 'Księgowość'
      });
      getDealProperties.mockResolvedValue(mockProperties);

      const result = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      // Should select SILVER package for simplified accounting (25 documents > 20 SILVER minimum, but not triggering 95% rule)
      expect(result.selectedPackageName).toBe('SILVER');
      expect(result.isFullAccounting).toBe(false);

      // Should have PIT packages for simplified accounting
      expect(result.pitRefreshed).toBe(true);
      expect(result.pitPackages).toBeDefined();
      expect(result.pitPackages.length).toBe(1);
      expect(result.pitPackages[0].sku).toBe('BR00095'); // SILVER PIT package
      expect(result.pitPackages[0].quantity).toBe(1);
      expect(result.selectedPitPackage).toBe('SILVER');

      console.log('PIT SILVER package test result:', {
        selectedPackageName: result.selectedPackageName,
        isFullAccounting: result.isFullAccounting,
        pitRefreshed: result.pitRefreshed,
        selectedPitPackage: result.selectedPitPackage,
        pitPackages: result.pitPackages
      });
    });

    test('Should add correct PIT package for simplified accounting GOLD', async () => {
      const mockProperties = createMockDealProperties({
        'faktury_rachunki_sprzedazowe___ile_': '70',
        'faktury_rachunki_zakupu___ile_': '20',
        'rodzaj_ksiegowosci': 'Księgowość uproszczona', // Simplified accounting
        'uslugi_do_wyceny': 'Księgowość'
      });
      getDealProperties.mockResolvedValue(mockProperties);

      const result = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      // Should select GOLD package for simplified accounting (90 documents > 80 GOLD minimum)
      expect(result.selectedPackageName).toBe('GOLD');
      expect(result.isFullAccounting).toBe(false);

      // Should have PIT packages for simplified accounting
      expect(result.pitRefreshed).toBe(true);
      expect(result.pitPackages).toBeDefined();
      expect(result.pitPackages.length).toBe(1);
      expect(result.pitPackages[0].sku).toBe('BR00096'); // GOLD PIT package
      expect(result.pitPackages[0].quantity).toBe(1);
      expect(result.selectedPitPackage).toBe('GOLD');

      console.log('PIT GOLD package test result:', {
        selectedPackageName: result.selectedPackageName,
        isFullAccounting: result.isFullAccounting,
        pitRefreshed: result.pitRefreshed,
        selectedPitPackage: result.selectedPitPackage,
        pitPackages: result.pitPackages
      });
    });

    test('Should add correct PIT package for simplified accounting PLATINUM', async () => {
      const mockProperties = createMockDealProperties({
        'faktury_rachunki_sprzedazowe___ile_': '130',
        'faktury_rachunki_zakupu___ile_': '70',
        'rodzaj_ksiegowosci': 'Księgowość uproszczona', // Simplified accounting
        'uslugi_do_wyceny': 'Księgowość'
      });
      getDealProperties.mockResolvedValue(mockProperties);

      const result = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      // Should select PLATINUM package for simplified accounting (200 documents > 150 PLATINUM minimum)
      expect(result.selectedPackageName).toBe('PLATINUM');
      expect(result.isFullAccounting).toBe(false);

      // Should have PIT packages for simplified accounting
      expect(result.pitRefreshed).toBe(true);
      expect(result.pitPackages).toBeDefined();
      expect(result.pitPackages.length).toBe(1);
      expect(result.pitPackages[0].sku).toBe('BR00097'); // PLATINUM PIT package
      expect(result.pitPackages[0].quantity).toBe(1);
      expect(result.selectedPitPackage).toBe('PLATINUM');

      console.log('PIT PLATINUM package test result:', {
        selectedPackageName: result.selectedPackageName,
        isFullAccounting: result.isFullAccounting,
        pitRefreshed: result.pitRefreshed,
        selectedPitPackage: result.selectedPitPackage,
        pitPackages: result.pitPackages
      });
    });

    test('Should NOT add PIT packages for full accounting', async () => {
      const mockProperties = createMockDealProperties({
        'faktury_rachunki_sprzedazowe___ile_': '35',
        'faktury_rachunki_zakupu___ile_': '25',
        'rodzaj_ksiegowosci': 'Pełna księgowość', // Full accounting
        'uslugi_do_wyceny': 'Księgowość'
      });
      getDealProperties.mockResolvedValue(mockProperties);

      const result = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      // Should select SILVER package for full accounting
      expect(result.selectedPackageName).toBe('SILVER');
      expect(result.isFullAccounting).toBe(true);

      // Should NOT have PIT packages for full accounting
      expect(result.pitRefreshed).toBe(false);
      expect(result.pitPackages).toEqual([]);
      expect(result.selectedPitPackage).toBeNull();

      console.log('PIT full accounting test result:', {
        selectedPackageName: result.selectedPackageName,
        isFullAccounting: result.isFullAccounting,
        pitRefreshed: result.pitRefreshed,
        pitPackages: result.pitPackages
      });
    });

    test('Should handle PIT package mapping consistency', async () => {
      // Test all package levels to ensure consistent mapping
      const testCases = [
        { documents: 3, expectedPackage: 'BASE', expectedPitSku: 'BR00094' },
        { documents: 25, expectedPackage: 'SILVER', expectedPitSku: 'BR00095' },
        { documents: 90, expectedPackage: 'GOLD', expectedPitSku: 'BR00096' },
        { documents: 200, expectedPackage: 'PLATINUM', expectedPitSku: 'BR00097' }
      ];

      for (const testCase of testCases) {
        const mockProperties = createMockDealProperties({
          'faktury_rachunki_sprzedazowe___ile_': testCase.documents.toString(),
          'faktury_rachunki_zakupu___ile_': '0',
          'rodzaj_ksiegowosci': 'Księgowość uproszczona',
          'uslugi_do_wyceny': 'Księgowość'
        });
        getDealProperties.mockResolvedValue(mockProperties);

        const result = await handleComprehensiveUpdate(
          'aktualizuj_dane',
          'true',
          mockDealId,
          mockAccessToken
        );

        expect(result.selectedPackageName).toBe(testCase.expectedPackage);
        expect(result.pitPackages[0].sku).toBe(testCase.expectedPitSku);
        expect(result.selectedPitPackage).toBe(testCase.expectedPackage);

        console.log(`PIT mapping test for ${testCase.documents} documents:`, {
          expectedPackage: testCase.expectedPackage,
          actualPackage: result.selectedPackageName,
          expectedPitSku: testCase.expectedPitSku,
          actualPitSku: result.pitPackages[0].sku
        });
      }
    });
  });

  // ============================================================================
  // NEW FUNCTIONALITY INTEGRATION TESTS - Testing combined new features
  // ============================================================================
  describe('New Functionality Integration Tests', () => {
    test('Should integrate BR00013 employee addition with kadry and full accounting', async () => {
      const mockProperties = createMockDealProperties({
        'rodzaj_ksiegowosci': 'Pełna księgowość',
        'uslugi_do_wyceny': 'Księgowość;Kadry',
        'faktury_rachunki_sprzedazowe___ile_': '20',
        'faktury_rachunki_zakupu___ile_': '15',
        'umowa_o_prace___liczba_osob': '12',
        'umowy_cywilnoprawne___liczba_pracownikow': '8',
        'pakiet_kadrowo_placowy': 'Płace',
        'dodatkowe_skladniki_wynagrodzenia': '5',
        'ilosc_kont_bankowych___raporty_miesieczne': '2',
        'ile_kanalow_platniczych_': '3'
      });

      getDealProperties.mockResolvedValue(mockProperties);

      const result = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      // New BR00013 calculation logic: max of Sum A (invoices+employees) or Sum B (cash-bank)
      // Sum A: 20 + 15 + 0 + 0 + 12 + 8 = 55 (employees included in calculation)
      // Sum B: 0 + 0 + 0 = 0
      // BR00013 document quantity = max(55, 0) = 55
      // With SILVER package minimum (50), BR00013 quantity = max(55, 50) = 55
      expect(result.br00013Quantity).toBe(55);

      // BR00075 should be set from dodatkowe_skladniki_wynagrodzenia for Płace package
      expect(result.br00075Quantity).toBe(5);

      // BR00117 should be true (12 employment contracts >= 10)
      expect(result.shouldHaveBR00117).toBe(true);

      // BR00119 and BR00081 should be false (12 employment contracts < 25)
      expect(result.shouldHaveBR00119).toBe(false);
      expect(result.shouldHaveBR00081).toBe(false);

      // BR00012 should sum monthly reports + payment channels in full accounting
      expect(result.br00012Quantity).toBe(5); // 2 + 3
    });

    test('Should handle simplified accounting with kadry correctly', async () => {
      const mockProperties = createMockDealProperties({
        'rodzaj_ksiegowosci': 'Uproszczona księgowość',
        'uslugi_do_wyceny': 'Księgowość;Kadry',
        'faktury_rachunki_sprzedazowe___ile_': '20',
        'faktury_rachunki_zakupu___ile_': '15',
        'umowa_o_prace___liczba_osob': '30',
        'umowy_cywilnoprawne___liczba_pracownikow': '5',
        'pakiet_kadrowo_placowy': 'Kadry i płace - z teczkami',
        'dodatkowe_skladniki_wynagrodzenia': '8',
        'ilosc_kont_bankowych___raporty_miesieczne': '4',
        'ile_kanalow_platniczych_': '6'
      });

      getDealProperties.mockResolvedValue(mockProperties);

      const result = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      // BR00013 should NOT be added for simplified accounting ("Księgowość uproszczona")
      // Only full accounting types get BR00013: "Pełna księgowość", "Fundacje rodzinne", "Fundacje non profit i NGO"
      expect(result.br00013Quantity).toBe(0);

      // BR00075 should be set from dodatkowe_skladniki_wynagrodzenia for Kadry i płace - z teczkami
      expect(result.br00075Quantity).toBe(8);

      // BR00117 should be true (30 employment contracts >= 10)
      expect(result.shouldHaveBR00117).toBe(true);

      // BR00119 and BR00081 should be true (30 employment contracts >= 25)
      expect(result.shouldHaveBR00119).toBe(true);
      expect(result.shouldHaveBR00081).toBe(true);

      // BR00012 should NOT be added for simplified accounting ("Księgowość uproszczona")
      // Only full accounting types get BR00012: "Pełna księgowość", "Fundacje rodzinne", "Fundacje non profit i NGO"
      expect(result.br00012Quantity).toBe(0);
    });

    test('Should handle MSP scenario with new functionality', async () => {
      const mockProperties = createMockDealProperties({
        'rodzaj_ksiegowosci': 'Pełna księgowość',
        'uslugi_do_wyceny': 'Księgowość;Kadry',
        'pytania_do_msp': 'Konta zespołu 5?',
        'faktury_rachunki_sprzedazowe___ile_': '25',
        'faktury_rachunki_zakupu___ile_': '20',
        'umowa_o_prace___liczba_osob': '15',
        'umowy_cywilnoprawne___liczba_pracownikow': '10',
        'pakiet_kadrowo_placowy': 'Płace',
        'dodatkowe_skladniki_wynagrodzenia': '3',
        'ilosc_kont_bankowych___raporty_miesieczne': '0', // Should be set to min 1
        'ile_kanalow_platniczych_': '2'
      });

      getDealProperties.mockResolvedValue(mockProperties);

      const result = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      // New BR00013 calculation logic with MSP: max of Sum A (invoices+employees) or Sum B (cash-bank)
      // Sum A: 25 + 20 + 0 + 0 + 15 + 10 = 70 (employees included in calculation)
      // Sum B: 0 + 0 + 0 = 0
      // BR00013 document quantity = max(70, 0) = 70
      // MSP markup for package selection: 45 (bookings only) * 1.5 = 68 → SILVER package
      // With SILVER package minimum (50), BR00013 quantity = max(70, 50) = 70
      // Note: MSP uses base value for BR00013 calculation, not the markup
      expect(result.br00013Quantity).toBe(70);

      // MSP audit should NOT be enabled (only 'Biegły rewident?' enables BR00111, not 'Konta zespołu 5?')
      expect(result.shouldHaveBR00111).toBe(false);

      // BR00075 should be set for Płace package
      expect(result.br00075Quantity).toBe(3);

      // BR00117 should be true (15 employment contracts >= 10)
      expect(result.shouldHaveBR00117).toBe(true);

      // BR00119 and BR00081 should be false (15 employment contracts < 25)
      expect(result.shouldHaveBR00119).toBe(false);
      expect(result.shouldHaveBR00081).toBe(false);

      // BR00012 should ensure monthly reports is at least 1 and sum with payment channels
      expect(result.br00012Quantity).toBe(3); // max(0, 1) + 2 = 3
    });

    test('Should handle premium payroll package correctly (no BR00075)', async () => {
      const mockProperties = createMockDealProperties({
        'rodzaj_ksiegowosci': 'Pełna księgowość',
        'uslugi_do_wyceny': 'Księgowość;Kadry',
        'faktury_rachunki_sprzedazowe___ile_': '10',
        'faktury_rachunki_zakupu___ile_': '5',
        'umowa_o_prace___liczba_osob': '8',
        'umowy_cywilnoprawne___liczba_pracownikow': '3',
        'pakiet_kadrowo_placowy': 'Kadry i płace PREMIUM',
        'dodatkowe_skladniki_wynagrodzenia': '10', // Should be ignored for PREMIUM
        'ilosc_kont_bankowych___raporty_miesieczne': '1',
        'ile_kanalow_platniczych_': '1'
      });

      getDealProperties.mockResolvedValue(mockProperties);

      const result = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      // New BR00013 calculation logic: max of Sum A (invoices+employees) or Sum B (cash-bank)
      // Sum A: 10 + 5 + 0 + 0 + 8 + 3 = 26 (employees included in calculation)
      // Sum B: 0 + 0 + 0 = 0
      // BR00013 document quantity = max(26, 0) = 26
      // With BASE package minimum (5), BR00013 quantity = max(26, 5) = 26
      expect(result.br00013Quantity).toBe(26);

      // BR00075 should NOT be set for PREMIUM package
      expect(result.br00075Quantity).toBe(0);

      // BR00070 and BR00071 should be set for PREMIUM package
      expect(result.br00070Quantity).toBe(8);
      expect(result.br00071Quantity).toBe(3);

      // BR00117 should be false (8 employment contracts < 10)
      expect(result.shouldHaveBR00117).toBe(false);

      // BR00119 and BR00081 should be false (8 employment contracts < 25)
      expect(result.shouldHaveBR00119).toBe(false);
      expect(result.shouldHaveBR00081).toBe(false);

      // BR00012 should sum monthly reports + payment channels
      expect(result.br00012Quantity).toBe(2); // 1 + 1
    });

    test('Should handle boundary conditions for employee thresholds', async () => {
      const mockProperties = createMockDealProperties({
        'rodzaj_ksiegowosci': 'Pełna księgowość',
        'uslugi_do_wyceny': 'Księgowość;Kadry',
        'faktury_rachunki_sprzedazowe___ile_': '5',
        'umowa_o_prace___liczba_osob': '25', // Exactly at BR00119/BR00081 threshold
        'umowy_cywilnoprawne___liczba_pracownikow': '50', // Should be ignored for thresholds
        'pakiet_kadrowo_placowy': 'Płace',
        'dodatkowe_skladniki_wynagrodzenia': '0'
      });

      getDealProperties.mockResolvedValue(mockProperties);

      const result = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      // New BR00013 calculation logic: max of Sum A (invoices+employees) or Sum B (cash-bank)
      // Sum A: 5 + 0 + 0 + 0 + 25 + 50 = 80 (employees included in calculation)
      // Sum B: 0 + 0 + 0 = 0
      // BR00013 document quantity = max(80, 0) = 80
      // Package selection based on bookings only (5 documents) = BASE package
      // With BASE package minimum (5), BR00013 quantity = max(80, 5) = 80
      expect(result.br00013Quantity).toBe(80);

      // BR00117 should be true (25 employment contracts >= 10)
      expect(result.shouldHaveBR00117).toBe(true);

      // BR00119 and BR00081 should be true (25 employment contracts >= 25)
      expect(result.shouldHaveBR00119).toBe(true);
      expect(result.shouldHaveBR00081).toBe(true);

      // BR00114 should include both employee types
      expect(result.br00114Quantity).toBe(75); // 25 + 50
    });
  });
});
