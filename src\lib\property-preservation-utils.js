/**
 * Property preservation utilities for maintaining existing VAT and language settings
 * when processing other property types
 */

import { getDealProperties } from './hubspot-api.js';
import { processVatStatus, processLanguageProperty } from './validation-utils.js';

/**
 * Preserve existing VAT and language settings from a deal
 * @param {string} dealId - Deal ID
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<Object>} Preserved settings result
 */
export async function preserveVatAndLanguageSettings(dealId, accessToken) {
    console.log('Preserving existing VAT and language settings');
    
    // Get current VAT and language properties
    const currentProps = await getDealProperties(dealId, accessToken, ['vat___status_podatnika', 'jezyk_obslugi']);
    
    // Process VAT settings
    const currentVatStatus = currentProps['vat___status_podatnika'] || '';
    const vatResult = processVatStatus(currentVatStatus);
    
    // Process language settings
    const currentLanguage = currentProps['jezyk_obslugi'] || '';
    const languageResult = processLanguageProperty(currentLanguage);
    
    console.log('Preserved VAT settings - BR00032:', vatResult.shouldHaveBR00032, 'BR00033:', vatResult.shouldHaveBR00033, 'VAT Status:', currentVatStatus);
    console.log('Preserved language setting - BR00129:', languageResult.shouldHaveBR00129, 'Language:', currentLanguage);
    
    return {
        shouldHaveBR00032: vatResult.shouldHaveBR00032,
        shouldHaveBR00033: vatResult.shouldHaveBR00033,
        br00032Quantity: vatResult.br00032Quantity,
        br00033Quantity: vatResult.br00033Quantity,
        shouldHaveBR00129: languageResult.shouldHaveBR00129
    };
}

/**
 * Preserve only VAT settings from a deal (for language property changes)
 * @param {string} dealId - Deal ID
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<Object>} Preserved VAT settings result
 */
export async function preserveVatSettings(dealId, accessToken) {
    console.log('Preserving existing VAT settings');
    
    // Get current VAT properties
    const currentVatProps = await getDealProperties(dealId, accessToken, ['vat___status_podatnika']);
    const currentVatStatus = currentVatProps['vat___status_podatnika'] || '';
    
    // Process VAT settings
    const vatResult = processVatStatus(currentVatStatus);
    
    console.log('Preserved VAT settings - BR00032:', vatResult.shouldHaveBR00032, 'BR00033:', vatResult.shouldHaveBR00033, 'VAT Status:', currentVatStatus);
    
    return {
        shouldHaveBR00032: vatResult.shouldHaveBR00032,
        shouldHaveBR00033: vatResult.shouldHaveBR00033,
        br00032Quantity: vatResult.br00032Quantity,
        br00033Quantity: vatResult.br00033Quantity
    };
}

/**
 * Preserve only language settings from a deal (for VAT property changes)
 * @param {string} dealId - Deal ID
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<Object>} Preserved language settings result
 */
export async function preserveLanguageSettings(dealId, accessToken) {
    console.log('Preserving existing language settings');

    // Get current language properties
    const currentLanguageProps = await getDealProperties(dealId, accessToken, ['jezyk_obslugi']);
    const currentLanguage = currentLanguageProps['jezyk_obslugi'] || '';
    
    // Process language settings
    const languageResult = processLanguageProperty(currentLanguage);
    
    console.log('Preserved language setting - BR00129:', languageResult.shouldHaveBR00129, 'Language:', currentLanguage);
    
    return {
        shouldHaveBR00129: languageResult.shouldHaveBR00129
    };
}
