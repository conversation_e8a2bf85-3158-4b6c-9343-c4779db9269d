import { describe, test, expect, vi, beforeEach } from 'vitest';
import {
  findLineItemBySku,
  findLineItemBySkuSafe,
  validateSkuInProductLibrary,
  createLineItemFromProduct,
  createLineItem,
  createLineItemWithQuantity,
  createLineItemFromProductWithQuantity,
  updateLineItemQuantity,
  updateLineItemPrice,
  deleteLineItem,
  associateLineItemWithDeal,
  createAndAssociateLineItem,
  manageLineItemQuantity,
  manageBooleanLineItem,
  createLineItemWithCustomPrice,
  manageLineItemWithCustomPrice,
  sortLineItemsByBRNumber,
  calculateBR00069CustomPrice
} from '../../src/lib/line-item-manager.js';

// Mock HubSpot API functions
vi.mock('../../src/lib/hubspot-api.js', () => ({
  findProductBySku: vi.fn(),
  updateLineItemsForDeal: vi.fn(),
  getLineItemsForDeal: vi.fn(),
  getDealProperties: vi.fn(),
  updateDealProperties: vi.fn(),
  getDealLineItemsWithDetails: vi.fn()
}));

// Mock price fetcher
vi.mock('../../src/lib/price-fetcher.js', () => ({
  getPayrollPrices: vi.fn()
}));

// Mock validation utils
vi.mock('../../src/lib/validation-utils.js', () => ({
  parseAndValidateNumericField: vi.fn()
}));

import { findProductBySku, getDealLineItemsWithDetails, getDealProperties } from '../../src/lib/hubspot-api.js';
import { getPayrollPrices } from '../../src/lib/price-fetcher.js';
import { parseAndValidateNumericField } from '../../src/lib/validation-utils.js';

describe('Line Item Manager', () => {
  const mockAccessToken = 'test-token';
  const mockDealId = 'deal123';
  
  // Mock fetch for API calls
  const mockFetch = vi.fn();
  global.fetch = mockFetch;

  beforeEach(() => {
    vi.clearAllMocks();
    mockFetch.mockClear();
  });

  const mockLineItems = [
    { id: 'line1', properties: { hs_sku: 'BR00013', name: 'Banking Operations', price: '15', quantity: '5' } },
    { id: 'line2', properties: { hs_sku: 'BR00032', name: 'VAT EU', price: '50', quantity: '1' } }
  ];

  describe('findLineItemBySku', () => {
    test('should find line item by hs_sku', () => {
      const result = findLineItemBySku(mockLineItems, 'BR00013');
      expect(result).toEqual(mockLineItems[0]);
    });

    test('should throw error when hs_sku not available', () => {
      expect(() => {
        findLineItemBySku(mockLineItems, 'BR00069');
      }).toThrow('Line item with SKU \'BR00069\' not found. Only existing line items are supported.');
    });

    test('should throw error when SKU not found', () => {
      expect(() => {
        findLineItemBySku(mockLineItems, 'NONEXISTENT');
      }).toThrow('Line item with SKU \'NONEXISTENT\' not found. Only existing line items are supported.');
    });

    test('should throw error for empty line items array', () => {
      expect(() => {
        findLineItemBySku([], 'BR00013');
      }).toThrow('Line item with SKU \'BR00013\' not found. Only existing line items are supported.');
    });
  });

  describe('findLineItemBySkuSafe', () => {
    test('should safely find line item by hs_sku', () => {
      const result = findLineItemBySkuSafe(mockLineItems, 'BR00013');
      expect(result).toEqual(mockLineItems[0]);
    });

    test('should return null when hs_sku not available (safe)', () => {
      const result = findLineItemBySkuSafe(mockLineItems, 'BR00069');
      expect(result).toBeNull();
    });

    test('should return null when SKU not found (safe)', () => {
      const result = findLineItemBySkuSafe(mockLineItems, 'NONEXISTENT');
      expect(result).toBeNull();
    });

    test('should return null for empty line items array (safe)', () => {
      const result = findLineItemBySkuSafe([], 'BR00013');
      expect(result).toBeNull();
    });
  });

  describe('validateSkuInProductLibrary', () => {
    test('should validate SKU exists in product library', async () => {
      const mockProduct = { id: 'prod123', properties: { hs_sku: 'BR00032' } };
      findProductBySku.mockResolvedValue(mockProduct);

      const result = await validateSkuInProductLibrary('BR00032', mockAccessToken);
      expect(result).toEqual(mockProduct);
    });

    test('should throw error when SKU not found in product library', async () => {
      findProductBySku.mockResolvedValue(null);

      await expect(validateSkuInProductLibrary('NONEXISTENT', mockAccessToken))
        .rejects.toThrow('SKU \'NONEXISTENT\' not found in product library. Cannot proceed with line item operations.');
    });
  });

  describe('createLineItemFromProduct', () => {
    test('should create line item from product successfully', async () => {
      const mockProduct = { id: 'prod123', properties: { hs_sku: 'BR00013' } };
      const mockResponse = { id: 'newline123', properties: { hs_sku: 'BR00013' } };
      
      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      });

      const result = await createLineItemFromProduct(mockProduct, mockAccessToken);

      expect(result).toEqual(mockResponse);
      expect(mockFetch).toHaveBeenCalledWith('https://api.hubapi.com/crm/v3/objects/line_items', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${mockAccessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          properties: {
            name: 'BR00013',
            hs_sku: 'BR00013',
            hs_product_id: 'prod123',
            quantity: 1,
            price: 0
          }
        })
      });
    });

    test('should handle API error when creating line item from product', async () => {
      const mockProduct = { id: 'prod123', properties: { hs_sku: 'BR00013' } };
      
      mockFetch.mockResolvedValue({
        ok: false,
        status: 400,
        statusText: 'Bad Request',
        text: () => Promise.resolve('Invalid data')
      });

      await expect(createLineItemFromProduct(mockProduct, mockAccessToken))
        .rejects.toThrow('Failed to create line item from product: Bad Request - Invalid data');
    });
  });

  describe('createLineItem', () => {
    test('should create line item with SKU successfully', async () => {
      const mockResponse = { id: 'newline123', properties: { hs_sku: 'BR00013' } };
      
      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      });

      const result = await createLineItem('BR00013', mockAccessToken);

      expect(result).toEqual(mockResponse);
      expect(mockFetch).toHaveBeenCalledWith('https://api.hubapi.com/crm/v3/objects/line_items', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${mockAccessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          properties: {
            name: 'BR00013',
            hs_sku: 'BR00013',
            quantity: 1,
            price: 0
          }
        })
      });
    });
  });

  describe('createLineItemWithQuantity', () => {
    test('should create line item with specific quantity', async () => {
      const mockResponse = { id: 'newline123', properties: { hs_sku: 'BR00013', quantity: '5' } };
      
      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      });

      const result = await createLineItemWithQuantity('BR00013', 5, mockAccessToken);

      expect(result).toEqual(mockResponse);
      expect(mockFetch).toHaveBeenCalledWith('https://api.hubapi.com/crm/v3/objects/line_items', expect.objectContaining({
        body: JSON.stringify({
          properties: {
            name: 'BR00013',
            hs_sku: 'BR00013',
            quantity: 5,
            price: 0
          }
        })
      }));
    });
  });

  describe('updateLineItemQuantity', () => {
    test('should update line item quantity successfully', async () => {
      const mockResponse = { id: 'line123', properties: { quantity: '10' } };
      
      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      });

      const result = await updateLineItemQuantity('line123', 10, mockAccessToken);

      expect(result).toEqual(mockResponse);
      expect(mockFetch).toHaveBeenCalledWith('https://api.hubapi.com/crm/v3/objects/line_items/line123', {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${mockAccessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          properties: {
            quantity: 10
          }
        })
      });
    });
  });

  describe('deleteLineItem', () => {
    test('should delete line item successfully', async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        status: 204
      });

      await deleteLineItem('line123', mockAccessToken);

      expect(mockFetch).toHaveBeenCalledWith('https://api.hubapi.com/crm/v3/objects/line_items/line123', {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${mockAccessToken}`,
          'Content-Type': 'application/json'
        }
      });
    });
  });

  describe('associateLineItemWithDeal', () => {
    test('should associate line item with deal successfully', async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
        json: () => Promise.resolve({ success: true })
      });

      const result = await associateLineItemWithDeal(mockDealId, 'line123', mockAccessToken);

      expect(result).toEqual({ success: true });
      expect(mockFetch).toHaveBeenCalledWith(`https://api.hubapi.com/crm/v3/objects/deals/${mockDealId}/associations/line_items/line123/deal_to_line_item`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${mockAccessToken}`,
          'Content-Type': 'application/json'
        }
      });
    });
  });

  describe('sortLineItemsByBRNumber', () => {

    test('should sort line items by BR number correctly', async () => {
      const mockLineItems = [
        { id: 'line1', properties: { hs_sku: 'BR00032', name: 'VAT EU' } },
        { id: 'line2', properties: { hs_sku: 'BR00013', name: 'Banking Operations' } },
        { id: 'line3', properties: { hs_sku: 'BR00111', name: 'MSP Audit' } },
        { id: 'line4', properties: { hs_sku: 'BR00001', name: 'Base Package' } }
      ];

      getDealLineItemsWithDetails.mockResolvedValue(mockLineItems);

      // Should not throw error and should log sorted items
      await expect(sortLineItemsByBRNumber(mockDealId, mockAccessToken)).resolves.toBeUndefined();

      expect(getDealLineItemsWithDetails).toHaveBeenCalledWith(mockDealId, mockAccessToken);
    });

    test('should handle non-BR SKUs correctly', async () => {
      const mockLineItems = [
        { id: 'line1', properties: { hs_sku: 'BR00032', name: 'VAT EU' } },
        { id: 'line2', properties: { hs_sku: 'CUSTOM001', name: 'Custom Item' } },
        { id: 'line3', properties: { hs_sku: 'BR00013', name: 'Banking Operations' } },
        { id: 'line4', properties: { name: 'No SKU Item' } } // No hs_sku property
      ];

      getDealLineItemsWithDetails.mockResolvedValue(mockLineItems);

      // Should not throw error and handle mixed SKU types
      await expect(sortLineItemsByBRNumber(mockDealId, mockAccessToken)).resolves.toBeUndefined();

      expect(getDealLineItemsWithDetails).toHaveBeenCalledWith(mockDealId, mockAccessToken);
    });

    test('should handle empty line items array', async () => {
      getDealLineItemsWithDetails.mockResolvedValue([]);

      // Should not throw error with empty array
      await expect(sortLineItemsByBRNumber(mockDealId, mockAccessToken)).resolves.toBeUndefined();

      expect(getDealLineItemsWithDetails).toHaveBeenCalledWith(mockDealId, mockAccessToken);
    });

    test('should handle null/undefined line items', async () => {
      getDealLineItemsWithDetails.mockResolvedValue(null);

      // Should not throw error with null response
      await expect(sortLineItemsByBRNumber(mockDealId, mockAccessToken)).resolves.toBeUndefined();

      expect(getDealLineItemsWithDetails).toHaveBeenCalledWith(mockDealId, mockAccessToken);
    });

    test('should handle API errors gracefully', async () => {
      getDealLineItemsWithDetails.mockRejectedValue(new Error('API Error'));

      // Should not throw error even when API fails
      await expect(sortLineItemsByBRNumber(mockDealId, mockAccessToken)).resolves.toBeUndefined();

      expect(getDealLineItemsWithDetails).toHaveBeenCalledWith(mockDealId, mockAccessToken);
    });

    test('should handle line items with missing properties', async () => {
      const mockLineItems = [
        { id: 'line1', properties: {} }, // No hs_sku or name
        { id: 'line2', properties: { hs_sku: 'BR00013' } }, // No name
        { id: 'line3', properties: { name: 'Some Item' } } // No hs_sku
      ];

      getDealLineItemsWithDetails.mockResolvedValue(mockLineItems);

      // Should not throw error with missing properties
      await expect(sortLineItemsByBRNumber(mockDealId, mockAccessToken)).resolves.toBeUndefined();

      expect(getDealLineItemsWithDetails).toHaveBeenCalledWith(mockDealId, mockAccessToken);
    });
  });

  describe('Error Handling and Edge Cases', () => {
    test('createLineItem should handle API errors', async () => {
      mockFetch.mockResolvedValue({
        ok: false,
        statusText: 'Bad Request',
        text: () => Promise.resolve('Invalid data')
      });

      await expect(createLineItem('BR00001', mockAccessToken))
        .rejects.toThrow('Failed to create line item: Bad Request - Invalid data');
    });

    test('createLineItemWithQuantity should handle API errors', async () => {
      mockFetch.mockResolvedValue({
        ok: false,
        statusText: 'Internal Server Error',
        text: () => Promise.resolve('Server error')
      });

      await expect(createLineItemWithQuantity('BR00001', 5, mockAccessToken))
        .rejects.toThrow('Failed to create line item with quantity: Internal Server Error - Server error');
    });

    test('createLineItemFromProduct should handle API errors', async () => {
      const mockProduct = {
        id: 'prod123',
        properties: { hs_sku: 'BR00001', name: 'Test Product', price: '100' }
      };

      mockFetch.mockResolvedValue({
        ok: false,
        statusText: 'Forbidden',
        text: () => Promise.resolve('Access denied')
      });

      await expect(createLineItemFromProduct(mockProduct, mockAccessToken))
        .rejects.toThrow('Failed to create line item from product: Forbidden - Access denied');
    });

    test('createLineItemFromProductWithQuantity should handle API errors', async () => {
      const mockProduct = {
        id: 'prod123',
        properties: { hs_sku: 'BR00001', name: 'Test Product', price: '100' }
      };

      mockFetch.mockResolvedValue({
        ok: false,
        statusText: 'Unprocessable Entity',
        text: () => Promise.resolve('Validation failed')
      });

      await expect(createLineItemFromProductWithQuantity(mockProduct, 3, mockAccessToken))
        .rejects.toThrow('Failed to create line item from product with quantity: Unprocessable Entity - Validation failed');
    });

    test('updateLineItemQuantity should handle API errors', async () => {
      mockFetch.mockResolvedValue({
        ok: false,
        statusText: 'Not Found',
        text: () => Promise.resolve('Line item not found')
      });

      await expect(updateLineItemQuantity('line123', 10, mockAccessToken))
        .rejects.toThrow('Failed to update line item quantity: Not Found - Line item not found');
    });

    test('deleteLineItem should handle API errors', async () => {
      mockFetch.mockResolvedValue({
        ok: false,
        statusText: 'Conflict',
        text: () => Promise.resolve('Cannot delete associated item')
      });

      await expect(deleteLineItem('line123', mockAccessToken))
        .rejects.toThrow('Failed to delete line item: Conflict - Cannot delete associated item');
    });

    test('associateLineItemWithDeal should handle v3 API failure and fallback to v4', async () => {
      // Mock v3 API failure
      mockFetch
        .mockResolvedValueOnce({
          ok: false,
          statusText: 'Method Not Allowed',
          text: () => Promise.resolve('V3 not supported')
        })
        // Mock v4 API success
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ status: 'COMPLETE' })
        });

      const result = await associateLineItemWithDeal(mockDealId, 'line123', mockAccessToken);

      expect(mockFetch).toHaveBeenCalledTimes(2);
      expect(result).toEqual({ status: 'COMPLETE' });
    });

    test('associateLineItemWithDeal should handle both v3 and v4 API failures', async () => {
      // Mock both v3 and v4 API failures
      mockFetch
        .mockResolvedValueOnce({
          ok: false,
          statusText: 'Method Not Allowed',
          text: () => Promise.resolve('V3 not supported')
        })
        .mockResolvedValueOnce({
          ok: false,
          statusText: 'Bad Request',
          text: () => Promise.resolve('V4 failed')
        });

      await expect(associateLineItemWithDeal(mockDealId, 'line123', mockAccessToken))
        .rejects.toThrow('Failed to associate line item with deal using v4 batch: Bad Request - V4 failed');
    });

    test('createAndAssociateLineItem should handle product not found', async () => {
      findProductBySku.mockResolvedValue(null);

      await expect(createAndAssociateLineItem('NONEXISTENT', 1, mockDealId, mockAccessToken))
        .rejects.toThrow('SKU \'NONEXISTENT\' not found in product library. Cannot proceed with line item operations.');
    });

    test('createAndAssociateLineItem should create line item with quantity 1', async () => {
      const mockProduct = {
        id: 'prod123',
        properties: { hs_sku: 'BR00001', name: 'Test Product', price: '100' }
      };
      const mockLineItem = { id: 'line123', properties: { hs_sku: 'BR00001' } };

      findProductBySku.mockResolvedValue(mockProduct);
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve(mockLineItem)
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ status: 'associated' })
        });

      const result = await createAndAssociateLineItem('BR00001', 1, mockDealId, mockAccessToken);

      expect(result).toEqual(mockLineItem);
      expect(mockFetch).toHaveBeenCalledTimes(2); // Create + Associate
    });

    test('createAndAssociateLineItem should create line item with custom quantity', async () => {
      const mockProduct = {
        id: 'prod123',
        properties: { hs_sku: 'BR00001', name: 'Test Product', price: '100' }
      };
      const mockLineItem = { id: 'line123', properties: { hs_sku: 'BR00001' } };

      findProductBySku.mockResolvedValue(mockProduct);
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve(mockLineItem)
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ status: 'associated' })
        });

      const result = await createAndAssociateLineItem('BR00001', 5, mockDealId, mockAccessToken);

      expect(result).toEqual(mockLineItem);
      expect(mockFetch).toHaveBeenCalledTimes(2); // Create + Associate
    });

    test('createLineItemWithCustomPrice should handle API errors', async () => {
      mockFetch.mockResolvedValue({
        ok: false,
        statusText: 'Bad Request',
        text: () => Promise.resolve('Invalid price')
      });

      await expect(createLineItemWithCustomPrice('BR00069', 500, mockAccessToken))
        .rejects.toThrow('Failed to create line item with custom price: Bad Request - Invalid price');
    });

    test('updateLineItemPrice should handle API errors', async () => {
      mockFetch.mockResolvedValue({
        ok: false,
        statusText: 'Not Found',
        text: () => Promise.resolve('Line item not found')
      });

      await expect(updateLineItemPrice('line123', 250, mockAccessToken))
        .rejects.toThrow('Failed to update line item price: Not Found - Line item not found');
    });

    test('manageLineItemWithCustomPrice should create new line item when none exists', async () => {
      const mockProduct = {
        id: 'prod123',
        properties: { hs_sku: 'BR00069', name: 'Ryczalt Package', price: '100' }
      };

      findProductBySku.mockResolvedValue(mockProduct);
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ id: 'line123' })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ id: 'line123', properties: { price: '500' } })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ status: 'associated' })
        });

      await manageLineItemWithCustomPrice('BR00069', 500, null, mockDealId, mockAccessToken);

      expect(mockFetch).toHaveBeenCalledTimes(3); // Create + Update Price + Associate
    });

    test('manageLineItemWithCustomPrice should update existing line item price', async () => {
      const existingLineItem = {
        id: 'line123',
        properties: { hs_sku: 'BR00069', price: '300' }
      };

      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ id: 'line123', properties: { price: '500' } })
      });

      await manageLineItemWithCustomPrice('BR00069', 500, existingLineItem, mockDealId, mockAccessToken);

      expect(mockFetch).toHaveBeenCalledTimes(1); // Update Price only
    });

    test('manageLineItemWithCustomPrice should delete line item when price is 0', async () => {
      const existingLineItem = {
        id: 'line123',
        properties: { hs_sku: 'BR00069', price: '300' }
      };

      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({})
      });

      await manageLineItemWithCustomPrice('BR00069', 0, existingLineItem, mockDealId, mockAccessToken);

      expect(mockFetch).toHaveBeenCalledTimes(1); // Delete only
    });

    test('manageLineItemWithCustomPrice should do nothing when price is 0 and no existing item', async () => {
      await manageLineItemWithCustomPrice('BR00069', 0, null, mockDealId, mockAccessToken);

      expect(mockFetch).not.toHaveBeenCalled();
    });
  });

  describe('calculateBR00069CustomPrice', () => {
    test('should calculate custom price based on payroll components', async () => {
      const mockDealProperties = {
        'umowa_o_prace___liczba_osob': '5',
        'umowy_cywilnoprawne___liczba_pracownikow': '3',
        'pfron___ile_osob_': '2',
        'a1___czy_wystepuja_': '1'
      };

      const mockPayrollPrices = {
        BR00070: 80,  // Employment contract price
        BR00071: 70,  // Civil contract price
        BR00080: 50,  // PFRON price
        BR00165: 120  // A1 price
      };

      getDealProperties.mockResolvedValue(mockDealProperties);
      getPayrollPrices.mockResolvedValue(mockPayrollPrices);

      parseAndValidateNumericField
        .mockReturnValueOnce(5)  // umowa_o_prace___liczba_osob
        .mockReturnValueOnce(3)  // umowy_cywilnoprawne___liczba_pracownikow
        .mockReturnValueOnce(2)  // pfron___ile_osob_
        .mockReturnValueOnce(1); // a1___czy_wystepuja_

      const result = await calculateBR00069CustomPrice(mockDealId, mockAccessToken);

      const expectedPrice = (5 * 80) + (3 * 70) + (2 * 50) + (1 * 120); // 400 + 210 + 100 + 120 = 830

      expect(result).toEqual({
        customPrice: expectedPrice,
        breakdown: {
          br00070: { count: 5, unitPrice: 80, total: 400 },
          br00071: { count: 3, unitPrice: 70, total: 210 },
          br00080: { count: 2, unitPrice: 50, total: 100 },
          br00165: { count: 1, unitPrice: 120, total: 120 }
        }
      });

      expect(getDealProperties).toHaveBeenCalledWith(mockDealId, mockAccessToken, [
        'umowa_o_prace___liczba_osob',
        'umowy_cywilnoprawne___liczba_pracownikow',
        'pfron___ile_osob_',
        'a1___czy_wystepuja_'
      ]);
      expect(getPayrollPrices).toHaveBeenCalledWith(mockAccessToken);
    });

    test('should handle zero values in payroll components', async () => {
      const mockDealProperties = {
        'umowa_o_prace___liczba_osob': '0',
        'umowy_cywilnoprawne___liczba_pracownikow': '0',
        'pfron___ile_osob_': '0',
        'a1___czy_wystepuja_': '0'
      };

      const mockPayrollPrices = {
        BR00070: 80,
        BR00071: 70,
        BR00080: 50,
        BR00165: 120
      };

      getDealProperties.mockResolvedValue(mockDealProperties);
      getPayrollPrices.mockResolvedValue(mockPayrollPrices);

      parseAndValidateNumericField
        .mockReturnValueOnce(0)
        .mockReturnValueOnce(0)
        .mockReturnValueOnce(0)
        .mockReturnValueOnce(0);

      const result = await calculateBR00069CustomPrice(mockDealId, mockAccessToken);

      expect(result.customPrice).toBe(0);
      expect(result.breakdown.br00070.total).toBe(0);
      expect(result.breakdown.br00071.total).toBe(0);
      expect(result.breakdown.br00080.total).toBe(0);
      expect(result.breakdown.br00165.total).toBe(0);
    });

    test('should handle API errors gracefully', async () => {
      getDealProperties.mockRejectedValue(new Error('HubSpot API error'));

      await expect(calculateBR00069CustomPrice(mockDealId, mockAccessToken))
        .rejects.toThrow('HubSpot API error');
    });

    test('should handle price fetching errors', async () => {
      const mockDealProperties = {
        'umowa_o_prace___liczba_osob': '1',
        'umowy_cywilnoprawne___liczba_pracownikow': '1',
        'pfron___ile_osob_': '1',
        'a1___czy_wystepuja_': '1'
      };

      getDealProperties.mockResolvedValue(mockDealProperties);
      getPayrollPrices.mockRejectedValue(new Error('Price fetch failed'));

      await expect(calculateBR00069CustomPrice(mockDealId, mockAccessToken))
        .rejects.toThrow('Price fetch failed');
    });

    test('should handle validation errors', async () => {
      const mockDealProperties = {
        'umowa_o_prace___liczba_osob': 'invalid',
        'umowy_cywilnoprawne___liczba_pracownikow': '1',
        'pfron___ile_osob_': '1',
        'a1___czy_wystepuja_': '1'
      };

      getDealProperties.mockResolvedValue(mockDealProperties);
      parseAndValidateNumericField.mockImplementation((props, field) => {
        if (field === 'umowa_o_prace___liczba_osob') {
          throw new Error('Invalid numeric value');
        }
        return 1;
      });

      await expect(calculateBR00069CustomPrice(mockDealId, mockAccessToken))
        .rejects.toThrow('Invalid numeric value');
    });
  });
});
