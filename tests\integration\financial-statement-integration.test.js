import { describe, test, expect, beforeEach } from 'vitest';
import { handleComprehensiveUpdate } from '../../src/lib/business-logic-handlers.js';

// Mock HubSpot API calls for integration testing
import { vi } from 'vitest';

vi.mock('../../src/lib/hubspot-api.js', () => ({
    getDealProperties: vi.fn(),
    updateDealProperties: vi.fn(),
    getLineItemsForDeal: vi.fn(),
    updateLineItemsForDeal: vi.fn(),
    findProductBySku: vi.fn(),
    getDealLineItemsWithDetails: vi.fn()
}));

vi.mock('../../src/lib/line-item-manager.js', () => ({
    deleteLineItem: vi.fn(),
    manageLineItemQuantity: vi.fn(),
    manageLineItemWithCustomPrice: vi.fn(),
    manageBooleanLineItem: vi.fn()
}));

vi.mock('../../src/lib/price-fetcher.js', () => ({
    getProductPrice: vi.fn(),
    getAccountingPackagePrices: vi.fn(),
    getEcommercePackagePrices: vi.fn(),
    getPayrollPrices: vi.fn(),
    getMultipleProductPrices: vi.fn()
}));

describe('Financial Statement Integration Tests', () => {
    const mockAccessToken = 'test-token';
    const mockDealId = 'test-deal-123';

    beforeEach(async () => {
        vi.clearAllMocks();

        // Setup default mocks
        const { getDealProperties, getLineItemsForDeal, getDealLineItemsWithDetails, updateDealProperties } = vi.mocked(await import('../../src/lib/hubspot-api.js'));
        const { getProductPrice, getAccountingPackagePrices, getEcommercePackagePrices, getMultipleProductPrices } = vi.mocked(await import('../../src/lib/price-fetcher.js'));
        const { deleteLineItem, manageLineItemQuantity } = vi.mocked(await import('../../src/lib/line-item-manager.js'));

        // Mock standard pricing
        getProductPrice.mockImplementation((sku) => {
            const prices = {
                // Financial statement packages
                'BR00099': 699.00,   // BASE
                'BR00100': 899.00,   // SILVER  
                'BR00101': 1599.00,  // GOLD
                'BR00102': 2599.00,  // PLATINUM
                // Individual document prices
                'BR00022': 5.00,     // Full accounting individual document
                'BR00030': 50.00,    // Fixed assets
                'BR00031': 30.00,    // Cash registers
                'BR00032': 100.00,   // VAT EU/8/9M
                'BR00012': 25.00,    // Monthly bank reports
                'BR00130': 15.00,    // Daily bank reports
                'BR00013': 10.00,    // Bank statement processing
                'BR00129': 100.00,   // Language service (non-Polish)
                // Accounting packages
                'BR00003': 500.00,   // BASE full accounting
                'BR00004': 800.00,   // SILVER full accounting
                'BR00005': 1200.00,  // GOLD full accounting
                'BR00006': 2000.00   // PLATINUM full accounting
            };
            return Promise.resolve(prices[sku] || 100);
        });

        // Mock accounting package prices
        getAccountingPackagePrices.mockResolvedValue({
            packages: {
                BASE: { sku: 'BR00003', price: 500.00 },
                SILVER: { sku: 'BR00004', price: 800.00 },
                GOLD: { sku: 'BR00005', price: 1200.00 },
                PLATINUM: { sku: 'BR00006', price: 2000.00 }
            },
            additionalPackages: {
                goldPack50: { sku: 'BR00027', price: 100.00 },
                platinumPack50: { sku: 'BR00028', price: 150.00 },
                platinumPack200: { sku: 'BR00029', price: 300.00 }
            },
            individualPrices: {
                BASE: 5.00,
                SILVER: 5.00,
                GOLD: 5.00,
                PLATINUM: 5.00
            }
        });

        // Mock e-commerce package prices
        getEcommercePackagePrices.mockResolvedValue({
            packages: {
                BR00058: 'BR00058',
                BR00059: 'BR00059',
                BR00060: 'BR00060',
                BR00061: 'BR00061'
            },
            additionalSkus: {
                BR00170: 'BR00170',
                BR00171: 'BR00171',
                BR00172: 'BR00172',
                BR00173: 'BR00173'
            },
            prices: {
                'BR00058': 200.00,
                'BR00059': 500.00,
                'BR00060': 1000.00,
                'BR00061': 2000.00,
                'BR00170': 1.00,
                'BR00171': 1.00,
                'BR00172': 1.00,
                'BR00173': 1.00
            }
        });

        // Mock getMultipleProductPrices for financial statement packages
        getMultipleProductPrices.mockImplementation((skus) => {
            const prices = {
                'BR00099': 699.00,   // BASE
                'BR00100': 899.00,   // SILVER
                'BR00101': 1599.00,  // GOLD
                'BR00102': 2599.00   // PLATINUM
            };
            const result = {};
            skus.forEach(sku => {
                result[sku] = prices[sku] || 100;
            });
            return Promise.resolve(result);
        });

        // Mock line item operations
        getLineItemsForDeal.mockResolvedValue([]);
        getDealLineItemsWithDetails.mockResolvedValue([]);
        updateDealProperties.mockResolvedValue();
        deleteLineItem.mockResolvedValue();
        manageLineItemQuantity.mockResolvedValue();
    });

    test('should calculate financial statement for BASE package with low document count', async () => {
        const { getDealProperties } = vi.mocked(await import('../../src/lib/hubspot-api.js'));
        
        // Mock deal properties for BASE package scenario
        getDealProperties.mockResolvedValue({
            'rodzaj_ksiegowosci': 'Pełna księgowość',
            'faktury_rachunki_sprzedazowe___ile_': '10',
            'faktury_rachunki_zakupu___ile_': '8',
            'faktury_walutowe___ile_miesiecznie_': '2',
            'dokumenty_wewnetrzne_wdt__wnt_itp': '5',
            'kp_kw___banki_': '3',
            'kp_kw_gotowka': '2',
            'operacje_kp_kw_walutowe': '1',
            'kasy_fiskalne___ile_': '0',
            'srodki_trwale_i_wartosci_niematerialne_i_prawne___ile_': '0',
            'vat___status_podatnika': '',
            'ilosc_kont_bankowych___raporty_dzienne': '0',
            'ilosc_kont_bankowych___raporty_miesieczne': '0',
            'ile_transakcji_sprzedazy_w_miesiacu_': '0',
            'jezyk_obslugi': 'Polski',
            'pakiet_kadrowo_placowy': '',
            'pytania_do_msp': '',
            'uslugi_do_wyceny': 'Księgowość',
            'branza': ''
        });

        const result = await handleComprehensiveUpdate('aktualizuj_dane', 'test', mockDealId, mockAccessToken);

        // Should select BASE package for full accounting
        expect(result.selectedPackageName).toBe('BASE');
        expect(result.isFullAccounting).toBe(true);

        // Should have financial statement packages
        expect(result.financialStatementRefreshed).toBe(true);
        expect(result.financialStatementPackages).toHaveLength(1);
        
        const financialStatementPackage = result.financialStatementPackages[0];
        expect(financialStatementPackage.sku).toBe('BR00099'); // BASE financial statement
        expect(financialStatementPackage.quantity).toBe(1);
        
        // The calculated base price is lower than package price, so package price is used
        // Expected calculation: 25 documents * 5.00 + 6 cash-bank operations * 10.00 = 185.00
        // Package price (699.00) is higher, so package price is used
        expect(financialStatementPackage.price).toBe(699.00);
        expect(financialStatementPackage.customPrice).toBe(false);
    });

    test('should calculate financial statement for SILVER package with high document count', async () => {
        const { getDealProperties } = vi.mocked(await import('../../src/lib/hubspot-api.js'));
        
        // Mock deal properties for SILVER package with moderate document count
        getDealProperties.mockResolvedValue({
            'rodzaj_ksiegowosci': 'Pełna księgowość',
            'faktury_rachunki_sprzedazowe___ile_': '40',
            'faktury_rachunki_zakupu___ile_': '30',
            'faktury_walutowe___ile_miesiecznie_': '5',
            'dokumenty_wewnetrzne_wdt__wnt_itp': '10',
            'kp_kw___banki_': '10',
            'kp_kw_gotowka': '5',
            'operacje_kp_kw_walutowe': '3',
            'kasy_fiskalne___ile_': '2',
            'srodki_trwale_i_wartosci_niematerialne_i_prawne___ile_': '1',
            'vat___status_podatnika': 'VAT EU',
            'ilosc_kont_bankowych___raporty_dzienne': '1',
            'ilosc_kont_bankowych___raporty_miesieczne': '2',
            'ile_transakcji_sprzedazy_w_miesiacu_': '0',
            'jezyk_obslugi': 'Polski',
            'pakiet_kadrowo_placowy': '',
            'pytania_do_msp': '',
            'uslugi_do_wyceny': 'Księgowość',
            'branza': ''
        });

        const result = await handleComprehensiveUpdate('aktualizuj_dane', 'test', mockDealId, mockAccessToken);

        // Should select SILVER package for full accounting
        expect(result.selectedPackageName).toBe('SILVER');
        expect(result.isFullAccounting).toBe(true);

        // Should have financial statement packages
        expect(result.financialStatementRefreshed).toBe(true);
        expect(result.financialStatementPackages).toHaveLength(1);
        
        const financialStatementPackage = result.financialStatementPackages[0];
        expect(financialStatementPackage.sku).toBe('BR00100'); // SILVER financial statement
        expect(financialStatementPackage.quantity).toBe(1);
        
        // The calculated base price is lower than package price, so package price is used
        // Expected calculation: 85 documents * 5.00 + 18 cash-bank * 10.00 + 2 cash registers * 30.00 +
        // 1 fixed asset * 50.00 + VAT EU 100.00 + 1 daily report * 15.00 + 2 monthly reports * 25.00 = 880.00
        // Package price (899.00) is higher, so package price is used
        expect(financialStatementPackage.price).toBe(899.00);
        expect(financialStatementPackage.customPrice).toBe(false);
    });

    test('should not add financial statement packages for simplified accounting', async () => {
        const { getDealProperties } = vi.mocked(await import('../../src/lib/hubspot-api.js'));
        
        // Mock deal properties for simplified accounting
        getDealProperties.mockResolvedValue({
            'rodzaj_ksiegowosci': 'Księgowość uproszczona',
            'faktury_rachunki_sprzedazowe___ile_': '50',
            'faktury_rachunki_zakupu___ile_': '30',
            'faktury_walutowe___ile_miesiecznie_': '10',
            'dokumenty_wewnetrzne_wdt__wnt_itp': '15',
            'kp_kw___banki_': '5',
            'kp_kw_gotowka': '3',
            'operacje_kp_kw_walutowe': '2',
            'kasy_fiskalne___ile_': '0',
            'srodki_trwale_i_wartosci_niematerialne_i_prawne___ile_': '0',
            'vat___status_podatnika': '',
            'ilosc_kont_bankowych___raporty_dzienne': '0',
            'ilosc_kont_bankowych___raporty_miesieczne': '0',
            'ile_transakcji_sprzedazy_w_miesiacu_': '0',
            'jezyk_obslugi': 'Polski',
            'pakiet_kadrowo_placowy': '',
            'pytania_do_msp': '',
            'uslugi_do_wyceny': 'Księgowość',
            'branza': ''
        });

        const result = await handleComprehensiveUpdate('aktualizuj_dane', 'test', mockDealId, mockAccessToken);

        // Should be simplified accounting
        expect(result.isFullAccounting).toBe(false);

        // Should NOT have financial statement packages
        expect(result.financialStatementRefreshed).toBe(false);
        expect(result.financialStatementPackages).toEqual([]);
    });

    test('should handle edge case with PLATINUM package and complex pricing', async () => {
        const { getDealProperties } = vi.mocked(await import('../../src/lib/hubspot-api.js'));
        
        // Mock deal properties for PLATINUM package with complex scenario
        getDealProperties.mockResolvedValue({
            'rodzaj_ksiegowosci': 'Pełna księgowość',
            'faktury_rachunki_sprzedazowe___ile_': '500',
            'faktury_rachunki_zakupu___ile_': '300',
            'faktury_walutowe___ile_miesiecznie_': '50',
            'dokumenty_wewnetrzne_wdt__wnt_itp': '100',
            'kp_kw___banki_': '25',
            'kp_kw_gotowka': '15',
            'operacje_kp_kw_walutowe': '10',
            'kasy_fiskalne___ile_': '5',
            'srodki_trwale_i_wartosci_niematerialne_i_prawne___ile_': '3',
            'vat___status_podatnika': 'VAT EU;VAT 8',
            'ilosc_kont_bankowych___raporty_dzienne': '3',
            'ilosc_kont_bankowych___raporty_miesieczne': '5',
            'ile_transakcji_sprzedazy_w_miesiacu_': '1500',
            'jezyk_obslugi': 'English',
            'pakiet_kadrowo_placowy': '',
            'pytania_do_msp': '',
            'uslugi_do_wyceny': 'Księgowość',
            'branza': 'E-commerce'
        });

        const result = await handleComprehensiveUpdate('aktualizuj_dane', 'test', mockDealId, mockAccessToken);

        // Should select PLATINUM package for full accounting
        expect(result.selectedPackageName).toBe('PLATINUM');
        expect(result.isFullAccounting).toBe(true);

        // Should have financial statement packages
        expect(result.financialStatementRefreshed).toBe(true);
        expect(result.financialStatementPackages).toHaveLength(1);
        
        const financialStatementPackage = result.financialStatementPackages[0];
        expect(financialStatementPackage.sku).toBe('BR00102'); // PLATINUM financial statement
        expect(financialStatementPackage.quantity).toBe(1);
        
        // With this many documents and additional costs, calculated price should be very high
        expect(financialStatementPackage.price).toBeGreaterThan(2599.00); // Higher than package price
        expect(financialStatementPackage.customPrice).toBe(true);
    });
});
