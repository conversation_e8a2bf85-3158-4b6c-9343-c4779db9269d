/**
 * Line item management utilities for HubSpot
 */

import { findProductBySku, getDealLineItemsWithDetails } from './hubspot-api.js';
import { getPayrollPrices } from './price-fetcher.js';
import { parseAndValidateNumericField } from './validation-utils.js';

/**
 * Check if a line item with given SKU exists in the deal
 * @param {Array} lineItems - Array of line items
 * @param {string} sku - SKU to search for
 * @returns {Object} Line item object if found
 * @throws {Error} If line item with SKU is not found
 */
export function findLineItemBySku(lineItems, sku) {
    const lineItem = lineItems.find(item =>
        item.properties.hs_sku === sku
    );

    if (!lineItem) {
        throw new Error(`Line item with SKU '${sku}' not found. Only existing line items are supported.`);
    }

    return lineItem;
}

/**
 * Safely find a line item by SKU without throwing errors
 * @param {Array} lineItems - Array of line items
 * @param {string} sku - SKU to search for
 * @returns {Object|null} Line item object if found, null otherwise
 */
export function findLineItemBySkuSafe(lineItems, sku) {
    const lineItem = lineItems.find(item => item.properties.hs_sku === sku);
    return lineItem || null;
}

/**
 * Validate that a SKU exists in the product library
 * @param {string} sku - SKU to validate
 * @param {string} accessToken - HubSpot access token
 * @throws {Error} If SKU doesn't exist in product library
 */
export async function validateSkuInProductLibrary(sku, accessToken) {
    const product = await findProductBySku(sku, accessToken);
    if (!product) {
        throw new Error(`SKU '${sku}' not found in product library. Cannot proceed with line item operations.`);
    }
    return product;
}

/**
 * Create a line item from an existing product
 * @param {Object} product - Product object from HubSpot
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<Object>} Created line item object
 */
export async function createLineItemFromProduct(product, accessToken) {
    try {
        console.log('Creating line item from product:', product.id);

        const response = await fetch('https://api.hubapi.com/crm/v3/objects/line_items', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                properties: {
                    name: product.properties.name || product.properties.hs_sku,
                    hs_sku: product.properties.hs_sku,
                    hs_product_id: product.id,
                    quantity: 1,
                    price: product.properties.price || 0
                }
            })
        });

        if (!response.ok) {
            const errorData = await response.text();
            throw new Error(`Failed to create line item from product: ${response.statusText} - ${errorData}`);
        }

        return await response.json();
    } catch (error) {
        console.error('Error creating line item from product:', error);
        throw error;
    }
}

/**
 * Create a new line item with given SKU (fallback method)
 * @param {string} sku - SKU for the new line item
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<Object>} Created line item object
 */
export async function createLineItem(sku, accessToken) {
    try {
        const response = await fetch('https://api.hubapi.com/crm/v3/objects/line_items', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                properties: {
                    name: sku,
                    hs_sku: sku,
                    quantity: 1,
                    price: 0
                }
            })
        });

        if (!response.ok) {
            const errorData = await response.text();
            throw new Error(`Failed to create line item: ${response.statusText} - ${errorData}`);
        }

        return await response.json();
    } catch (error) {
        console.error('Error creating line item:', error);
        throw error;
    }
}

/**
 * Create a new line item with given SKU and specific quantity
 * @param {string} sku - SKU for the new line item
 * @param {number} quantity - Quantity for the line item
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<Object>} Created line item object
 */
export async function createLineItemWithQuantity(sku, quantity, accessToken) {
    try {
        const response = await fetch('https://api.hubapi.com/crm/v3/objects/line_items', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                properties: {
                    name: sku,
                    hs_sku: sku,
                    quantity: quantity,
                    price: 0
                }
            })
        });

        if (!response.ok) {
            const errorData = await response.text();
            throw new Error(`Failed to create line item with quantity: ${response.statusText} - ${errorData}`);
        }

        return await response.json();
    } catch (error) {
        console.error('Error creating line item with quantity:', error);
        throw error;
    }
}

/**
 * Create a line item from an existing product with specific quantity
 * @param {Object} product - Product object from HubSpot
 * @param {number} quantity - Quantity for the line item
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<Object>} Created line item object
 */
export async function createLineItemFromProductWithQuantity(product, quantity, accessToken) {
    try {
        console.log('Creating line item from product with quantity:', product.id, quantity);

        const response = await fetch('https://api.hubapi.com/crm/v3/objects/line_items', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                properties: {
                    name: product.properties.name || product.properties.hs_sku,
                    hs_sku: product.properties.hs_sku,
                    hs_product_id: product.id,
                    quantity: quantity,
                    price: product.properties.price
                }
            })
        });

        if (!response.ok) {
            const errorData = await response.text();
            throw new Error(`Failed to create line item from product with quantity: ${response.statusText} - ${errorData}`);
        }

        return await response.json();
    } catch (error) {
        console.error('Error creating line item from product with quantity:', error);
        throw error;
    }
}

/**
 * Update the quantity of an existing line item
 * @param {string} lineItemId - Line item ID to update
 * @param {number} quantity - New quantity
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<Object>} Updated line item object
 */
export async function updateLineItemQuantity(lineItemId, quantity, accessToken) {
    try {
        console.log(`Updating line item ${lineItemId} quantity to ${quantity}`);

        const response = await fetch(`https://api.hubapi.com/crm/v3/objects/line_items/${lineItemId}`, {
            method: 'PATCH',
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                properties: {
                    quantity: quantity
                }
            })
        });

        if (!response.ok) {
            const errorData = await response.text();
            throw new Error(`Failed to update line item quantity: ${response.statusText} - ${errorData}`);
        }

        return await response.json();
    } catch (error) {
        console.error('Error updating line item quantity:', error);
        throw error;
    }
}

/**
 * Delete a line item
 * @param {string} lineItemId - Line item ID to delete
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<void>}
 */
export async function deleteLineItem(lineItemId, accessToken) {
    try {
        const response = await fetch(`https://api.hubapi.com/crm/v3/objects/line_items/${lineItemId}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            const errorData = await response.text();
            throw new Error(`Failed to delete line item: ${response.statusText} - ${errorData}`);
        }
    } catch (error) {
        console.error('Error deleting line item:', error);
        throw error;
    }
}

/**
 * Associate a line item with a deal
 * @param {string} dealId - Deal ID
 * @param {string} lineItemId - Line item ID
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<Object>} Association result
 */
export async function associateLineItemWithDeal(dealId, lineItemId, accessToken) {
    try {
        console.log('Associating line item with deal:');
        console.log('Deal ID:', dealId);
        console.log('Line Item ID:', lineItemId);

        // Try using the v3 associations API instead
        const url = `https://api.hubapi.com/crm/v3/objects/deals/${dealId}/associations/line_items/${lineItemId}/deal_to_line_item`;
        console.log('Association URL:', url);

        const response = await fetch(url, {
            method: 'PUT',
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            const errorData = await response.text();
            console.log('V3 association failed, trying v4 batch method...');

            // Try v4 batch associations as fallback
            return await associateLineItemWithDealV4Batch(dealId, lineItemId, accessToken);
        }

        return await response.json();
    } catch (error) {
        console.error('Error associating line item with deal:', error);
        throw error;
    }
}

/**
 * Alternative association method using v4 batch API
 */
async function associateLineItemWithDealV4Batch(dealId, lineItemId, accessToken) {
    try {
        console.log('Trying v4 batch association method...');

        const response = await fetch('https://api.hubapi.com/crm/v4/associations/deals/line_items/batch/create', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                inputs: [{
                    from: { id: dealId },
                    to: { id: lineItemId },
                    types: [{
                        associationCategory: "HUBSPOT_DEFINED",
                        associationTypeId: 20
                    }]
                }]
            })
        });

        if (!response.ok) {
            const errorData = await response.text();
            throw new Error(`Failed to associate line item with deal using v4 batch: ${response.statusText} - ${errorData}`);
        }

        return await response.json();
    } catch (error) {
        console.error('Error in v4 batch association:', error);
        throw error;
    }
}

/**
 * Create a line item with automatic product lookup and association
 * @param {string} sku - SKU for the line item
 * @param {number} quantity - Quantity for the line item (default: 1)
 * @param {string} dealId - Deal ID to associate with
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<Object>} Created and associated line item
 */
export async function createAndAssociateLineItem(sku, quantity = 1, dealId, accessToken) {
    try {
        console.log(`Creating and associating line item: ${sku} x${quantity} for deal ${dealId}`);

        // Validate that the SKU exists in the product library first
        const existingProduct = await validateSkuInProductLibrary(sku, accessToken);

        console.log(`Found existing product ${sku}, creating line item from product...`);
        let newLineItem;

        if (quantity === 1) {
            newLineItem = await createLineItemFromProduct(existingProduct, accessToken);
        } else {
            newLineItem = await createLineItemFromProductWithQuantity(existingProduct, quantity, accessToken);
        }

        // Associate with deal
        await associateLineItemWithDeal(dealId, newLineItem.id, accessToken);
        console.log(`Line item ${sku} created and associated successfully`);

        return newLineItem;
    } catch (error) {
        console.error(`Error creating and associating line item ${sku}:`, error);
        throw error;
    }
}

/**
 * Unified line item management - always works with product library
 * @param {string} sku - SKU for the line item
 * @param {number} requiredQuantity - Required quantity (0 means delete)
 * @param {Object|null} existingLineItem - Existing line item object or null (for optimization)
 * @param {string} dealId - Deal ID for association
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<void>}
 */
export async function manageLineItemQuantity(sku, requiredQuantity, existingLineItem, dealId, accessToken) {
    try {
        console.log(`Managing line item ${sku} with required quantity: ${requiredQuantity}`);

        if (requiredQuantity > 0) {
            if (existingLineItem) {
                // Update existing line item
                const currentQuantity = parseInt(existingLineItem.properties.quantity) || 1;
                if (currentQuantity !== requiredQuantity) {
                    console.log(`Updating ${sku} quantity from ${currentQuantity} to ${requiredQuantity}`);
                    await updateLineItemQuantity(existingLineItem.id, requiredQuantity, accessToken);
                } else {
                    console.log(`${sku} already has correct quantity (${currentQuantity})`);
                }
            } else {
                // Create new line item from product library
                console.log(`Creating new line item ${sku} from product library with quantity ${requiredQuantity}`);
                await createAndAssociateLineItem(sku, requiredQuantity, dealId, accessToken);
            }
        } else {
            // Delete existing line item if quantity is 0
            if (existingLineItem) {
                console.log(`Deleting existing ${sku} line item (quantity is 0)...`);
                await deleteLineItem(existingLineItem.id, accessToken);
                console.log(`${sku} line item deleted`);
            }
            // If no existing line item and quantity is 0, nothing to do
        }
    } catch (error) {
        console.error(`Error managing line item ${sku}:`, error);
        throw error;
    }
}

/**
 * Unified boolean line item management - always works with product library
 * @param {string} sku - SKU for the line item
 * @param {boolean} shouldExist - Whether the line item should exist
 * @param {Object|null} existingLineItem - Existing line item object or null (for optimization)
 * @param {string} dealId - Deal ID to associate with
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<void>}
 */
export async function manageBooleanLineItem(sku, shouldExist, existingLineItem, dealId, accessToken) {
    try {
        console.log(`Managing boolean line item ${sku}, should exist: ${shouldExist}`);

        if (shouldExist) {
            if (!existingLineItem) {
                // Create new line item from product library
                console.log(`Creating new boolean line item ${sku} from product library`);
                await createAndAssociateLineItem(sku, 1, dealId, accessToken);
            } else {
                console.log(`${sku} line item already exists`);
            }
        } else {
            if (existingLineItem) {
                console.log(`Deleting existing ${sku} line item...`);
                await deleteLineItem(existingLineItem.id, accessToken);
                console.log(`${sku} line item deleted`);
            }
            // If no existing line item and shouldn't exist, nothing to do
        }
    } catch (error) {
        console.error(`Error managing boolean line item ${sku}:`, error);
        throw error;
    }
}

/**
 * Create a line item with custom price (quantity = 1)
 * @param {string} sku - SKU for the line item
 * @param {number} customPrice - Custom price for the line item
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<Object>} Created line item object
 */
export async function createLineItemWithCustomPrice(sku, customPrice, accessToken) {
    try {
        console.log(`Creating line item ${sku} with custom price: ${customPrice}`);

        const response = await fetch('https://api.hubapi.com/crm/v3/objects/line_items', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                properties: {
                    name: sku,
                    hs_sku: sku,
                    quantity: 1,
                    price: customPrice
                }
            })
        });

        if (!response.ok) {
            const errorData = await response.text();
            throw new Error(`Failed to create line item with custom price: ${response.statusText} - ${errorData}`);
        }

        return await response.json();
    } catch (error) {
        console.error('Error creating line item with custom price:', error);
        throw error;
    }
}

/**
 * Update the price of an existing line item (keeping quantity = 1)
 * @param {string} lineItemId - Line item ID to update
 * @param {number} customPrice - New custom price
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<Object>} Updated line item object
 */
export async function updateLineItemPrice(lineItemId, customPrice, accessToken) {
    try {
        console.log(`Updating line item ${lineItemId} price to ${customPrice}`);

        const response = await fetch(`https://api.hubapi.com/crm/v3/objects/line_items/${lineItemId}`, {
            method: 'PATCH',
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                properties: {
                    quantity: 1,
                    price: customPrice
                }
            })
        });

        if (!response.ok) {
            const errorData = await response.text();
            throw new Error(`Failed to update line item price: ${response.statusText} - ${errorData}`);
        }

        return await response.json();
    } catch (error) {
        console.error('Error updating line item price:', error);
        throw error;
    }
}

/**
 * Manage line item with custom price (create, update price, or delete based on value)
 * @param {string} sku - SKU for the line item
 * @param {number} customPrice - Custom price (0 means delete)
 * @param {Object|null} existingLineItem - Existing line item object or null
 * @param {string} dealId - Deal ID to associate with
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<void>}
 */
export async function manageLineItemWithCustomPrice(sku, customPrice, existingLineItem, dealId, accessToken) {
    try {
        if (customPrice > 0) {
            if (existingLineItem) {
                const currentPriceValue = parseFloat(existingLineItem.properties.price);

                // Validate current price
                if (isNaN(currentPriceValue)) {
                    throw new Error(`Invalid current price for line item ${sku}: ${existingLineItem.properties.price}`);
                }

                if (currentPriceValue !== customPrice) {
                    console.log(`Updating ${sku} price from ${currentPriceValue} to ${customPrice}`);
                    await updateLineItemPrice(existingLineItem.id, customPrice, accessToken);
                } else {
                    console.log(`${sku} already has correct price (${currentPrice})`);
                }
            } else {
                // Create new line item with custom price from product library
                console.log(`Creating new line item ${sku} from product library with custom price ${customPrice}`);
                const existingProduct = await validateSkuInProductLibrary(sku, accessToken);
                const newLineItem = await createLineItemFromProductWithQuantity(existingProduct, 1, accessToken);

                // Update the price to the custom price
                await updateLineItemPrice(newLineItem.id, customPrice, accessToken);

                // Associate with deal
                await associateLineItemWithDeal(dealId, newLineItem.id, accessToken);
                console.log(`Line item ${sku} created with custom price and associated successfully`);
            }
        } else {
            if (existingLineItem) {
                console.log(`Deleting ${sku} line item (price is 0)...`);
                await deleteLineItem(existingLineItem.id, accessToken);
            } else {
                console.log(`${sku} line item doesn't exist and price is 0 (no action needed)`);
            }
        }
    } catch (error) {
        console.error(`Error managing line item with custom price ${sku}:`, error);
        throw error;
    }
}

/**
 * Update accounting packages to match optimal configuration
 * @param {Array} currentAccountingItems - Current accounting line items
 * @param {Array} optimalPackages - Optimal package configuration
 * @param {string} dealId - Deal ID
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<void>}
 */
export async function updateAccountingPackagesToOptimal(currentAccountingItems, optimalPackages, dealId, accessToken) {
    console.log('Updating accounting packages to optimal configuration...');
    console.log('Current items:', currentAccountingItems.map(item => ({
        sku: item.properties.hs_sku,
        quantity: item.properties.quantity || 1
    })));
    console.log('Optimal packages:', optimalPackages);

    // Process each optimal package using manageLineItemQuantity for full create/update/delete support
    for (const optimalPackage of optimalPackages) {
        const existingItem = currentAccountingItems.find(item =>
            (item.properties.hs_sku || item.properties.name || '') === optimalPackage.sku
        );

        console.log(`Managing accounting package ${optimalPackage.sku} with quantity ${optimalPackage.quantity}`);
        await manageLineItemQuantity(optimalPackage.sku, optimalPackage.quantity, existingItem, dealId, accessToken);
    }

    // Remove any accounting packages that are no longer needed
    const optimalSkus = optimalPackages.map(pkg => pkg.sku);
    const itemsToRemove = currentAccountingItems.filter(item => {
        const sku = item.properties.hs_sku || item.properties.name || '';
        return !optimalSkus.includes(sku);
    });

    for (const item of itemsToRemove) {
        console.log(`Removing unneeded accounting item: ${item.id} (${item.properties.hs_sku})`);
        await deleteLineItem(item.id, accessToken);
    }

    console.log('Accounting packages optimization completed');
}

/**
 * Sort line items alphabetically by their BR numbers
 * @param {string} dealId - Deal ID to sort line items for
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<void>}
 */
export async function sortLineItemsByBRNumber(dealId, accessToken) {
    try {
        console.log('Sorting line items by BR number for deal:', dealId);

        // Get all current line items
        const lineItems = await getDealLineItemsWithDetails(dealId, accessToken);

        if (!lineItems || lineItems.length === 0) {
            console.log('No line items to sort');
            return;
        }

        // Extract BR numbers and sort
        const lineItemsWithBRNumbers = lineItems.map(item => {
            const sku = item.properties.hs_sku || item.properties.name || '';
            const brMatch = sku.match(/^BR(\d+)$/);
            const brNumber = brMatch ? parseInt(brMatch[1], 10) : 999999; // Non-BR items go to the end

            return {
                item,
                sku,
                brNumber,
                originalSku: sku
            };
        });

        // Sort by BR number (numerically, which gives alphabetical order for BR codes)
        lineItemsWithBRNumbers.sort((a, b) => {
            // First sort by BR number
            if (a.brNumber !== b.brNumber) {
                return a.brNumber - b.brNumber;
            }
            // If BR numbers are the same (or both non-BR), sort alphabetically by SKU
            return a.originalSku.localeCompare(b.originalSku);
        });

        console.log('Line items sorted by BR number:', lineItemsWithBRNumbers.map(item => ({
            sku: item.originalSku,
            brNumber: item.brNumber === 999999 ? 'N/A' : item.brNumber
        })));

        // Note: HubSpot doesn't provide a direct API to reorder line items
        // The sorting is mainly for logging and potential future use
        // Line items will appear in the order they were created in HubSpot

    } catch (error) {
        console.error('Error sorting line items by BR number:', error);
        // Don't throw error as this is a non-critical operation
    }
}

/**
 * Calculate BR00069 custom price based on component breakdown
 * @param {string} dealId - Deal ID
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<Object>} Price calculation result with breakdown
 */
export async function calculateBR00069CustomPrice(dealId, accessToken) {
    try {
        const { getDealProperties } = await import('./hubspot-api.js');

        // Get current deal properties to break down the BR00069 quantity into components
        const br00069Properties = await getDealProperties(dealId, accessToken, [
            'umowa_o_prace___liczba_osob',
            'umowy_cywilnoprawne___liczba_pracownikow',
            'pfron___ile_osob_',
            'a1___czy_wystepuja_'
        ]);

        // Parse and validate numeric fields
        const br00070Count = parseAndValidateNumericField(br00069Properties, 'umowa_o_prace___liczba_osob');
        const br00071Count = parseAndValidateNumericField(br00069Properties, 'umowy_cywilnoprawne___liczba_pracownikow');
        const br00080Count = parseAndValidateNumericField(br00069Properties, 'pfron___ile_osob_');
        const br00165Count = parseAndValidateNumericField(br00069Properties, 'a1___czy_wystepuja_');

        // Fetch dynamic pricing for payroll components
        const payrollPrices = await getPayrollPrices(accessToken);

        // Calculate custom price using dynamic component pricing
        const customPrice =
            (br00070Count * payrollPrices.BR00070) +    // BR00070: Premium employment contracts
            (br00071Count * payrollPrices.BR00071) +    // BR00071: Premium civil contracts
            (br00080Count * payrollPrices.BR00080) +    // BR00080: PFRON declaration
            (br00165Count * payrollPrices.BR00165);     // BR00165: A1 certificate

        console.log(`BR00069 custom price calculation:`);
        console.log(`- BR00070 (umowa o pracę): ${br00070Count} × ${payrollPrices.BR00070} = ${br00070Count * payrollPrices.BR00070}`);
        console.log(`- BR00071 (umowy cywilnoprawne): ${br00071Count} × ${payrollPrices.BR00071} = ${br00071Count * payrollPrices.BR00071}`);
        console.log(`- BR00080 (PFRON): ${br00080Count} × ${payrollPrices.BR00080} = ${br00080Count * payrollPrices.BR00080}`);
        console.log(`- BR00165 (A1): ${br00165Count} × ${payrollPrices.BR00165} = ${br00165Count * payrollPrices.BR00165}`);
        console.log(`- Total BR00069 price: ${customPrice}`);

        return {
            customPrice,
            breakdown: {
                br00070: { count: br00070Count, unitPrice: payrollPrices.BR00070, total: br00070Count * payrollPrices.BR00070 },
                br00071: { count: br00071Count, unitPrice: payrollPrices.BR00071, total: br00071Count * payrollPrices.BR00071 },
                br00080: { count: br00080Count, unitPrice: payrollPrices.BR00080, total: br00080Count * payrollPrices.BR00080 },
                br00165: { count: br00165Count, unitPrice: payrollPrices.BR00165, total: br00165Count * payrollPrices.BR00165 }
            }
        };
    } catch (error) {
        console.error('Error calculating BR00069 custom price:', error);
        throw error;
    }
}