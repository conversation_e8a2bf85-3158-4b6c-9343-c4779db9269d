import { json, error } from '@sveltejs/kit';
import { env } from '$env/dynamic/private';

// Import our new modules
import {
    getDealLineItemsWithDetails,
    getDealProperties,
    updateDealAmount,
    updateDealProperties,
    findProductBySku
} from '$lib/hubspot-api.js';

import {
    findLineItemBySku,
    findLineItemBySkuSafe,
    validateSkuInProductLibrary,
    createAndAssociateLineItem,
    updateLineItemQuantity,
    deleteLineItem,
    manageLineItemQuantity,
    manageBooleanLineItem,
    manageLineItemWithCustomPrice,
    calculateBR00069CustomPrice,
    createLineItemWithQuantity,
    createLineItemFromProductWithQuantity,
    associateLineItemWithDeal,
    updateAccountingPackagesToOptimal,
    sortLineItemsByBRNumber
} from '$lib/line-item-manager.js';



import {
    handleComprehensiveUpdate
} from '$lib/business-logic-handlers.js';

import {
    SKUS,
    validateRequestParams,
    getPropertyTypeFlags,
    isSupportedProperty,
    setupConsoleCapture,
    DOCUMENT_FIELDS,
    calculateDocumentSum,
    isFullAccountingType,
    calculateBaseDocumentQuantity,
    calculateBR00013Quantity
} from '$lib/validation-utils.js';

// Constants for repeated arrays
const ALL_ECOMMERCE_SKUS = [SKUS.BR00058, SKUS.BR00059, SKUS.BR00060, SKUS.BR00061, SKUS.BR00090, SKUS.BR00091, SKUS.BR00092, SKUS.BR00093];
const ALL_ADDITIONAL_ECOMMERCE_SKUS = [SKUS.BR00166, SKUS.BR00167, SKUS.BR00168, SKUS.BR00169, SKUS.BR00170, SKUS.BR00171, SKUS.BR00172, SKUS.BR00173];

// Note: CASH_BANK_OPERATIONS, BOOKING_OPERATIONS, and ACCOUNTING_REQUIRED_PROPERTIES
// are now imported from DOCUMENT_FIELDS in validation-utils.js

/**
 * SvelteKit POST endpoint handler for HubSpot line item operations using comprehensive update trigger
 * @param {import('@sveltejs/kit').RequestEvent} event - The request event
 * @returns {Promise<Response>} - JSON response with the operation result
 */
export async function POST({ request, url }) {
    // Setup console logging capture
    const { consoleLogs, restore } = setupConsoleCapture();

    try {
        // Check API key
        const apiKey = request.headers.get('x-api-key');
        const expectedApiKey = env.API_KEY;

        if (!apiKey || apiKey !== expectedApiKey) {
            throw error(401, 'Unauthorized: Invalid or missing API key');
        }

        // Get HubSpot access token from environment
        const accessToken = env.HUBSPOT_ACCESS_TOKEN;
        if (!accessToken) {
            throw error(500, 'Server Error: HubSpot access token not configured');
        }

        // Extract request data from JSON body
        let requestData = {};

        try {
            requestData = await request.json();
        } catch (err) {
            restore();
            return json({
                success: false,
                error: 'Invalid JSON in request body',
                consoleLogs: consoleLogs
            }, { status: 400 });
        }

        // Validate request parameters
        const validation = validateRequestParams(requestData);
        if (!validation.success) {
            restore();
            return json({
                success: false,
                error: validation.error,
                consoleLogs: consoleLogs
            }, { status: validation.statusCode });
        }

        const { dealId, propertyValue, propertyName } = requestData;

        // Check property type support
        const propertyFlags = getPropertyTypeFlags(propertyName);
        if (!isSupportedProperty(propertyFlags)) {
            restore();
            return json({
                success: true,
                message: 'Property is not supported for line item changes',
                action: 'none',
                consoleLogs: consoleLogs
            });
        }

        // TODO: Remove this testing condition - only process deals with description "cascasc"
        // This is for testing purposes only
        const dealProperties = await getDealProperties(dealId, accessToken, ['description']);
        const dealDescription = dealProperties.description || '';

        if (dealDescription !== 'cascasc') {
            restore();
            return json({
                success: true,
                message: 'Deal skipped - not in testing mode (description must be "cascasc")',
                action: 'skipped',
                dealDescription,
                consoleLogs: consoleLogs
            });
        }

        // Get existing line items for the deal
        const lineItems = await getDealLineItemsWithDetails(dealId, accessToken);

        // Process business logic based on property type
        let businessLogicResult;

        // Check if this is the update trigger property
        if (propertyFlags.isUpdateTrigger) {
            businessLogicResult = await handleComprehensiveUpdate(propertyName, propertyValue, dealId, accessToken);
        } else {
            throw new Error(`Unsupported property: ${propertyName}. Only 'aktualizuj_dane' field is supported for updates.`);
        }

        // Extract business logic results
        const {
            shouldHaveBR00032,
            shouldHaveBR00033,
            br00032Quantity,
            br00033Quantity,
            shouldHaveBR00129,
            br00013Quantity = 0,
            br00013BankStatementQuantity = 0,
            br00030Quantity = 0,
            br00031Quantity = 0,
            br00077Quantity = 0,
            br00080Quantity = 0,
            br00165Quantity = 0,
            mobilityPackageQuantity = 0,
            br00069Quantity = 0,
            br00069OverridePrice = null,
            br00070Quantity = 0,
            br00071Quantity = 0,
            br00072Quantity = 0,
            br00073Quantity = 0,
            br00074Quantity = 0,
            br00075Quantity = 0,
            shouldHaveBR00078 = false,
            shouldHaveBR00079 = false,
            shouldHaveBR00111 = false,
            // New KADRY-related properties
            br00114Quantity = 0,
            shouldHaveBR00115 = false,
            shouldHaveBR00117 = false,
            shouldHaveBR00118 = false,
            shouldHaveBR00119 = false,
            shouldHaveBR00081 = false,
            br00012Quantity = 0,
            br00130Quantity = 0,
            accountingPackages = [],
            isFullAccounting = false,
            selectedPackageName = null,
            ecommercePackage = null,
            pitPackages = [],
            pitRefreshed = false,
            selectedPitPackage = null,
            financialStatementPackages = [],
            financialStatementRefreshed = false,
            selectedFinancialStatementPackage = null
        } = businessLogicResult;

        // Extract e-commerce package information from nested object
        const selectedEcommercePackage = ecommercePackage?.selectedPackage || null;
        const ecommercePackageQuantity = selectedEcommercePackage ? 1 : 0;
        const additionalTransactions = ecommercePackage?.additionalTransactions || 0;
        const totalEcommerceCost = ecommercePackage?.totalCost || 0;
        const additionalTransactionSku = ecommercePackage?.additionalSku || null;
        const additionalTransactionQuantity = additionalTransactions;

        // DEBUG: Log VAT processing results from comprehensive update








        // Check existing line items using safe method
        const br00032LineItem = findLineItemBySkuSafe(lineItems, SKUS.BR00032);
        const br00033LineItem = findLineItemBySkuSafe(lineItems, SKUS.BR00033);

        const existingLineItems = {
            BR00032: br00032LineItem,
            BR00033: br00033LineItem,
            BR00129: findLineItemBySkuSafe(lineItems, SKUS.BR00129),
            BR00030: findLineItemBySkuSafe(lineItems, SKUS.BR00030),
            BR00031: findLineItemBySkuSafe(lineItems, SKUS.BR00031),
            BR00076: findLineItemBySkuSafe(lineItems, SKUS.BR00076),
            BR00077: findLineItemBySkuSafe(lineItems, SKUS.BR00077),
            BR00080: findLineItemBySkuSafe(lineItems, SKUS.BR00080),
            BR00165: findLineItemBySkuSafe(lineItems, SKUS.BR00165),
            BR00069: findLineItemBySkuSafe(lineItems, SKUS.BR00069),
            BR00070: findLineItemBySkuSafe(lineItems, SKUS.BR00070),
            BR00071: findLineItemBySkuSafe(lineItems, SKUS.BR00071),
            BR00072: findLineItemBySkuSafe(lineItems, SKUS.BR00072),
            BR00073: findLineItemBySkuSafe(lineItems, SKUS.BR00073),
            BR00074: findLineItemBySkuSafe(lineItems, SKUS.BR00074),
            BR00075: findLineItemBySkuSafe(lineItems, SKUS.BR00075),
            BR00078: findLineItemBySkuSafe(lineItems, SKUS.BR00078),
            BR00079: findLineItemBySkuSafe(lineItems, SKUS.BR00079),
            BR00111: findLineItemBySkuSafe(lineItems, SKUS.BR00111),
            BR00012: findLineItemBySkuSafe(lineItems, SKUS.BR00012),
            BR00130: findLineItemBySkuSafe(lineItems, SKUS.BR00130),
            // E-commerce packages
            BR00058: findLineItemBySkuSafe(lineItems, SKUS.BR00058),
            BR00059: findLineItemBySkuSafe(lineItems, SKUS.BR00059),
            BR00060: findLineItemBySkuSafe(lineItems, SKUS.BR00060),
            BR00061: findLineItemBySkuSafe(lineItems, SKUS.BR00061),
            BR00090: findLineItemBySkuSafe(lineItems, SKUS.BR00090),
            BR00091: findLineItemBySkuSafe(lineItems, SKUS.BR00091),
            BR00092: findLineItemBySkuSafe(lineItems, SKUS.BR00092),
            BR00093: findLineItemBySkuSafe(lineItems, SKUS.BR00093),
            // Additional e-commerce transaction SKUs
            BR00166: findLineItemBySkuSafe(lineItems, SKUS.BR00166),
            BR00167: findLineItemBySkuSafe(lineItems, SKUS.BR00167),
            BR00168: findLineItemBySkuSafe(lineItems, SKUS.BR00168),
            BR00169: findLineItemBySkuSafe(lineItems, SKUS.BR00169),
            BR00170: findLineItemBySkuSafe(lineItems, SKUS.BR00170),
            BR00171: findLineItemBySkuSafe(lineItems, SKUS.BR00171),
            BR00172: findLineItemBySkuSafe(lineItems, SKUS.BR00172),
            BR00173: findLineItemBySkuSafe(lineItems, SKUS.BR00173),
            // PIT packages for simplified accounting
            BR00094: findLineItemBySkuSafe(lineItems, SKUS.BR00094),
            BR00095: findLineItemBySkuSafe(lineItems, SKUS.BR00095),
            BR00096: findLineItemBySkuSafe(lineItems, SKUS.BR00096),
            BR00097: findLineItemBySkuSafe(lineItems, SKUS.BR00097),
            // Financial statement packages
            BR00099: findLineItemBySkuSafe(lineItems, SKUS.BR00099),
            BR00100: findLineItemBySkuSafe(lineItems, SKUS.BR00100),
            BR00101: findLineItemBySkuSafe(lineItems, SKUS.BR00101),
            BR00102: findLineItemBySkuSafe(lineItems, SKUS.BR00102),
            // New KADRY-related SKUs
            BR00114: findLineItemBySkuSafe(lineItems, SKUS.BR00114),
            BR00115: findLineItemBySkuSafe(lineItems, SKUS.BR00115),
            BR00117: findLineItemBySkuSafe(lineItems, SKUS.BR00117),
            BR00118: findLineItemBySkuSafe(lineItems, SKUS.BR00118),
            BR00119: findLineItemBySkuSafe(lineItems, SKUS.BR00119),
            BR00081: findLineItemBySkuSafe(lineItems, SKUS.BR00081)
        };



        // For comprehensive updates, clean up all managed line items first (except accounting and e-commerce which are handled separately)
        if (propertyFlags.isUpdateTrigger) {

            // Define all SKUs that we manage (excluding accounting and e-commerce packages which are handled separately)
            const managedSkus = [
                // VAT SKUs
                'BR00032', 'BR00033',
                // Language SKUs
                'BR00129',
                // Quantity-based SKUs
                'BR00030', 'BR00031', 'BR00076', 'BR00077', 'BR00080', 'BR00165',
                // Payroll SKUs
                'BR00069', 'BR00070', 'BR00071', 'BR00072', 'BR00073', 'BR00074', 'BR00075', 'BR00078', 'BR00079',
                // New KADRY-related SKUs
                'BR00114', 'BR00115', 'BR00117', 'BR00118', 'BR00119', 'BR00081',
                // MSP SKUs
                'BR00111',
                // Banking SKUs
                'BR00012', 'BR00130',
                // PIT packages for simplified accounting
                'BR00094', 'BR00095', 'BR00096', 'BR00097',
                // Financial statement packages for full accounting
                'BR00099', 'BR00100', 'BR00101', 'BR00102'
                // Note: BR00013, accounting packages, and e-commerce packages are handled in their own sections
            ];

            for (const sku of managedSkus) {
                const existingItem = existingLineItems[sku];
                if (existingItem) {
                    await deleteLineItem(existingItem.id, accessToken);
                    // Update the existingLineItems object to reflect the deletion
                    existingLineItems[sku] = null;
                }
            }
        }

        // Handle VAT line items for comprehensive updates
        if (propertyFlags.isUpdateTrigger) {
            // Handle VAT line items using unified function that works with product library
            await manageLineItemQuantity(SKUS.BR00032, br00032Quantity, existingLineItems.BR00032, dealId, accessToken);
            await manageLineItemQuantity(SKUS.BR00033, br00033Quantity, existingLineItems.BR00033, dealId, accessToken);
        }

        // Handle language line items for comprehensive updates
        if (propertyFlags.isUpdateTrigger) {
            // Handle language line item using unified function that works with product library
            await manageBooleanLineItem(SKUS.BR00129, shouldHaveBR00129, existingLineItems.BR00129, dealId, accessToken);
        }

        // Handle quantity-based line items for comprehensive updates
        if (propertyFlags.isUpdateTrigger) {
            // Handle comprehensive update - process non-payroll quantity-based line items only
            // Note: Payroll items (BR00077, BR00078, BR00080, BR00165) are handled in the payroll section to avoid duplicates
            await manageLineItemQuantity(SKUS.BR00030, br00030Quantity, existingLineItems.BR00030, dealId, accessToken);
            await manageLineItemQuantity(SKUS.BR00031, br00031Quantity, existingLineItems.BR00031, dealId, accessToken);

            // Only create BR00076 if not using Ryczałt package (which includes BR00076 in BR00069)
            const currentPayrollPackage = businessLogicResult.pakietKadrowoPlacowy || '';
            if (currentPayrollPackage !== 'Ryczałt') {
                await manageLineItemQuantity(SKUS.BR00076, mobilityPackageQuantity, existingLineItems.BR00076, dealId, accessToken);
            } else {
                // Remove BR00076 if it exists (it's included in Ryczałt BR00069)
                if (existingLineItems.BR00076) {
                    await deleteLineItem(existingLineItems.BR00076.id, accessToken);
                }
            }
        }

        // Handle payroll line items for comprehensive updates
        if (propertyFlags.isUpdateTrigger) {
            // Handle BR00069 (Ryczałt) with custom price instead of quantity
            if (br00069Quantity > 0) {
                let br00069CustomPrice;

                // Use override price from business logic if available, otherwise calculate dynamically
                if (br00069OverridePrice !== null && br00069OverridePrice > 0) {
                    br00069CustomPrice = br00069OverridePrice;
                } else {
                    // Fallback to dynamic calculation
                    const { customPrice } = await calculateBR00069CustomPrice(dealId, accessToken);
                    br00069CustomPrice = customPrice;
                }

                await manageLineItemWithCustomPrice(SKUS.BR00069, br00069CustomPrice, existingLineItems.BR00069, dealId, accessToken);
            } else {
                // Remove BR00069 if quantity is 0
                await manageLineItemWithCustomPrice(SKUS.BR00069, 0, existingLineItems.BR00069, dealId, accessToken);
            }

            // Handle other payroll package line items with regular quantity management
            await manageLineItemQuantity(SKUS.BR00070, br00070Quantity, existingLineItems.BR00070, dealId, accessToken);
            await manageLineItemQuantity(SKUS.BR00071, br00071Quantity, existingLineItems.BR00071, dealId, accessToken);
            await manageLineItemQuantity(SKUS.BR00072, br00072Quantity, existingLineItems.BR00072, dealId, accessToken);
            await manageLineItemQuantity(SKUS.BR00073, br00073Quantity, existingLineItems.BR00073, dealId, accessToken);
            await manageLineItemQuantity(SKUS.BR00074, br00074Quantity, existingLineItems.BR00074, dealId, accessToken);
            await manageLineItemQuantity(SKUS.BR00075, br00075Quantity, existingLineItems.BR00075, dealId, accessToken);

            // Handle payroll-related line items (but not for Ryczałt package which includes them in BR00069)
            const currentPayrollPackage = businessLogicResult.pakietKadrowoPlacowy || '';
            if (currentPayrollPackage !== 'Ryczałt') {
                await manageLineItemQuantity(SKUS.BR00077, br00077Quantity, existingLineItems.BR00077, dealId, accessToken);
                await manageLineItemQuantity(SKUS.BR00080, br00080Quantity, existingLineItems.BR00080, dealId, accessToken);
                await manageLineItemQuantity(SKUS.BR00165, br00165Quantity, existingLineItems.BR00165, dealId, accessToken);

                // Handle boolean payroll line items
                await manageBooleanLineItem(SKUS.BR00078, shouldHaveBR00078, existingLineItems.BR00078, dealId, accessToken);
                await manageBooleanLineItem(SKUS.BR00079, shouldHaveBR00079, existingLineItems.BR00079, dealId, accessToken);
            } else {
                // For Ryczałt package, remove these items if they exist (they're included in BR00069)
                const ryczaltItemsToRemove = [
                    existingLineItems.BR00077, existingLineItems.BR00078, existingLineItems.BR00079, existingLineItems.BR00080, existingLineItems.BR00165
                ].filter(item => item);

                for (const item of ryczaltItemsToRemove) {
                    await deleteLineItem(item.id, accessToken);
                }
            }

            // Handle new KADRY-related line items
            await manageLineItemQuantity(SKUS.BR00114, br00114Quantity, existingLineItems.BR00114, dealId, accessToken);
            await manageBooleanLineItem(SKUS.BR00115, shouldHaveBR00115, existingLineItems.BR00115, dealId, accessToken);
            await manageBooleanLineItem(SKUS.BR00117, shouldHaveBR00117, existingLineItems.BR00117, dealId, accessToken);
            await manageBooleanLineItem(SKUS.BR00118, shouldHaveBR00118, existingLineItems.BR00118, dealId, accessToken);
            await manageBooleanLineItem(SKUS.BR00119, shouldHaveBR00119, existingLineItems.BR00119, dealId, accessToken);
            await manageBooleanLineItem(SKUS.BR00081, shouldHaveBR00081, existingLineItems.BR00081, dealId, accessToken);


        }

        // Handle MSP line items for comprehensive updates
        if (propertyFlags.isUpdateTrigger) {
            await manageBooleanLineItem(SKUS.BR00111, shouldHaveBR00111, existingLineItems.BR00111, dealId, accessToken);

            // Handle MSP team accounts BR00013 calculation
            if (br00013Quantity > 0) {
                const currentLineItems = await getDealLineItemsWithDetails(dealId, accessToken);
                const existingBR00013 = findLineItemBySkuSafe(currentLineItems, 'BR00013');

                if (existingBR00013) {
                    const currentQuantity = parseInt(existingBR00013.properties.quantity) || 1;
                    if (currentQuantity !== br00013Quantity) {
                        await updateLineItemQuantity(existingBR00013.id, br00013Quantity, accessToken);
                    }
                } else {
                    await createAndAssociateLineItem('BR00013', br00013Quantity, dealId, accessToken);
                }
            }
        }

        // Handle banking line items for comprehensive updates
        if (propertyFlags.isUpdateTrigger) {
            await manageLineItemQuantity(SKUS.BR00012, br00012Quantity, existingLineItems.BR00012, dealId, accessToken);
            await manageLineItemQuantity(SKUS.BR00130, br00130Quantity, existingLineItems.BR00130, dealId, accessToken);
        }

        // Handle e-commerce line items (for comprehensive updates only)
        if (propertyFlags.isUpdateTrigger) {
            // Remove all e-commerce packages
            for (const sku of ALL_ECOMMERCE_SKUS) {
                const existingItem = existingLineItems[sku];
                if (existingItem) {
                    await deleteLineItem(existingItem.id, accessToken);
                }
            }

            // Remove all additional transaction SKUs
            for (const sku of ALL_ADDITIONAL_ECOMMERCE_SKUS) {
                const existingItem = existingLineItems[sku];
                if (existingItem) {
                    await deleteLineItem(existingItem.id, accessToken);
                }
            }

            if (selectedEcommercePackage) {
                // Add the selected e-commerce package (comprehensive update - existing packages already cleaned up)
                await manageLineItemQuantity(selectedEcommercePackage, ecommercePackageQuantity, null, dealId, accessToken);

                // Handle additional transaction costs
                if (additionalTransactionSku && additionalTransactionQuantity > 0) {
                    // Add the additional transaction SKU (comprehensive update - existing items already cleaned up)
                    await manageLineItemQuantity(additionalTransactionSku, additionalTransactionQuantity, null, dealId, accessToken);
                }
            }
        }

        // Handle PIT packages for comprehensive updates (simplified accounting only)
        if (propertyFlags.isUpdateTrigger && pitRefreshed) {
            // Define all PIT SKUs for cleanup
            const ALL_PIT_SKUS = [SKUS.BR00094, SKUS.BR00095, SKUS.BR00096, SKUS.BR00097];

            // Remove all existing PIT packages first (comprehensive update)
            for (const sku of ALL_PIT_SKUS) {
                const existingItem = existingLineItems[sku];
                if (existingItem) {
                    await deleteLineItem(existingItem.id, accessToken);
                }
            }

            // Add new PIT packages if any (only for simplified accounting)
            if (!isFullAccounting && pitPackages && pitPackages.length > 0) {
                for (const pitPackage of pitPackages) {
                    await manageLineItemQuantity(pitPackage.sku, pitPackage.quantity, null, dealId, accessToken);
                }
            }
        }

        // NOTE: Financial statement packages will be calculated AFTER all other line items are created
        // This is moved to after all line items are processed to calculate based on actual line item costs



        // Handle accounting packages for comprehensive updates
        if (propertyFlags.isUpdateTrigger) {
            // Note: We don't remove BR00013 items here anymore since we manage them separately
            // BR00013 items are handled in their own section below

            // For comprehensive updates, always clean up ALL accounting packages first
            const allAccountingItems = lineItems.filter(item => {
                const sku = item.properties.hs_sku || item.properties.name || '';
                return [
                    'BR00003', 'BR00004', 'BR00005', 'BR00006', // Full accounting packages
                    'BR00007', 'BR00008', 'BR00009', 'BR00010', // Simplified accounting packages
                    'BR00015', 'BR00016', 'BR00017', 'BR00018', // Simplified individual documents
                    'BR00019', 'BR00020', 'BR00021', // Simplified additional packs
                    'BR00022', 'BR00023', 'BR00024', 'BR00025', // Full individual documents
                    'BR00027', 'BR00028', 'BR00029' // Full additional packs
                ].includes(sku);
            });

            for (const item of allAccountingItems) {
                await deleteLineItem(item.id, accessToken);
            }

            // Use the already calculated optimal packages from business logic result
            const optimalPackages = accountingPackages || [];

            if (br00013Quantity > 0 && optimalPackages.length > 0) {
                // For comprehensive updates, we start with empty list since we cleaned up above
                const currentAccountingItems = [];

                // Use the optimal packages configuration to create line items
                await updateAccountingPackagesToOptimal(currentAccountingItems, optimalPackages, dealId, accessToken);
            }
        }

            // Handle BR00013 processing with new logic (uses max of bookings or cash-bank)
            // For comprehensive updates, clean up BR00013 first
            const currentLineItems = await getDealLineItemsWithDetails(dealId, accessToken);
            const existingBR00013Items = currentLineItems.filter(item => {
                const sku = item.properties.hs_sku || item.properties.name || '';
                return sku === 'BR00013';
            });

            for (const item of existingBR00013Items) {
                await deleteLineItem(item.id, accessToken);
            }

            // Use the new br00013Quantity (which is max of bookings or cash-bank) instead of br00013BankStatementQuantity
            if (br00013Quantity > 0) {
                // Create new BR00013 line item (this will be the case for comprehensive updates)
                await manageLineItemQuantity('BR00013', br00013Quantity, null, dealId, accessToken);
            }
        }

        // Handle financial statement packages for comprehensive updates (full accounting only)
        // This is done AFTER all other line items are created to calculate based on actual line item costs
        if (propertyFlags.isUpdateTrigger && financialStatementRefreshed) {
            // Define all financial statement SKUs for cleanup
            const ALL_FINANCIAL_STATEMENT_SKUS = [SKUS.BR00099, SKUS.BR00100, SKUS.BR00101, SKUS.BR00102];

            // Remove all existing financial statement packages first (comprehensive update)
            for (const sku of ALL_FINANCIAL_STATEMENT_SKUS) {
                const existingItem = existingLineItems[sku];
                if (existingItem) {
                    await deleteLineItem(existingItem.id, accessToken);
                }
            }

            // Add new financial statement packages if any (only for full accounting)
            if (isFullAccounting && selectedPackageName) {

                // Get current line items after all other processing
                const currentLineItems = await getDealLineItemsWithDetails(dealId, accessToken);

                // Convert line items to the format expected by the calculation function
                const createdLineItems = currentLineItems.map(item => ({
                    sku: item.properties.hs_sku || item.properties.name || '',
                    price: parseFloat(item.properties.price) || 0,
                    quantity: parseInt(item.properties.quantity) || 1
                }));

                // Import and use the financial statement calculation function
                const { selectFinancialStatementPackageForFullAccounting } = await import('$lib/financial-statement-packages.js');
                const dealProperties = await getDealProperties(dealId, accessToken, DOCUMENT_FIELDS.ALL_DOCUMENT_FIELDS);

                // Calculate financial statement packages with actual line item costs
                const recalculatedFinancialStatementPackages = await selectFinancialStatementPackageForFullAccounting(
                    selectedPackageName,
                    dealProperties,
                    accessToken,
                    createdLineItems
                );

                // Create the financial statement line items
                for (const financialStatementPackage of recalculatedFinancialStatementPackages) {
                    if (financialStatementPackage.customPrice) {
                        // Use custom price for financial statement packages
                        await manageLineItemWithCustomPrice(financialStatementPackage.sku, financialStatementPackage.price, null, dealId, accessToken);
                    } else {
                        await manageLineItemQuantity(financialStatementPackage.sku, financialStatementPackage.quantity, null, dealId, accessToken);
                    }
                }
            }
        }

        // Update deal amount with calculated total from all line items
        let totalAmount = 0;
        try {
            totalAmount = await updateDealAmount(dealId, accessToken);
        } catch (error) {
            console.error('Failed to update deal amount:', error);
            // Continue processing even if amount update fails
        }

        // Prepare response
        const actions = [];

        // VAT line item actions with quantities
        if (br00032Quantity > 0) {
            if (existingLineItems.BR00032) {
                const currentQuantity = parseInt(existingLineItems.BR00032.properties.quantity) || 1;
                if (currentQuantity !== br00032Quantity) {
                    actions.push(`Updated BR00032 quantity to ${br00032Quantity}`);
                }
            } else {
                actions.push(`Created BR00032 with quantity ${br00032Quantity}`);
            }
        } else if (existingLineItems.BR00032) {
            actions.push('Deleted BR00032');
        }

        if (br00033Quantity > 0) {
            if (existingLineItems.BR00033) {
                const currentQuantity = parseInt(existingLineItems.BR00033.properties.quantity) || 1;
                if (currentQuantity !== br00033Quantity) {
                    actions.push(`Updated BR00033 quantity to ${br00033Quantity}`);
                }
            } else {
                actions.push(`Created BR00033 with quantity ${br00033Quantity}`);
            }
        } else if (existingLineItems.BR00033) {
            actions.push('Deleted BR00033');
        }

        if (shouldHaveBR00129 && !existingLineItems.BR00129) actions.push('Created BR00129');
        if (!shouldHaveBR00129 && existingLineItems.BR00129) actions.push('Deleted BR00129');



        // Add payroll line item actions for comprehensive updates
        if (propertyFlags.isUpdateTrigger) {
            if (br00070Quantity > 0) {
                if (existingLineItems.BR00070) {
                    const currentQuantity = parseInt(existingLineItems.BR00070.properties.quantity) || 1;
                    if (currentQuantity !== br00070Quantity) {
                        actions.push(`Updated BR00070 quantity to ${br00070Quantity}`);
                    }
                } else {
                    actions.push(`Created BR00070 with quantity ${br00070Quantity}`);
                }
            } else if (existingLineItems.BR00070) {
                actions.push('Deleted BR00070');
            }

            if (br00072Quantity > 0) {
                if (existingLineItems.BR00072) {
                    const currentQuantity = parseInt(existingLineItems.BR00072.properties.quantity) || 1;
                    if (currentQuantity !== br00072Quantity) {
                        actions.push(`Updated BR00072 quantity to ${br00072Quantity}`);
                    }
                } else {
                    actions.push(`Created BR00072 with quantity ${br00072Quantity}`);
                }
            } else if (existingLineItems.BR00072) {
                actions.push('Deleted BR00072');
            }

            if (br00073Quantity > 0) {
                if (existingLineItems.BR00073) {
                    const currentQuantity = parseInt(existingLineItems.BR00073.properties.quantity) || 1;
                    if (currentQuantity !== br00073Quantity) {
                        actions.push(`Updated BR00073 quantity to ${br00073Quantity}`);
                    }
                } else {
                    actions.push(`Created BR00073 with quantity ${br00073Quantity}`);
                }
            } else if (existingLineItems.BR00073) {
                actions.push('Deleted BR00073');
            }

            if (br00075Quantity > 0) {
                if (existingLineItems.BR00075) {
                    const currentQuantity = parseInt(existingLineItems.BR00075.properties.quantity) || 1;
                    if (currentQuantity !== br00075Quantity) {
                        actions.push(`Updated BR00075 quantity to ${br00075Quantity}`);
                    }
                } else {
                    actions.push(`Created BR00075 with quantity ${br00075Quantity}`);
                }
            } else if (existingLineItems.BR00075) {
                actions.push('Deleted BR00075');
            }

            if (shouldHaveBR00078 && !existingLineItems.BR00078) actions.push('Created BR00078');
            if (!shouldHaveBR00078 && existingLineItems.BR00078) actions.push('Deleted BR00078');

            if (shouldHaveBR00079 && !existingLineItems.BR00079) actions.push('Created BR00079');
            if (!shouldHaveBR00079 && existingLineItems.BR00079) actions.push('Deleted BR00079');
        }

        // Add MSP line item actions for comprehensive updates
        if (propertyFlags.isUpdateTrigger) {
            if (shouldHaveBR00111 && !existingLineItems.BR00111) actions.push('Created BR00111');
            if (!shouldHaveBR00111 && existingLineItems.BR00111) actions.push('Deleted BR00111');
        }

        const actionMessage = actions.length > 0 ? actions.join(', ') : 'No changes needed';

        // Determine primary property type based on flags
        let primaryPropertyType;
        if (propertyFlags.isUpdateTrigger) {
            primaryPropertyType = 'comprehensive';
        } else {
            primaryPropertyType = 'unknown';
        }

        // Determine processing type for response message
        let processingType;
        switch (primaryPropertyType) {
            case 'comprehensive':
                processingType = 'Comprehensive update';
                break;

            case 'unknown':
            default:
                processingType = 'Property';
                break;
        }

        const response = {
            success: true,
            message: `${processingType} processing completed. ${actionMessage}`,
            actions: {
                BR00032: br00032Quantity > 0 ?
                    (existingLineItems.BR00032 ?
                        (parseInt(existingLineItems.BR00032.properties.quantity) !== br00032Quantity ? `updated_quantity_${br00032Quantity}` : `exists_quantity_${br00032Quantity}`) :
                        `created_quantity_${br00032Quantity}`) :
                    (existingLineItems.BR00032 ? 'deleted' : 'none'),
                BR00033: br00033Quantity > 0 ?
                    (existingLineItems.BR00033 ?
                        (parseInt(existingLineItems.BR00033.properties.quantity) !== br00033Quantity ? `updated_quantity_${br00033Quantity}` : `exists_quantity_${br00033Quantity}`) :
                        `created_quantity_${br00033Quantity}`) :
                    (existingLineItems.BR00033 ? 'deleted' : 'none'),
                BR00129: shouldHaveBR00129 ? (existingLineItems.BR00129 ? 'exists' : 'created') : (existingLineItems.BR00129 ? 'deleted' : 'none')
            },
            propertyName,
            propertyValue,
            totalAmount: totalAmount,
            br00032Quantity: br00032Quantity,
            br00033Quantity: br00033Quantity,
            consoleLogs: consoleLogs
        };

        // Add quantity-based line item status to response for comprehensive updates
        if (propertyFlags.isUpdateTrigger) {
            if (br00030Quantity > 0) {
                response.actions.BR00030 = existingLineItems.BR00030 ?
                    (parseInt(existingLineItems.BR00030.properties.quantity) !== br00030Quantity ? `updated_quantity_${br00030Quantity}` : `exists_quantity_${br00030Quantity}`) :
                    `created_quantity_${br00030Quantity}`;
                response.br00030Quantity = br00030Quantity;
            } else {
                response.actions.BR00030 = existingLineItems.BR00030 ? 'deleted' : 'none';
                response.br00030Quantity = 0;
            }

            if (br00031Quantity > 0) {
                response.actions.BR00031 = existingLineItems.BR00031 ?
                    (parseInt(existingLineItems.BR00031.properties.quantity) !== br00031Quantity ? `updated_quantity_${br00031Quantity}` : `exists_quantity_${br00031Quantity}`) :
                    `created_quantity_${br00031Quantity}`;
                response.br00031Quantity = br00031Quantity;
            } else {
                response.actions.BR00031 = existingLineItems.BR00031 ? 'deleted' : 'none';
                response.br00031Quantity = 0;
            }

            if (mobilityPackageQuantity > 0) {
                response.actions.BR00076 = existingLineItems.BR00076 ?
                    (parseInt(existingLineItems.BR00076.properties.quantity) !== mobilityPackageQuantity ? `updated_quantity_${mobilityPackageQuantity}` : `exists_quantity_${mobilityPackageQuantity}`) :
                    `created_quantity_${mobilityPackageQuantity}`;
                response.mobilityPackageQuantity = mobilityPackageQuantity;
            } else {
                response.actions.BR00076 = existingLineItems.BR00076 ? 'deleted' : 'none';
                response.mobilityPackageQuantity = 0;
            }

            if (br00077Quantity > 0) {
                response.actions.BR00077 = existingLineItems.BR00077 ?
                    (parseInt(existingLineItems.BR00077.properties.quantity) !== br00077Quantity ? `updated_quantity_${br00077Quantity}` : `exists_quantity_${br00077Quantity}`) :
                    `created_quantity_${br00077Quantity}`;
                response.br00077Quantity = br00077Quantity;
            } else {
                response.actions.BR00077 = existingLineItems.BR00077 ? 'deleted' : 'none';
                response.br00077Quantity = 0;
            }

            if (br00080Quantity > 0) {
                response.actions.BR00080 = existingLineItems.BR00080 ?
                    (parseInt(existingLineItems.BR00080.properties.quantity) !== br00080Quantity ? `updated_quantity_${br00080Quantity}` : `exists_quantity_${br00080Quantity}`) :
                    `created_quantity_${br00080Quantity}`;
                response.br00080Quantity = br00080Quantity;
            } else {
                response.actions.BR00080 = existingLineItems.BR00080 ? 'deleted' : 'none';
                response.br00080Quantity = 0;
            }

            // Add BR00165 quantity-based line item status
            if (br00165Quantity > 0) {
                response.actions.BR00165 = existingLineItems.BR00165 ?
                    (parseInt(existingLineItems.BR00165.properties.quantity) !== br00165Quantity ? `updated_quantity_${br00165Quantity}` : `exists_quantity_${br00165Quantity}`) :
                    `created_quantity_${br00165Quantity}`;
                response.br00165Quantity = br00165Quantity;
            } else {
                response.actions.BR00165 = existingLineItems.BR00165 ? 'deleted' : 'none';
                response.br00165Quantity = 0;
            }

            // Add payroll line item status to response for comprehensive updates
            // BR00069 (Ryczałt package) - uses custom price instead of quantity
            if (br00069Quantity > 0) {
                let br00069CustomPrice, breakdown;

                // Use the same logic as in the processing section
                if (br00069OverridePrice !== null && br00069OverridePrice > 0) {
                    br00069CustomPrice = br00069OverridePrice;
                    breakdown = { source: 'override_price', value: br00069OverridePrice };
                } else {
                    // Fallback to dynamic calculation for response
                    const calculationResult = await calculateBR00069CustomPrice(dealId, accessToken);
                    br00069CustomPrice = calculationResult.customPrice;
                    breakdown = calculationResult.breakdown;
                }

                response.actions.BR00069 = existingLineItems.BR00069 ?
                    (parseFloat(existingLineItems.BR00069.properties.price) !== br00069CustomPrice ? `updated_price_${br00069CustomPrice}` : `exists_price_${br00069CustomPrice}`) :
                    `created_price_${br00069CustomPrice}`;
                response.br00069CustomPrice = br00069CustomPrice;
                response.br00069Quantity = br00069Quantity; // Keep for reference
                response.br00069Breakdown = breakdown;
                response.br00069OverridePrice = br00069OverridePrice; // Include override price in response
            } else {
                response.actions.BR00069 = existingLineItems.BR00069 ? 'deleted' : 'none';
                response.br00069CustomPrice = 0;
                response.br00069Quantity = 0;
                response.br00069OverridePrice = null;
            }

            // BR00070 (PREMIUM umowa o pracę)
            if (br00070Quantity > 0) {
                response.actions.BR00070 = existingLineItems.BR00070 ?
                    (parseInt(existingLineItems.BR00070.properties.quantity) !== br00070Quantity ? `updated_quantity_${br00070Quantity}` : `exists_quantity_${br00070Quantity}`) :
                    `created_quantity_${br00070Quantity}`;
                response.br00070Quantity = br00070Quantity;
            } else {
                response.actions.BR00070 = existingLineItems.BR00070 ? 'deleted' : 'none';
                response.br00070Quantity = 0;
            }

            // BR00071 (PREMIUM umowy cywilnoprawne)
            if (br00071Quantity > 0) {
                response.actions.BR00071 = existingLineItems.BR00071 ?
                    (parseInt(existingLineItems.BR00071.properties.quantity) !== br00071Quantity ? `updated_quantity_${br00071Quantity}` : `exists_quantity_${br00071Quantity}`) :
                    `created_quantity_${br00071Quantity}`;
                response.br00071Quantity = br00071Quantity;
            } else {
                response.actions.BR00071 = existingLineItems.BR00071 ? 'deleted' : 'none';
                response.br00071Quantity = 0;
            }

            if (br00072Quantity > 0) {
                response.actions.BR00072 = existingLineItems.BR00072 ?
                    (parseInt(existingLineItems.BR00072.properties.quantity) !== br00072Quantity ? `updated_quantity_${br00072Quantity}` : `exists_quantity_${br00072Quantity}`) :
                    `created_quantity_${br00072Quantity}`;
                response.br00072Quantity = br00072Quantity;
            } else {
                response.actions.BR00072 = existingLineItems.BR00072 ? 'deleted' : 'none';
                response.br00072Quantity = 0;
            }

            if (br00073Quantity > 0) {
                response.actions.BR00073 = existingLineItems.BR00073 ?
                    (parseInt(existingLineItems.BR00073.properties.quantity) !== br00073Quantity ? `updated_quantity_${br00073Quantity}` : `exists_quantity_${br00073Quantity}`) :
                    `created_quantity_${br00073Quantity}`;
                response.br00073Quantity = br00073Quantity;
            } else {
                response.actions.BR00073 = existingLineItems.BR00073 ? 'deleted' : 'none';
                response.br00073Quantity = 0;
            }

            // BR00074 (Kadry i płace - z teczkami)
            if (br00074Quantity > 0) {
                response.actions.BR00074 = existingLineItems.BR00074 ?
                    (parseInt(existingLineItems.BR00074.properties.quantity) !== br00074Quantity ? `updated_quantity_${br00074Quantity}` : `exists_quantity_${br00074Quantity}`) :
                    `created_quantity_${br00074Quantity}`;
                response.br00074Quantity = br00074Quantity;
            } else {
                response.actions.BR00074 = existingLineItems.BR00074 ? 'deleted' : 'none';
                response.br00074Quantity = 0;
            }

            if (br00075Quantity > 0) {
                response.actions.BR00075 = existingLineItems.BR00075 ?
                    (parseInt(existingLineItems.BR00075.properties.quantity) !== br00075Quantity ? `updated_quantity_${br00075Quantity}` : `exists_quantity_${br00075Quantity}`) :
                    `created_quantity_${br00075Quantity}`;
                response.br00075Quantity = br00075Quantity;
            } else {
                response.actions.BR00075 = existingLineItems.BR00075 ? 'deleted' : 'none';
                response.br00075Quantity = 0;
            }

            response.actions.BR00078 = shouldHaveBR00078 ? (existingLineItems.BR00078 ? 'exists' : 'created') : (existingLineItems.BR00078 ? 'deleted' : 'none');
            response.actions.BR00079 = shouldHaveBR00079 ? (existingLineItems.BR00079 ? 'exists' : 'created') : (existingLineItems.BR00079 ? 'deleted' : 'none');

            // Add BR00165 for payroll properties
            if (br00165Quantity > 0) {
                response.actions.BR00165 = existingLineItems.BR00165 ?
                    (parseInt(existingLineItems.BR00165.properties.quantity) !== br00165Quantity ? `updated_quantity_${br00165Quantity}` : `exists_quantity_${br00165Quantity}`) :
                    `created_quantity_${br00165Quantity}`;
                response.br00165Quantity = br00165Quantity;
            } else {
                response.actions.BR00165 = existingLineItems.BR00165 ? 'deleted' : 'none';
                response.br00165Quantity = 0;
            }
        }

        // Add MSP line item status to response for comprehensive updates
        if (propertyFlags.isUpdateTrigger) {
            response.actions.BR00111 = shouldHaveBR00111 ? (existingLineItems.BR00111 ? 'exists' : 'created') : (existingLineItems.BR00111 ? 'deleted' : 'none');

            // Add MSP team accounts BR00013 info
            if (br00013Quantity > 0) {
                const existingBR00013 = findLineItemBySkuSafe(lineItems, 'BR00013');
                response.actions.BR00013 = existingBR00013 ? `updated_quantity_${br00013Quantity}` : `created_quantity_${br00013Quantity}`;
                response.br00013Quantity = br00013Quantity;
                response.mspTeamAccounts = true;
            }
        }

        // Add banking line item status to response for comprehensive updates
        if (propertyFlags.isUpdateTrigger) {
            if (br00012Quantity > 0) {
                response.actions.BR00012 = existingLineItems.BR00012 ?
                    (parseInt(existingLineItems.BR00012.properties.quantity) !== br00012Quantity ? `updated_quantity_${br00012Quantity}` : `exists_quantity_${br00012Quantity}`) :
                    `created_quantity_${br00012Quantity}`;
                response.br00012Quantity = br00012Quantity;
            } else {
                response.actions.BR00012 = existingLineItems.BR00012 ? 'deleted' : 'none';
                response.br00012Quantity = 0;
            }

            if (br00130Quantity > 0) {
                response.actions.BR00130 = existingLineItems.BR00130 ?
                    (parseInt(existingLineItems.BR00130.properties.quantity) !== br00130Quantity ? `updated_quantity_${br00130Quantity}` : `exists_quantity_${br00130Quantity}`) :
                    `created_quantity_${br00130Quantity}`;
                response.br00130Quantity = br00130Quantity;
            } else {
                response.actions.BR00130 = existingLineItems.BR00130 ? 'deleted' : 'none';
                response.br00130Quantity = 0;
            }
        }

        // Add e-commerce line item status to response (for comprehensive updates only)
        if (propertyFlags.isUpdateTrigger && selectedEcommercePackage) {
            if (selectedEcommercePackage) {
                response.actions[selectedEcommercePackage] = existingLineItems[selectedEcommercePackage] ? 'exists' : 'created';
                response.selectedEcommercePackage = selectedEcommercePackage;
                response.ecommercePackageQuantity = ecommercePackageQuantity;
                response.additionalTransactions = additionalTransactions;
                response.totalEcommerceCost = totalEcommerceCost;

                // Handle additional transaction line items
                if (additionalTransactionSku && additionalTransactionQuantity > 0) {
                    response.actions[additionalTransactionSku] = existingLineItems[additionalTransactionSku] ? 'updated' : 'created';
                    response.additionalTransactionSku = additionalTransactionSku;
                    response.additionalTransactionQuantity = additionalTransactionQuantity;

                    // Mark other additional transaction SKUs as removed
                    for (const sku of ALL_ADDITIONAL_ECOMMERCE_SKUS) {
                        if (sku !== additionalTransactionSku && existingLineItems[sku]) {
                            response.actions[sku] = 'deleted';
                        }
                    }
                } else {
                    response.additionalTransactionSku = null;
                    response.additionalTransactionQuantity = 0;

                    // Mark all additional transaction SKUs as removed
                    for (const sku of ALL_ADDITIONAL_ECOMMERCE_SKUS) {
                        if (existingLineItems[sku]) {
                            response.actions[sku] = 'deleted';
                        }
                    }
                }

                // Mark other e-commerce packages as removed
                for (const sku of ALL_ECOMMERCE_SKUS) {
                    if (sku !== selectedEcommercePackage && existingLineItems[sku]) {
                        response.actions[sku] = 'deleted';
                    }
                }
            } else {
                // Mark all e-commerce packages as removed
                for (const sku of ALL_ECOMMERCE_SKUS) {
                    if (existingLineItems[sku]) {
                        response.actions[sku] = 'deleted';
                    }
                }

                // Mark all additional transaction SKUs as removed
                for (const sku of ALL_ADDITIONAL_ECOMMERCE_SKUS) {
                    if (existingLineItems[sku]) {
                        response.actions[sku] = 'deleted';
                    }
                }

                response.selectedEcommercePackage = null;
                response.ecommercePackageQuantity = 0;
                response.additionalTransactionSku = null;
                response.additionalTransactionQuantity = 0;
            }
        }

        // Add e-commerce response for comprehensive updates when no package is selected
        if (propertyFlags.isUpdateTrigger && !selectedEcommercePackage) {
            // Mark all e-commerce packages as removed if they exist
            for (const sku of ALL_ECOMMERCE_SKUS) {
                if (existingLineItems[sku]) {
                    response.actions[sku] = 'deleted';
                }
            }

            // Mark all additional transaction SKUs as removed if they exist
            for (const sku of ALL_ADDITIONAL_ECOMMERCE_SKUS) {
                if (existingLineItems[sku]) {
                    response.actions[sku] = 'deleted';
                }
            }

            response.selectedEcommercePackage = null;
            response.ecommercePackageQuantity = 0;
            response.additionalTransactionSku = null;
            response.additionalTransactionQuantity = 0;
        }

        // Add PIT package information to response (for comprehensive updates only)
        if (propertyFlags.isUpdateTrigger && pitRefreshed) {
            const ALL_PIT_SKUS = [SKUS.BR00094, SKUS.BR00095, SKUS.BR00096, SKUS.BR00097];

            if (!isFullAccounting && pitPackages && pitPackages.length > 0) {
                // Add PIT package actions to response
                for (const pitPackage of pitPackages) {
                    response.actions[pitPackage.sku] = existingLineItems[pitPackage.sku] ? 'updated' : 'created';
                }

                response.selectedPitPackage = selectedPitPackage;
                response.pitPackages = pitPackages;
                response.pitAccountingType = 'simplified';

                // Mark other PIT packages as removed
                for (const sku of ALL_PIT_SKUS) {
                    const isSelected = pitPackages.some(pkg => pkg.sku === sku);
                    if (!isSelected && existingLineItems[sku]) {
                        response.actions[sku] = 'deleted';
                    }
                }
            } else {
                // Mark all PIT packages as removed (full accounting or no packages)
                for (const sku of ALL_PIT_SKUS) {
                    if (existingLineItems[sku]) {
                        response.actions[sku] = 'deleted';
                    }
                }

                response.selectedPitPackage = null;
                response.pitPackages = [];
                response.pitAccountingType = isFullAccounting ? 'full' : 'simplified';
            }
        }

        // Add accounting packages info for comprehensive updates
        if (propertyFlags.isUpdateTrigger && accountingPackages && accountingPackages.length > 0) {
            if (br00013Quantity > 0) {
                // Use the already calculated packages and accounting type from business logic result
                response.actions.accountingPackages = `optimized_for_${br00013Quantity}_documents`;
                response.accountingPackages = accountingPackages || [];
                response.documentQuantity = br00013Quantity;
                response.accountingType = isFullAccounting ? 'Pełna księgowość' : 'Księgowość uproszona';
            } else {
                response.actions.accountingPackages = 'removed_all';
                response.documentQuantity = 0;
            }

            // Add BR00013 info
            if (br00013Quantity > 0) {
                const existingBR00013 = findLineItemBySkuSafe(lineItems, 'BR00013');
                response.actions.BR00013 = existingBR00013 ? `updated_quantity_${br00013Quantity}` : `created_quantity_${br00013Quantity}`;
                response.br00013Quantity = br00013Quantity;
            } else {
                response.actions.BR00013 = 'removed';
                response.br00013Quantity = 0;
            }
        }

        // Reset the aktualizuj_dane field to empty after successful processing
        if (propertyFlags.isUpdateTrigger) {
            try {
                await updateDealProperties(dealId, accessToken, {
                    aktualizuj_dane: ''
                });
                response.fieldReset = 'aktualizuj_dane field reset to empty after processing';
            } catch (resetError) {
                console.error('Warning: Failed to reset aktualizuj_dane field:', resetError);
                // Don't fail the entire request if field reset fails
                // Add a note to the response that the reset failed
                response.fieldResetWarning = 'Failed to reset aktualizuj_dane field after processing';
            }
        }

        // Sort line items by BR number alphabetically
        try {
            await sortLineItemsByBRNumber(dealId, accessToken);
        } catch (sortError) {
            console.error('Warning: Failed to sort line items by BR number:', sortError);
            // Don't fail the entire request if sorting fails
            response.sortingWarning = 'Failed to sort line items by BR number';
        }

        // Restore original console functions
        restore();

        return json(response);

    } catch (err) {
        console.error('Error processing HubSpot VAT-based line item operation:', err);

        // Restore original console functions in case of error
        restore();

        // Determine appropriate status code based on error type
        let statusCode = 500; // Default to internal server error
        let errorMessage = err.message || 'An unexpected error occurred';

        // Handle specific error types
        if (err.status) {
            // If the error has a status property (from SvelteKit error function), use it
            statusCode = err.status;
            if (err.body && err.body.message) {
                errorMessage = err.body.message;
            }
        } else if (err.message && (err.message.includes('Bad Request:') || err.message.includes('parameter is required'))) {
            statusCode = 400; // Bad Request
        } else if (err.message && (err.message.includes('Unauthorized') || err.message.includes('Invalid access token'))) {
            statusCode = 401; // Unauthorized
        } else if (err.message && (err.message.includes('Forbidden') || err.message.includes('Access denied'))) {
            statusCode = 403; // Forbidden
        } else if (err.message && (err.message.includes('Not found') || err.message.includes('Deal not found'))) {
            statusCode = 404; // Not Found
        } else if (err.message && (err.message.includes('Rate limit') || err.message.includes('Too many requests'))) {
            statusCode = 429; // Too Many Requests
        } else if (err instanceof TypeError && err.message && err.message.includes('Cannot read properties of undefined')) {
            // Handle undefined property access errors
            statusCode = 400;
            errorMessage = 'Invalid request: Missing required property value or malformed data';
        } else if (err.name === 'SyntaxError' || (err.message && err.message.includes('JSON'))) {
            // Handle JSON parsing errors
            statusCode = 400;
            errorMessage = 'Invalid request: Malformed JSON in request body';
        }

        return json({
            success: false,
            error: errorMessage,
            consoleLogs: consoleLogs
        }, { status: statusCode });
    }
}
