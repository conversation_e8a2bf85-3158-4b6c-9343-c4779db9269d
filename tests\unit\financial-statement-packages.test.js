import { describe, test, expect, vi, beforeEach } from 'vitest';
import {
    getFinancialStatementPackagePrices,
    calculateFinancialStatementBasePrice,
    calculateFinancialStatementBasePriceFromLineItems,
    selectFinancialStatementPackageForFullAccounting
} from '../../src/lib/financial-statement-packages.js';

// Mock dependencies
vi.mock('../../src/lib/price-fetcher.js', () => ({
    getProductPrice: vi.fn(),
    getMultipleProductPrices: vi.fn()
}));

vi.mock('../../src/lib/validation-utils.js', () => ({
    SKUS: {
        BR00099: 'BR00099',
        BR00100: 'BR00100', 
        BR00101: 'BR00101',
        BR00102: 'BR00102',
        BR00022: 'BR00022', // Individual document price for full accounting
        BR00030: 'BR00030', // Fixed assets
        BR00031: 'BR00031', // Cash registers
        BR00032: 'BR00032', // VAT EU/8/9M
        BR00012: 'BR00012', // Monthly bank reports
        BR00130: 'BR00130', // Daily bank reports
        BR00013: 'BR00013'  // Bank statement processing
    },
    calculateDocumentSum: vi.fn(),
    parseAndValidateNumericField: vi.fn(),
    processVatStatus: vi.fn(),
    DOCUMENT_FIELDS: {
        BOOKING_OPERATIONS: ['faktury_rachunki_sprzedazowe___ile_', 'faktury_rachunki_zakupu___ile_', 'faktury_walutowe___ile_miesiecznie_', 'dokumenty_wewnetrzne_wdt__wnt_itp'],
        CASH_BANK_OPERATIONS: ['kp_kw___banki_', 'kp_kw_gotowka', 'operacje_kp_kw_walutowe']
    }
}));

vi.mock('../../src/lib/ecommerce-packages.js', () => ({
    calculateOptimalEcommercePackage: vi.fn()
}));

describe('Financial Statement Packages', () => {
    const mockAccessToken = 'test-token';
    
    beforeEach(() => {
        vi.clearAllMocks();
    });

    describe('getFinancialStatementPackagePrices', () => {
        test('should fetch prices for all financial statement packages', async () => {
            const { getMultipleProductPrices } = await import('../../src/lib/price-fetcher.js');

            getMultipleProductPrices.mockResolvedValue({
                'BR00099': 699.00, // BASE
                'BR00100': 899.00, // SILVER
                'BR00101': 1599.00, // GOLD
                'BR00102': 2599.00  // PLATINUM
            });

            const result = await getFinancialStatementPackagePrices(mockAccessToken);

            expect(result).toEqual({
                packages: {
                    BASE: { sku: 'BR00099', price: 699.00 },
                    SILVER: { sku: 'BR00100', price: 899.00 },
                    GOLD: { sku: 'BR00101', price: 1599.00 },
                    PLATINUM: { sku: 'BR00102', price: 2599.00 }
                }
            });

            expect(getMultipleProductPrices).toHaveBeenCalledTimes(1);
            expect(getMultipleProductPrices).toHaveBeenCalledWith(['BR00099', 'BR00100', 'BR00101', 'BR00102'], mockAccessToken);
        });

        test('should throw error if price fetching fails', async () => {
            const { getMultipleProductPrices } = await import('../../src/lib/price-fetcher.js');
            getMultipleProductPrices.mockRejectedValue(new Error('API error'));

            await expect(getFinancialStatementPackagePrices(mockAccessToken))
                .rejects.toThrow('Failed to fetch financial statement package prices: API error');
        });
    });

    describe('calculateFinancialStatementBasePrice', () => {
        test('should calculate base price from all relevant line items', async () => {
            const { getProductPrice } = vi.mocked(await import('../../src/lib/price-fetcher.js'));
            const { calculateDocumentSum, parseAndValidateNumericField, processVatStatus } = vi.mocked(await import('../../src/lib/validation-utils.js'));
            const { calculateOptimalEcommercePackage } = vi.mocked(await import('../../src/lib/ecommerce-packages.js'));

            // Mock all the price fetching
            getProductPrice.mockImplementation((sku) => {
                const prices = {
                    'BR00022': 5.00,   // Individual document price
                    'BR00030': 50.00,  // Fixed assets
                    'BR00031': 30.00,  // Cash registers
                    'BR00032': 100.00, // VAT EU/8/9M
                    'BR00012': 25.00,  // Monthly bank reports
                    'BR00130': 15.00,  // Daily bank reports
                    'BR00013': 10.00   // Bank statement processing
                };
                return Promise.resolve(prices[sku]);
            });

            // Mock document calculations
            calculateDocumentSum.mockImplementation((props, fields) => {
                // Check if this is the booking operations call
                if (fields === 'BOOKING_OPERATIONS' || (Array.isArray(fields) && fields.includes('faktury_rachunki_sprzedazowe___ile_'))) {
                    return 100; // 100 documents
                }
                // Check if this is the cash-bank operations call
                if (fields === 'CASH_BANK_OPERATIONS' || (Array.isArray(fields) && fields.includes('kp_kw___banki_'))) {
                    return 50; // 50 cash-bank operations
                }
                return 0;
            });

            // Mock field parsing
            parseAndValidateNumericField.mockImplementation((props, field) => {
                const values = {
                    'kasy_fiskalne___ile_': 2,
                    'srodki_trwale_i_wartosci_niematerialne_i_prawne___ile_': 3,
                    'ilosc_kont_bankowych___raporty_dzienne': 1,
                    'ilosc_kont_bankowych___raporty_miesieczne': 2,
                    'ile_transakcji_sprzedazy_w_miesiacu_': 500
                };
                return values[field] || 0;
            });

            // Mock VAT processing
            processVatStatus.mockReturnValue({
                shouldHaveBR00032: true,
                shouldHaveBR00033: false
            });

            // Mock e-commerce package
            calculateOptimalEcommercePackage.mockResolvedValue({
                totalCost: 200.00
            });

            const mockProperties = {
                'vat___status_podatnika': 'VAT EU'
            };

            const result = await calculateFinancialStatementBasePrice(mockProperties, mockAccessToken);

            // Expected calculation:
            // Documents: 100 * 5.00 = 500.00
            // Cash registers: 2 * 30.00 = 60.00
            // Fixed assets: 3 * 50.00 = 150.00
            // VAT EU: 100.00
            // Daily reports: 1 * 15.00 = 15.00
            // Monthly reports: 2 * 25.00 = 50.00
            // Cash-bank operations: 50 * 10.00 = 500.00
            // E-commerce: 200.00 (transaction count = 500)
            // Total: 1575.00
            expect(result).toBe(1575.00);
        });

        test('should handle zero values correctly', async () => {
            const { getProductPrice } = vi.mocked(await import('../../src/lib/price-fetcher.js'));
            const { calculateDocumentSum, parseAndValidateNumericField, processVatStatus } = vi.mocked(await import('../../src/lib/validation-utils.js'));
            const { calculateOptimalEcommercePackage } = vi.mocked(await import('../../src/lib/ecommerce-packages.js'));

            getProductPrice.mockResolvedValue(10.00);
            calculateDocumentSum.mockReturnValue(0);
            parseAndValidateNumericField.mockReturnValue(0);
            processVatStatus.mockReturnValue({
                shouldHaveBR00032: false,
                shouldHaveBR00033: false
            });
            calculateOptimalEcommercePackage.mockResolvedValue({ totalCost: 0 });

            const result = await calculateFinancialStatementBasePrice({}, mockAccessToken);
            expect(result).toBe(0);
        });
    });

    describe('selectFinancialStatementPackageForFullAccounting', () => {
        test('should select correct package and use higher price', async () => {
            const { getProductPrice, getMultipleProductPrices } = vi.mocked(await import('../../src/lib/price-fetcher.js'));
            const { calculateDocumentSum, parseAndValidateNumericField, processVatStatus } = vi.mocked(await import('../../src/lib/validation-utils.js'));

            // Mock package prices for getMultipleProductPrices
            getMultipleProductPrices.mockResolvedValue({
                'BR00099': 699.00,
                'BR00100': 899.00,
                'BR00101': 1599.00,
                'BR00102': 2599.00
            });

            // Mock individual price fetching for base price calculation
            getProductPrice.mockImplementation((sku) => {
                const prices = {
                    'BR00022': 5.00
                };
                return Promise.resolve(prices[sku]);
            });

            // Mock a calculated base price higher than package price
            calculateDocumentSum.mockImplementation((props, fields) => {
                // Check if this is the booking operations call
                if (fields === 'BOOKING_OPERATIONS' || (Array.isArray(fields) && fields.includes('faktury_rachunki_sprzedazowe___ile_'))) {
                    return 200; // 200 documents
                }
                return 0;
            });
            parseAndValidateNumericField.mockReturnValue(0);
            processVatStatus.mockReturnValue({ shouldHaveBR00032: false });

            // Mock the ecommerce package calculation
            const { calculateOptimalEcommercePackage } = vi.mocked(await import('../../src/lib/ecommerce-packages.js'));
            calculateOptimalEcommercePackage.mockResolvedValue({ totalCost: 0 });

            const result = await selectFinancialStatementPackageForFullAccounting('SILVER', {}, mockAccessToken);

            expect(result).toHaveLength(1);
            expect(result[0]).toEqual({
                sku: 'BR00100',
                quantity: 1,
                description: 'Financial statement SILVER package for full accounting',
                price: 1000.00, // 200 documents * 5.00 = 1000.00 (higher than 899.00)
                customPrice: true
            });
        });

        test('should use package price when higher than calculated base price', async () => {
            const { getProductPrice, getMultipleProductPrices } = vi.mocked(await import('../../src/lib/price-fetcher.js'));
            const { calculateDocumentSum, parseAndValidateNumericField, processVatStatus } = vi.mocked(await import('../../src/lib/validation-utils.js'));

            // Mock package prices for getMultipleProductPrices
            getMultipleProductPrices.mockResolvedValue({
                'BR00099': 699.00,
                'BR00100': 899.00,
                'BR00101': 1599.00,
                'BR00102': 2599.00
            });

            // Mock individual price fetching for base price calculation
            getProductPrice.mockImplementation((sku) => {
                const prices = {
                    'BR00022': 5.00
                };
                return Promise.resolve(prices[sku]);
            });

            // Mock a low calculated base price
            calculateDocumentSum.mockImplementation((props, fields) => {
                // Check if this is the booking operations call
                if (fields === 'BOOKING_OPERATIONS' || (Array.isArray(fields) && fields.includes('faktury_rachunki_sprzedazowe___ile_'))) {
                    return 50; // 50 documents
                }
                return 0;
            });
            parseAndValidateNumericField.mockReturnValue(0);
            processVatStatus.mockReturnValue({ shouldHaveBR00032: false });

            // Mock the ecommerce package calculation
            const { calculateOptimalEcommercePackage } = vi.mocked(await import('../../src/lib/ecommerce-packages.js'));
            calculateOptimalEcommercePackage.mockResolvedValue({ totalCost: 0 });

            const result = await selectFinancialStatementPackageForFullAccounting('GOLD', {}, mockAccessToken);

            expect(result).toHaveLength(1);
            expect(result[0]).toEqual({
                sku: 'BR00101',
                quantity: 1,
                description: 'Financial statement GOLD package for full accounting',
                price: 1599.00, // Package price is higher than calculated 250.00
                customPrice: false
            });
        });

        test('should return empty array when no package selected', async () => {
            const result = await selectFinancialStatementPackageForFullAccounting(null, {}, mockAccessToken);
            expect(result).toEqual([]);
        });

        test('should return empty array when invalid package name', async () => {
            const { getMultipleProductPrices } = vi.mocked(await import('../../src/lib/price-fetcher.js'));
            getMultipleProductPrices.mockResolvedValue({
                'BR00099': 699.00,
                'BR00100': 899.00,
                'BR00101': 1599.00,
                'BR00102': 2599.00
            });

            const result = await selectFinancialStatementPackageForFullAccounting('INVALID', {}, mockAccessToken);
            expect(result).toEqual([]);
        });
    });

    describe('calculateFinancialStatementBasePriceFromLineItems', () => {
        test('should calculate base price from relevant line items', async () => {
            const mockAllProperties = {
                'faktury_rachunki_sprzedazowe___ile_': '10',
                'faktury_rachunki_zakupu___ile_': '5'
            };

            const mockCreatedLineItems = [
                { sku: 'BR00003', price: 100, quantity: 1 }, // Full accounting package
                { sku: 'BR00013', price: 15, quantity: 5 },  // Bank statements
                { sku: 'BR00030', price: 50, quantity: 2 },  // Fixed assets
                { sku: 'BR00032', price: 75, quantity: 1 },  // VAT
                { sku: 'BR00999', price: 25, quantity: 1 }   // Irrelevant SKU (should be ignored)
            ];

            const result = await calculateFinancialStatementBasePriceFromLineItems(
                mockAllProperties,
                mockCreatedLineItems,
                mockAccessToken
            );

            // Expected: 100*1 + 15*5 + 50*2 + 75*1 = 100 + 75 + 100 + 75 = 350
            expect(result).toBe(350);
        });

        test('should return 0 when no relevant line items found', async () => {
            const mockAllProperties = {};
            const mockCreatedLineItems = [
                { sku: 'BR00999', price: 100, quantity: 1 }, // Irrelevant SKU
                { sku: 'BR00998', price: 50, quantity: 2 }   // Another irrelevant SKU
            ];

            const result = await calculateFinancialStatementBasePriceFromLineItems(
                mockAllProperties,
                mockCreatedLineItems,
                mockAccessToken
            );

            expect(result).toBe(0);
        });

        test('should handle empty line items array', async () => {
            const mockAllProperties = {};
            const mockCreatedLineItems = [];

            const result = await calculateFinancialStatementBasePriceFromLineItems(
                mockAllProperties,
                mockCreatedLineItems,
                mockAccessToken
            );

            expect(result).toBe(0);
        });

        test('should handle line items with zero quantities', async () => {
            const mockAllProperties = {};
            const mockCreatedLineItems = [
                { sku: 'BR00003', price: 100, quantity: 0 }, // Zero quantity
                { sku: 'BR00013', price: 15, quantity: 3 }   // Normal quantity
            ];

            const result = await calculateFinancialStatementBasePriceFromLineItems(
                mockAllProperties,
                mockCreatedLineItems,
                mockAccessToken
            );

            // Expected: 100*0 + 15*3 = 0 + 45 = 45
            expect(result).toBe(45);
        });

        test('should handle calculation errors gracefully', async () => {
            const mockAllProperties = {};
            const mockCreatedLineItems = [
                { sku: 'BR00003', price: 'invalid', quantity: 1 } // Invalid price
            ];

            // This should not throw an error but handle it gracefully
            const result = await calculateFinancialStatementBasePriceFromLineItems(
                mockAllProperties,
                mockCreatedLineItems,
                mockAccessToken
            );

            // With invalid price, the calculation results in NaN (actual behavior)
            expect(result).toBeNaN();
        });
    });

    describe('Error Handling', () => {
        test('getFinancialStatementPackagePrices should handle price fetch errors', async () => {
            const { getMultipleProductPrices } = await import('../../src/lib/price-fetcher.js');

            // Mock getMultipleProductPrices to return prices with missing BR00099
            getMultipleProductPrices.mockResolvedValue({
                'BR00100': 800.00,
                'BR00101': 1200.00,
                'BR00102': 2000.00
                // Missing BR00099 - this should trigger the error
            });

            await expect(getFinancialStatementPackagePrices(mockAccessToken))
                .rejects.toThrow('Price not available for financial statement SKU BR00099');
        });

        test('calculateFinancialStatementBasePrice should handle calculation errors', async () => {
            const { calculateDocumentSum, parseAndValidateNumericField, processVatStatus } = await import('../../src/lib/validation-utils.js');
            const { calculateOptimalEcommercePackage } = await import('../../src/lib/ecommerce-packages.js');
            const { getProductPrice } = await import('../../src/lib/price-fetcher.js');

            // Mock successful setup
            calculateDocumentSum.mockReturnValue(100);
            parseAndValidateNumericField.mockReturnValue(5);
            processVatStatus.mockReturnValue({ shouldHaveBR00032: true });
            calculateOptimalEcommercePackage.mockResolvedValue({ totalCost: 200 });

            // Mock price fetch error
            getProductPrice.mockRejectedValue(new Error('Price fetch failed'));

            const mockAllProperties = {
                'faktury_rachunki_sprzedazowe___ile_': '10',
                'faktury_rachunki_zakupu___ile_': '5'
            };

            await expect(calculateFinancialStatementBasePrice(mockAllProperties, mockAccessToken))
                .rejects.toThrow('Failed to calculate financial statement base price: Price fetch failed');
        });

        test('selectFinancialStatementPackageForFullAccounting should handle price comparison edge cases', async () => {
            const { getMultipleProductPrices, getProductPrice } = await import('../../src/lib/price-fetcher.js');

            // Mock prices where calculated base price equals package price
            getMultipleProductPrices.mockResolvedValue({
                'BR00099': 500.00,
                'BR00100': 800.00,
                'BR00101': 1200.00,
                'BR00102': 2000.00
            });

            // Mock individual price fetching for base price calculation
            getProductPrice.mockImplementation((sku) => {
                const prices = {
                    'BR00022': 10.00  // Individual document price for calculation
                };
                return Promise.resolve(prices[sku] || 0);
            });

            // Mock the validation utils functions that are used in the calculation
            const { calculateDocumentSum, parseAndValidateNumericField, processVatStatus } = await import('../../src/lib/validation-utils.js');
            calculateDocumentSum.mockReturnValue(80); // 80 documents
            parseAndValidateNumericField.mockReturnValue(0); // No additional items
            processVatStatus.mockReturnValue({ shouldHaveBR00032: false }); // No VAT special handling

            // Mock the ecommerce package calculation
            const { calculateOptimalEcommercePackage } = await import('../../src/lib/ecommerce-packages.js');
            calculateOptimalEcommercePackage.mockResolvedValue({ totalCost: 0 });

            const result = await selectFinancialStatementPackageForFullAccounting(
                'SILVER',
                {
                    'faktury_rachunki_sprzedazowe___ile_': '40',
                    'faktury_rachunki_zakupu___ile_': '40'
                },
                mockAccessToken
            );

            // Should return the SILVER package since calculated price (800) equals package price (800)
            expect(result).toEqual([
                {
                    sku: 'BR00100',
                    quantity: 1,
                    price: 800.00,
                    description: 'Financial statement SILVER package for full accounting',
                    customPrice: false
                }
            ]);
        });

        test('selectFinancialStatementPackageForFullAccounting should handle line items calculation path', async () => {
            const { getMultipleProductPrices } = await import('../../src/lib/price-fetcher.js');

            getMultipleProductPrices.mockResolvedValue({
                'BR00099': 500.00,
                'BR00100': 800.00,
                'BR00101': 1200.00,
                'BR00102': 2000.00
            });

            const mockCreatedLineItems = [
                { sku: 'BR00003', price: 100, quantity: 1 },
                { sku: 'BR00013', price: 15, quantity: 5 }
            ];

            const result = await selectFinancialStatementPackageForFullAccounting(
                'GOLD',
                {},
                mockAccessToken,
                mockCreatedLineItems
            );

            // Should use line items calculation when provided
            expect(result).toEqual([
                {
                    sku: 'BR00101',
                    quantity: 1,
                    price: 1200.00,
                    description: 'Financial statement GOLD package for full accounting',
                    customPrice: false
                }
            ]);
        });
    });
});
