import { chromium } from 'playwright';

(async () => {
  const browser = await chromium.launch({
    headless: false
  });
  const context = await browser.newContext();
  const page = await context.newPage();
  await page.goto('https://www.mojedatovaschranka.cz/as/login?uri=https%3a%2f%2fwww.mojedatovaschranka.cz%2fportal%2fISDS%2f&status=NCOO');
  await page.getByLabel('Uživatelské jméno').click();
  await page.getByLabel('Uživatelské jméno').fill('tr2gjm');
  await page.getByText('Hes<PERSON>', { exact: true }).click();
  await page.getByLabel('Hes<PERSON>').fill('Taxcoach2024!');
  await page.getByRole('button', { name: '<PERSON><PERSON><PERSON>l<PERSON>it se' }).click();
  await page.goto('https://www.mojedatovaschranka.cz/portal/ISDS/seznamzprav/prijate');

  await page.getByRole('button', { name: 'Napsat zprávu' }).click();
  await page.waitForTimeout(3000); // Wait for 2000 milliseconds (2 seconds)

  await page.getByRole('heading', { name: 'Územní pracoviště Ostrava I' }).click();
  await page.getByLabel('Předmět (povinný)').click();
  await page.getByLabel('Předmět (povinný)').fill('CZC00004');
  await page.getByRole('button', { name: 'Pokračovat' }).click();
  await page.getByRole('button', { name: 'Nahrajte z počítače' }).click();
  await page.getByRole('button', { name: 'Nahrajte z počítače' }).setInputFiles('CZC00004_CZ686214534_yiwushiyouxiangmaoyishanghang_2024-07-01_2024-07-31_亚马逊.csv');

  // ---------------------
  await context.close();
  await browser.close();
})();