/**
 * E-commerce package selection logic
 */

import { getEcommercePackagePrices } from './price-fetcher.js';

/**
 * Test function to verify real-time pricing is working
 * @param {boolean} isFullAccounting - Whether it's full accounting or simplified
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<Object>} Pricing test results
 */
export async function testEcommercePricing(isFullAccounting, accessToken) {
    console.log('Testing e-commerce pricing fetch for:', isFullAccounting ? 'full' : 'simplified', 'accounting');

    const pricingData = await getEcommercePackagePrices(isFullAccounting, accessToken);

    if (!pricingData) {
        console.error('Failed to fetch pricing data');
        return { success: false, error: 'Failed to fetch pricing data' };
    }

    console.log('Pricing test results:', {
        accountingType: isFullAccounting ? 'full' : 'simplified',
        packagePrices: pricingData.prices,
        packageSkus: pricingData.packages,
        additionalSkus: pricingData.additionalSkus
    });

    return {
        success: true,
        data: pricingData,
        accountingType: isFullAccounting ? 'full' : 'simplified'
    };
}

/**
 * Calculate optimal e-commerce package based on transaction volume
 * @param {number} transactionCount - Number of transactions per month
 * @param {boolean} isFullAccounting - Whether it's full accounting or simplified
 * @param {string} accessToken - HubSpot access token for fetching prices
 * @returns {Promise<Object>} Optimal package selection with additional cost logic
 */
export async function calculateOptimalEcommercePackage(transactionCount, isFullAccounting, accessToken) {
    console.log('Calculating optimal e-commerce package for:', transactionCount, 'transactions, Full accounting:', isFullAccounting);

    if (transactionCount <= 0) {
        console.log('No transactions - no e-commerce package needed');
        return {
            selectedPackage: null,
            additionalTransactions: 0,
            totalCost: 0,
            additionalCostPerTransaction: 0
        };
    }

    // Fetch dynamic pricing from HubSpot - will throw error if prices not available
    const pricingData = await getEcommercePackagePrices(isFullAccounting, accessToken);

    console.log('E-commerce pricing data fetched from HubSpot:', {
        accountingType: isFullAccounting ? 'full' : 'simplified',
        prices: pricingData.prices
    });

    // Build package configurations with dynamic pricing
    const packageSkus = isFullAccounting ?
        ['BR00058', 'BR00059', 'BR00060', 'BR00061'] :
        ['BR00090', 'BR00091', 'BR00092', 'BR00093'];

    const additionalSkus = isFullAccounting ?
        ['BR00170', 'BR00171', 'BR00172', 'BR00173'] :
        ['BR00166', 'BR00167', 'BR00168', 'BR00169'];

    const transactionLimits = [200, 1000, 5000, 20000];

    const availablePackages = {};
    packageSkus.forEach((sku, index) => {
        availablePackages[sku] = {
            maxTransactions: transactionLimits[index],
            price: pricingData.prices[sku],
            additionalCost: pricingData.prices[additionalSkus[index]],
            additionalSku: additionalSkus[index]
        };
        console.log(`Package ${sku}: ${transactionLimits[index]} transactions, price: ${pricingData.prices[sku]} zł, additional cost: ${pricingData.prices[additionalSkus[index]]} zł per transaction`);
    });

    // Apply 90% rule for package optimization
    const bestOption = calculateOptimalEcommercePackageWith90PercentRule(
        transactionCount,
        availablePackages,
        packageSkus
    );

    console.log('Selected optimal e-commerce package with 90% rule:', bestOption);

    return {
        selectedPackage: bestOption ? bestOption.sku : null,
        additionalTransactions: bestOption ? bestOption.additionalTransactions : 0,
        totalCost: bestOption ? bestOption.totalCost : 0,
        additionalCostPerTransaction: bestOption ? bestOption.additionalCostPerTransaction : 0,
        additionalSku: bestOption ? bestOption.additionalSku : null,
        packageData: bestOption ? bestOption.packageData : null
    };
}

/**
 * Calculate optimal e-commerce package using 90% price threshold rule
 * When package + additional costs >= 90% of next package price, jump to next package
 * @param {number} transactionCount - Number of transactions per month
 * @param {Object} availablePackages - Available package configurations
 * @param {Array} packageSkus - Array of package SKUs in order
 * @returns {Object|null} Optimal package configuration or null
 */
function calculateOptimalEcommercePackageWith90PercentRule(transactionCount, availablePackages, packageSkus) {
    console.log('Calculating optimal e-commerce package with 90% rule for', transactionCount, 'transactions');

    // Calculate cost for each package and check 90% rule
    for (let i = 0; i < packageSkus.length; i++) {
        const sku = packageSkus[i];
        const packageData = availablePackages[sku];

        if (!packageData) continue;

        // Calculate cost for current package
        let totalCost = packageData.price;
        let additionalTransactions = 0;

        // Calculate additional cost if transactions exceed package limit
        if (transactionCount > packageData.maxTransactions) {
            additionalTransactions = transactionCount - packageData.maxTransactions;
            const additionalCost = additionalTransactions * packageData.additionalCost;
            totalCost += additionalCost;
        }

        console.log(`Package ${sku}: Base cost ${packageData.price}, Additional transactions: ${additionalTransactions}, Additional cost: ${additionalTransactions * packageData.additionalCost}, Total: ${totalCost}`);

        // Check if there's a next package to compare against
        const nextPackageIndex = i + 1;
        if (nextPackageIndex < packageSkus.length) {
            const nextSku = packageSkus[nextPackageIndex];
            const nextPackageData = availablePackages[nextSku];

            if (nextPackageData) {
                const threshold90Percent = nextPackageData.price * 0.9;
                console.log(`Checking 90% rule: ${totalCost} >= ${threshold90Percent} (90% of ${nextPackageData.price})`);

                // If current package cost >= 90% of next package, try next package
                if (totalCost >= threshold90Percent) {
                    console.log(`90% threshold reached, checking ${nextSku} package`);
                    continue;
                }
            }
        }

        // This package is optimal
        console.log(`Selected ${sku} package as optimal`);
        return {
            sku,
            packageData,
            additionalTransactions,
            totalCost,
            additionalCostPerTransaction: packageData.additionalCost,
            additionalSku: packageData.additionalSku
        };
    }

    // If we get here, there's an error in the package selection logic
    throw new Error(`Failed to select optimal e-commerce package for ${transactionCount} transactions. Package selection logic error.`);

    console.log(`Using ${fallbackSku} as fallback package`);
    return {
        sku: fallbackSku,
        packageData: fallbackPackageData,
        additionalTransactions: fallbackAdditionalTransactions,
        totalCost: fallbackTotalCost,
        additionalCostPerTransaction: fallbackPackageData.additionalCost,
        additionalSku: fallbackPackageData.additionalSku
    };
}
