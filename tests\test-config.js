/**
 * Test Configuration for HubSpot Line Item Management System
 *
 * This configuration supports both integration and unit tests:
 * 1. Integration tests: Real HubSpot API calls for reading data (getDealProperties, fetchPricesFromHubSpot)
 * 2. Unit tests: Fully mocked environment for simulating different business scenarios
 * 3. Mocked write operations to prevent data modification during tests
 * 4. Proper error handling for missing access tokens
 */

// Load environment variables
import { config } from 'dotenv';
config();

// Vitest is imported in individual test files, not in config

// Test configuration constants
export const TEST_CONFIG = {
  // Use real HubSpot access token if available
  HUBSPOT_ACCESS_TOKEN: process.env.HUBSPOT_ACCESS_TOKEN,

  // Test deal IDs - these should be real deals in your HubSpot account
  TEST_DEALS: {
    SAMPLE_DEAL: '************',
    SMALL_BUSINESS: '************',
    MEDIUM_BUSINESS: '************',
    LARGE_BUSINESS: '************'
  },
  
  // Test timeouts
  TIMEOUTS: {
    API_CALL: 30000,      // 30 seconds for API calls
    INTEGRATION: 60000,   // 1 minute for integration tests
    UNIT: 5000           // 5 seconds for unit tests
  },
  
  // Test flags
  FLAGS: {
    SKIP_INTEGRATION_WITHOUT_TOKEN: true,
    MOCK_ALL_WRITES: true,
    ENABLE_CONSOLE_LOGS: false,
    STRICT_MODE: true,
    PREFER_UNIT_TESTS: true,  // Prefer unit tests over integration tests for business logic
    ENABLE_COMPREHENSIVE_UNIT_TESTS: true  // Enable comprehensive unit test scenarios
  }
};

/**
 * Get safe test environment configuration
 * This provides mock configurations without using Vitest directly
 * (Vitest should be imported in individual test files)
 */
export function getSafeTestEnvironmentConfig() {
  return {
    // Mock response configurations for write operations
    mockResponses: {
      updateLineItemsForDeal: {
        success: true,
        message: 'MOCKED: Line items would be updated',
        lineItemsProcessed: 0,
        timestamp: new Date().toISOString()
      },

      createLineItem: {
        success: true,
        message: 'MOCKED: Line item would be created',
        lineItemId: 'mock-line-item-id'
      },

      deleteLineItem: {
        success: true,
        message: 'MOCKED: Line item would be deleted'
      }
    },

    // Test environment settings
    accessToken: TEST_CONFIG.HUBSPOT_ACCESS_TOKEN,
    testDealId: TEST_CONFIG.TEST_DEALS.SAMPLE_DEAL,
    canRunIntegrationTests: !!(TEST_CONFIG.HUBSPOT_ACCESS_TOKEN),

    // Helper for setting up mocks in test files
    getMockSetupInstructions: () => `
      // In your test file, set up mocks like this:
      import { vi } from 'vitest';
      import { getSafeTestEnvironmentConfig } from './test-config.js';

      const testConfig = getSafeTestEnvironmentConfig();

      vi.mock('../../src/lib/hubspot-api.js', () => ({
        ...vi.importActual('../../src/lib/hubspot-api.js'),
        updateLineItemsForDeal: vi.fn().mockResolvedValue(testConfig.mockResponses.updateLineItemsForDeal),
        createLineItem: vi.fn().mockResolvedValue(testConfig.mockResponses.createLineItem),
        deleteLineItem: vi.fn().mockResolvedValue(testConfig.mockResponses.deleteLineItem)
      }));
    `
  };
}

/**
 * Check if integration tests can run
 */
export function canRunIntegrationTests() {
  return !!(TEST_CONFIG.HUBSPOT_ACCESS_TOKEN);
}

/**
 * Get test access token or throw descriptive error
 */
export function getTestAccessToken() {
  if (!TEST_CONFIG.HUBSPOT_ACCESS_TOKEN) {
    throw new Error(
      'HUBSPOT_ACCESS_TOKEN environment variable is required for integration tests. ' +
      'Set it to your HubSpot private app access token.'
    );
  }
  return TEST_CONFIG.HUBSPOT_ACCESS_TOKEN;
}

/**
 * Setup safe test environment
 * This function ensures that write operations are mocked and read operations use real data
 */
export function setupSafeTestEnvironment() {
  // Ensure global test utilities are available
  if (!global.testUtils) {
    global.testUtils = {
      enableConsole: () => {
        global.console.log = console.log;
        global.console.debug = console.debug;
        global.console.info = console.info;
        global.console.warn = console.warn;
      },
      mockConsole: () => {
        // Mock console functions with simple functions
        global.console.log = () => {};
        global.console.debug = () => {};
        global.console.info = () => {};
        global.console.warn = () => {};
      }
    };
  }

  // Log that we're in safe test mode
  if (TEST_CONFIG.FLAGS.ENABLE_CONSOLE_LOGS) {
    console.log('🔒 Safe test environment initialized - write operations will be mocked');
  }
}

/**
 * Create a test wrapper that skips tests if no access token is available
 */
export function createIntegrationTestSuite(suiteName, testFn) {
  const describeOrSkip = canRunIntegrationTests() ? describe : describe.skip;

  return describeOrSkip(suiteName, () => {
    beforeEach(() => {
      setupSafeTestEnvironment();

      if (TEST_CONFIG.FLAGS.ENABLE_CONSOLE_LOGS) {
        global.testUtils.enableConsole();
      } else {
        global.testUtils.mockConsole();
      }
    });

    testFn();
  });
}

/**
 * Validate that a test deal exists and has the required properties
 */
export async function validateTestDeal(dealId, accessToken) {
  try {
    const { getDealProperties } = await import('../src/lib/hubspot-api.js');
    
    const properties = await getDealProperties(dealId, accessToken, [
      'faktury_rachunki_sprzedazowe___ile_',
      'rodzaj_ksiegowosci'
    ]);
    
    if (!properties) {
      throw new Error(`Deal ${dealId} not found or inaccessible`);
    }
    
    return {
      isValid: true,
      dealId,
      properties
    };
  } catch (error) {
    return {
      isValid: false,
      dealId,
      error: error.message
    };
  }
}

/**
 * Create mock deal properties that match real HubSpot data structure
 */
export function createRealisticMockDealProperties(overrides = {}) {
  return {
    // Document fields
    'faktury_rachunki_sprzedazowe___ile_': '0',
    'faktury_rachunki_zakupu___ile_': '0',
    'faktury_walutowe___ile_miesiecznie_': '0',
    'dokumenty_wewnetrzne_wdt__wnt_itp': '0',
    'operacje_kp_kw_walutowe': '0',
    'kp_kw___banki_': '0',
    'kp_kw_gotowka': '0',
    
    // Accounting type
    'rodzaj_ksiegowosci': 'Pełna księgowość',
    
    // VAT and language
    'vat___status_podatnika': '',
    'jezyk_obslugi': 'Polski',
    
    // Payroll fields
    'pakiet_kadrowo_placowy': '',
    'umowa_o_prace___liczba_osob': '0',
    'umowy_cywilnoprawne___liczba_pracownikow': '0',
    'dodatkowe_skladniki_wynagrodzenia': '0',
    'ppk___ile_osob_': '0',
    'kto_robi_import_do_moje_ppk': '',
    'pfron___ile_osob_': '0',
    'a1___czy_wystepuja_': '0',
    
    // E-commerce fields
    'ile_transakcji_sprzedazy_w_miesiacu_': '0',
    
    // Additional fields
    'srodki_trwale_i_wartosci_niematerialne_i_prawne___ile_': '0',
    'kasy_fiskalne___ile_': '0',
    'pytania_do_msp': '',
    'ilosc_kont_bankowych___raporty_dzienne': '0',
    'wyciagi_bankowe___liczba_': '0',
    'liczba_kanalow_platnosci': '0',
    
    // HubSpot metadata (realistic)
    'hs_object_id': '************',
    'hs_created_date': '2024-01-15T10:30:00.000Z',
    'hs_lastmodifieddate': '2024-01-20T14:45:00.000Z',
    
    // Override with provided values
    ...overrides
  };
}

/**
 * Test data scenarios for comprehensive testing
 */
export const TEST_SCENARIOS = {
  SMALL_BUSINESS: {
    name: 'Small Business - BASE Package',
    properties: createRealisticMockDealProperties({
      'faktury_rachunki_sprzedazowe___ile_': '7',
      'rodzaj_ksiegowosci': 'Pełna księgowość',
      'jezyk_obslugi': 'Polski',
      'vat___status_podatnika': ''
    }),
    expectedResults: {
      br00013Quantity: 7,
      baseDocumentQuantity: 7,
      packageType: 'BASE'
    }
  },

  MEDIUM_BUSINESS: {
    name: 'Medium Business - SILVER Package',
    properties: createRealisticMockDealProperties({
      'faktury_rachunki_sprzedazowe___ile_': '23',
      'rodzaj_ksiegowosci': 'Pełna księgowość',
      'jezyk_obslugi': 'English',
      'vat___status_podatnika': 'VAT EU',
      'pakiet_kadrowo_placowy': 'Płace',
      'umowa_o_prace___liczba_osob': '5',
      'ppk___ile_osob_': '3'
    }),
    expectedResults: {
      br00013Quantity: 50,
      baseDocumentQuantity: 23,
      packageType: 'SILVER'
    }
  },

  LARGE_BUSINESS: {
    name: 'Large Business - GOLD Package',
    properties: createRealisticMockDealProperties({
      'faktury_rachunki_sprzedazowe___ile_': '105',
      'rodzaj_ksiegowosci': 'Pełna księgowość',
      'jezyk_obslugi': 'Polski',
      'vat___status_podatnika': 'VAT EU;VAT OSS',
      'pakiet_kadrowo_placowy': 'Kadry i płace PREMIUM',
      'umowa_o_prace___liczba_osob': '15',
      'umowy_cywilnoprawne___liczba_pracownikow': '8'
    }),
    expectedResults: {
      br00013Quantity: 110,
      baseDocumentQuantity: 105,
      packageType: 'GOLD'
    }
  },

  ENTERPRISE: {
    name: 'Enterprise - PLATINUM Package',
    properties: createRealisticMockDealProperties({
      'faktury_rachunki_sprzedazowe___ile_': '189',
      'rodzaj_ksiegowosci': 'Pełna księgowość',
      'jezyk_obslugi': 'German',
      'vat___status_podatnika': 'VAT EU;VAT OSS;VAT INNE KRAJE',
      'pakiet_kadrowo_placowy': 'Ryczałt',
      'umowa_o_prace___liczba_osob': '25',
      'umowy_cywilnoprawne___liczba_pracownikow': '15'
    }),
    expectedResults: {
      br00013Quantity: 200,
      baseDocumentQuantity: 189,
      packageType: 'PLATINUM'
    }
  },

  // ============================================================================
  // EDGE CASE SCENARIOS - For comprehensive unit testing
  // ============================================================================
  EDGE_CASES: {
    ZERO_DOCUMENTS: {
      name: 'Zero Documents Edge Case',
      properties: createRealisticMockDealProperties({
        'faktury_rachunki_sprzedazowe___ile_': '0',
        'faktury_rachunki_zakupu___ile_': '0',
        'faktury_walutowe___ile_miesiecznie_': '0',
        'dokumenty_wewnetrzne_wdt__wnt_itp': '0',
        'operacje_kp_kw_walutowe': '0',
        'kp_kw___banki_': '0',
        'kp_kw_gotowka': '0',
        'rodzaj_ksiegowosci': 'Pełna księgowość'
      }),
      expectedResults: {
        br00013Quantity: 5, // BASE minimum
        baseDocumentQuantity: 0,
        packageType: 'BASE'
      }
    },

    NEGATIVE_VALUES: {
      name: 'Negative Values Handling',
      properties: createRealisticMockDealProperties({
        'faktury_rachunki_sprzedazowe___ile_': '-5',
        'faktury_rachunki_zakupu___ile_': '-3',
        'umowa_o_prace___liczba_osob': '-2',
        'umowy_cywilnoprawne___liczba_pracownikow': '-1',
        'rodzaj_ksiegowosci': 'Pełna księgowość',
        'pakiet_kadrowo_placowy': 'Płace'
      }),
      expectedResults: {
        br00013Quantity: 5, // BASE minimum (negative converted to 0)
        baseDocumentQuantity: 0,
        packageType: 'BASE'
      }
    },

    SILVER_BOUNDARY: {
      name: 'Critical SILVER Package Boundary (104 documents)',
      properties: createRealisticMockDealProperties({
        'faktury_rachunki_sprzedazowe___ile_': '104',
        'rodzaj_ksiegowosci': 'Pełna księgowość'
      }),
      expectedResults: {
        br00013Quantity: 104, // Should use document count, not GOLD minimum
        baseDocumentQuantity: 104,
        packageType: 'SILVER'
      }
    }
  },

  // ============================================================================
  // VAT SCENARIOS - For VAT processing testing
  // ============================================================================
  VAT_SCENARIOS: {
    MULTIPLE_VAT: {
      name: 'Multiple VAT Types',
      properties: createRealisticMockDealProperties({
        'faktury_rachunki_sprzedazowe___ile_': '15',
        'rodzaj_ksiegowosci': 'Pełna księgowość',
        'vat___status_podatnika': 'VAT EU;VAT OSS;VAT 8;VAT 9M'
      }),
      expectedResults: {
        shouldHaveBR00032: true,
        shouldHaveBR00033: true,
        br00032Quantity: 3, // VAT EU + VAT 8 + VAT 9M
        br00033Quantity: 1  // VAT OSS
      }
    },

    VAT_EU_ONLY: {
      name: 'VAT EU Only',
      properties: createRealisticMockDealProperties({
        'faktury_rachunki_sprzedazowe___ile_': '15',
        'rodzaj_ksiegowosci': 'Pełna księgowość',
        'vat___status_podatnika': 'VAT EU'
      }),
      expectedResults: {
        shouldHaveBR00032: true,
        shouldHaveBR00033: false,
        br00032Quantity: 1,
        br00033Quantity: 0
      }
    }
  },

  // ============================================================================
  // PAYROLL SCENARIOS - For payroll package testing
  // ============================================================================
  PAYROLL_SCENARIOS: {
    PREMIUM_PACKAGE: {
      name: 'Premium Payroll Package',
      properties: createRealisticMockDealProperties({
        'faktury_rachunki_sprzedazowe___ile_': '15',
        'rodzaj_ksiegowosci': 'Pełna księgowość',
        'pakiet_kadrowo_placowy': 'Kadry i płace PREMIUM',
        'umowa_o_prace___liczba_osob': '10',
        'umowy_cywilnoprawne___liczba_pracownikow': '5',
        'ppk___ile_osob_': '8',
        'pfron___ile_osob_': '2',
        'a1___czy_wystepuja_': '1',
        'kto_robi_import_do_moje_ppk': 'Biuro'
      }),
      expectedResults: {
        br00070Quantity: 10,
        br00071Quantity: 5,
        shouldHaveBR00078: false, // No PPK for PREMIUM
        shouldHaveBR00079: false  // No Biuro import for PREMIUM
      }
    },

    RYCZALT_PACKAGE: {
      name: 'Ryczałt Package',
      properties: createRealisticMockDealProperties({
        'faktury_rachunki_sprzedazowe___ile_': '15',
        'rodzaj_ksiegowosci': 'Pełna księgowość',
        'pakiet_kadrowo_placowy': 'Ryczałt',
        'umowa_o_prace___liczba_osob': '4',
        'umowy_cywilnoprawne___liczba_pracownikow': '2',
        'ppk___ile_osob_': '3',
        'pfron___ile_osob_': '1',
        'a1___czy_wystepuja_': '1'
      }),
      expectedResults: {
        br00069Quantity: 1,
        br00069OverridePrice: 400, // 4×80 + 2×70 + 1×50 + 1×120 = 400
        br00070Quantity: 0, // Cleared for Ryczałt
        br00071Quantity: 0, // Cleared for Ryczałt
        br00080Quantity: 0  // Cleared for Ryczałt
      }
    }
  }
};
