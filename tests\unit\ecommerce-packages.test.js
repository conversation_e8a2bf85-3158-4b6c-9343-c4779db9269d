import { describe, test, expect, beforeEach, vi } from 'vitest';

// Mock dependencies
vi.mock('../../src/lib/price-fetcher.js', () => ({
  getEcommercePackagePrices: vi.fn()
}));

import {
  testEcommercePricing,
  calculateOptimalEcommercePackage
} from '../../src/lib/ecommerce-packages.js';

import { getEcommercePackagePrices } from '../../src/lib/price-fetcher.js';

describe('E-commerce Packages', () => {
  const mockAccessToken = 'test-token';

  beforeEach(() => {
    vi.clearAllMocks();
    // Mock console methods to avoid noise in tests
    vi.spyOn(console, 'log').mockImplementation(() => {});
    vi.spyOn(console, 'error').mockImplementation(() => {});
  });

  describe('calculateOptimalEcommercePackage - Edge Cases', () => {
    test('should return null package for zero transactions', async () => {
      const result = await calculateOptimalEcommercePackage(0, true, mockAccessToken);

      expect(result).toEqual({
        selectedPackage: null,
        additionalTransactions: 0,
        totalCost: 0,
        additionalCostPerTransaction: 0
      });

      // Should not call pricing API for zero transactions
      expect(getEcommercePackagePrices).not.toHaveBeenCalled();
    });

    test('should return null package for negative transactions', async () => {
      const result = await calculateOptimalEcommercePackage(-5, false, mockAccessToken);

      expect(result).toEqual({
        selectedPackage: null,
        additionalTransactions: 0,
        totalCost: 0,
        additionalCostPerTransaction: 0
      });

      expect(getEcommercePackagePrices).not.toHaveBeenCalled();
    });

    test('should handle package selection logic error gracefully', async () => {
      // Mock pricing data that would cause package selection to fail
      const mockPricingData = {
        packages: {}, // Empty packages object
        additionalSkus: {},
        prices: {} // Empty prices - this will cause undefined package prices
      };

      getEcommercePackagePrices.mockResolvedValue(mockPricingData);

      // Function returns first package with undefined prices when pricing data is empty
      const result = await calculateOptimalEcommercePackage(100, true, mockAccessToken);

      expect(result.selectedPackage).toBe('BR00058'); // First package in the list
      expect(result.totalCost).toBeUndefined(); // Cost is undefined due to missing prices
    });

    test('should handle missing package data in selection logic', async () => {
      // Mock pricing data with missing package configurations
      const mockPricingData = {
        packages: {
          'BR00058': null, // Missing package data
          'BR00059': undefined // Another missing package
        },
        additionalSkus: {
          'BR00170': 1.25,
          'BR00171': 0.45
        },
        prices: {
          'BR00058': 250,
          'BR00059': 450
        }
      };

      getEcommercePackagePrices.mockResolvedValue(mockPricingData);

      // Function should handle missing package data and select the first available package
      const result = await calculateOptimalEcommercePackage(100, true, mockAccessToken);

      expect(result.selectedPackage).toBe('BR00058');
      expect(result.totalCost).toBe(250);
    });

    test('should handle pricing API errors', async () => {
      getEcommercePackagePrices.mockRejectedValue(new Error('HubSpot API error'));

      await expect(calculateOptimalEcommercePackage(100, true, mockAccessToken))
        .rejects.toThrow('HubSpot API error');
    });

    test('should handle null pricing data', async () => {
      getEcommercePackagePrices.mockResolvedValue(null);

      await expect(calculateOptimalEcommercePackage(100, true, mockAccessToken))
        .rejects.toThrow();
    });

    test('should work with simplified accounting packages', async () => {
      const mockSimplifiedPricingData = {
        packages: {
          'BR00090': { price: 180, maxTransactions: 200, additionalCost: 0.90 },
          'BR00091': { price: 320, maxTransactions: 1000, additionalCost: 0.32 },
          'BR00092': { price: 800, maxTransactions: 5000, additionalCost: 0.16 },
          'BR00093': { price: 1600, maxTransactions: 20000, additionalCost: 0.08 }
        },
        additionalSkus: {
          'BR00166': 0.90,
          'BR00167': 0.32,
          'BR00168': 0.16,
          'BR00169': 0.08
        },
        prices: {
          'BR00090': 180,
          'BR00091': 320,
          'BR00092': 800,
          'BR00093': 1600,
          'BR00166': 0.90,
          'BR00167': 0.32,
          'BR00168': 0.16,
          'BR00169': 0.08
        }
      };

      getEcommercePackagePrices.mockResolvedValue(mockSimplifiedPricingData);

      const result = await calculateOptimalEcommercePackage(150, false, mockAccessToken);

      expect(result.selectedPackage).toBe('BR00090');
      expect(result.additionalTransactions).toBe(0);
      expect(result.totalCost).toBe(180);
      expect(result.additionalCostPerTransaction).toBe(0.90);
      expect(result.additionalSku).toBe('BR00166');
    });

    test('should apply 90% rule correctly for package upgrades', async () => {
      const mockPricingData = {
        packages: {
          'BR00058': { price: 250, maxTransactions: 200, additionalCost: 1.25 },
          'BR00059': { price: 450, maxTransactions: 1000, additionalCost: 0.45 }
        },
        additionalSkus: {
          'BR00170': 1.25,
          'BR00171': 0.45
        },
        prices: {
          'BR00058': 250,
          'BR00059': 450,
          'BR00170': 1.25,
          'BR00171': 0.45
        }
      };

      getEcommercePackagePrices.mockResolvedValue(mockPricingData);

      // Test case where additional cost would push us to 90% of next package
      // BR00058 (250) + additional for 350 transactions would be 250 + (150 * 1.25) = 437.5
      // This is > 90% of BR00059 (450 * 0.9 = 405), so should upgrade to BR00059
      const result = await calculateOptimalEcommercePackage(350, true, mockAccessToken);

      expect(result.selectedPackage).toBe('BR00059');
      expect(result.totalCost).toBe(450);
      expect(result.additionalTransactions).toBe(0);
    });

    test('should handle edge case with exact 90% threshold', async () => {
      const mockPricingData = {
        packages: {
          'BR00058': { price: 250, maxTransactions: 200, additionalCost: 1.25 },
          'BR00059': { price: 450, maxTransactions: 1000, additionalCost: 0.45 }
        },
        additionalSkus: {
          'BR00170': 1.25,
          'BR00171': 0.45
        },
        prices: {
          'BR00058': 250,
          'BR00059': 450,
          'BR00170': 1.25,
          'BR00171': 0.45
        }
      };

      getEcommercePackagePrices.mockResolvedValue(mockPricingData);

      // Calculate transactions that would result in exactly 90% of next package
      // BR00059 * 0.9 = 405
      // BR00058 base = 250, so additional cost needed = 155
      // Additional transactions = 155 / 1.25 = 124
      // Total transactions = 200 + 124 = 324
      const result = await calculateOptimalEcommercePackage(324, true, mockAccessToken);

      // At exactly 90%, should upgrade to next package
      expect(result.selectedPackage).toBe('BR00059');
      expect(result.totalCost).toBe(450);
    });

    test('should handle very high transaction counts', async () => {
      const mockPricingData = {
        packages: {
          'BR00058': { price: 250, maxTransactions: 200, additionalCost: 1.25 },
          'BR00059': { price: 450, maxTransactions: 1000, additionalCost: 0.45 },
          'BR00060': { price: 1100, maxTransactions: 5000, additionalCost: 0.22 },
          'BR00061': { price: 2200, maxTransactions: 20000, additionalCost: 0.11 }
        },
        additionalSkus: {
          'BR00170': 1.25,
          'BR00171': 0.45,
          'BR00172': 0.22,
          'BR00173': 0.11
        },
        prices: {
          'BR00058': 250,
          'BR00059': 450,
          'BR00060': 1100,
          'BR00061': 2200,
          'BR00170': 1.25,
          'BR00171': 0.45,
          'BR00172': 0.22,
          'BR00173': 0.11
        }
      };

      getEcommercePackagePrices.mockResolvedValue(mockPricingData);

      // Test with 50,000 transactions (exceeds highest package limit)
      const result = await calculateOptimalEcommercePackage(50000, true, mockAccessToken);

      expect(result.selectedPackage).toBe('BR00061');
      expect(result.additionalTransactions).toBe(30000); // 50000 - 20000
      expect(result.totalCost).toBe(2200 + (30000 * 0.11)); // 2200 + 3300 = 5500
      expect(result.additionalCostPerTransaction).toBe(0.11);
    });

    test('should handle single transaction correctly', async () => {
      const mockPricingData = {
        packages: {
          'BR00090': { price: 180, maxTransactions: 200, additionalCost: 0.90 }
        },
        additionalSkus: {
          'BR00166': 0.90
        },
        prices: {
          'BR00090': 180,
          'BR00166': 0.90
        }
      };

      getEcommercePackagePrices.mockResolvedValue(mockPricingData);

      const result = await calculateOptimalEcommercePackage(1, false, mockAccessToken);

      expect(result.selectedPackage).toBe('BR00090');
      expect(result.additionalTransactions).toBe(0);
      expect(result.totalCost).toBe(180);
    });
  });

  describe('testEcommercePricing - Additional Tests', () => {
    test('should handle null pricing data', async () => {
      getEcommercePackagePrices.mockResolvedValue(null);

      const result = await testEcommercePricing(true, mockAccessToken);

      expect(result).toEqual({
        success: false,
        error: 'Failed to fetch pricing data'
      });
    });

    test('should handle undefined pricing data', async () => {
      getEcommercePackagePrices.mockResolvedValue(undefined);

      const result = await testEcommercePricing(false, mockAccessToken);

      expect(result).toEqual({
        success: false,
        error: 'Failed to fetch pricing data'
      });
    });

    test('should propagate API errors', async () => {
      const apiError = new Error('Network timeout');
      getEcommercePackagePrices.mockRejectedValue(apiError);

      await expect(testEcommercePricing(true, mockAccessToken))
        .rejects.toThrow('Network timeout');
    });

    test('should return success with valid pricing data for full accounting', async () => {
      const mockPricingData = {
        packages: { 'BR00058': { price: 250 } },
        prices: { 'BR00058': 250 }
      };

      getEcommercePackagePrices.mockResolvedValue(mockPricingData);

      const result = await testEcommercePricing(true, mockAccessToken);

      expect(result).toEqual({
        success: true,
        data: mockPricingData,
        accountingType: 'full'
      });
    });

    test('should return success with valid pricing data for simplified accounting', async () => {
      const mockPricingData = {
        packages: { 'BR00090': { price: 180 } },
        prices: { 'BR00090': 180 }
      };

      getEcommercePackagePrices.mockResolvedValue(mockPricingData);

      const result = await testEcommercePricing(false, mockAccessToken);

      expect(result).toEqual({
        success: true,
        data: mockPricingData,
        accountingType: 'simplified'
      });
    });
  });
});
