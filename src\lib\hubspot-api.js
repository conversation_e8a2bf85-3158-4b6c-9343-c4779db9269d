/**
 * HubSpot API utilities for managing deals, line items, and products
 */

/**
 * Get line items associated with a deal from HubSpot
 * @param {string} dealId - The HubSpot deal ID
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<Array>} Array of line items with details
 */
export async function getDealLineItemsWithDetails(dealId, accessToken) {
    try {
        // First, get all line item associations
        const associationsResponse = await fetch(
            `https://api.hubapi.com/crm/v4/objects/deals/${dealId}/associations/line_items`,
            {
                headers: {
                    'Authorization': `Bearer ${accessToken}`,
                    'Content-Type': 'application/json'
                }
            }
        );

        if (!associationsResponse.ok) {
            throw new Error(`Failed to fetch associations: ${associationsResponse.statusText}`);
        }

        const associationsData = await associationsResponse.json();
        const lineItemIds = associationsData.results.map(item => item.toObjectId);

        if (lineItemIds.length === 0) {
            return [];
        }

        // Batch fetch line item details (up to 100 at a time)
        const batchSize = 100;
        const batches = [];

        // Create batches of line item IDs
        for (let i = 0; i < lineItemIds.length; i += batchSize) {
            batches.push(lineItemIds.slice(i, i + batchSize));
        }

        // Fetch all batches in parallel
        const batchPromises = batches.map(batch =>
            fetch('https://api.hubapi.com/crm/v3/objects/line_items/batch/read', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${accessToken}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    inputs: batch.map(id => ({ id })),
                    properties: ['name', 'price', 'quantity', 'hs_product_id', 'amount', 'hs_sku']
                })
            }).then(response => {
                if (!response.ok) {
                    throw new Error(`Batch request failed: ${response.statusText}`);
                }
                return response.json();
            })
        );

        const batchResults = await Promise.all(batchPromises);
        const allLineItems = batchResults.flatMap(result => result.results);

        return allLineItems;

    } catch (error) {
        console.error('Error fetching deal line items:', error);
        throw error;
    }
}

/**
 * Search for existing products in HubSpot product library by SKU
 * @param {string} sku - SKU to search for
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<Object|null>} Product object if found, null otherwise
 */
export async function findProductBySku(sku, accessToken) {
    try {
        console.log('Searching for product with SKU:', sku);

        // Search products by SKU
        const response = await fetch(
            `https://api.hubapi.com/crm/v3/objects/products/search`,
            {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${accessToken}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    filterGroups: [{
                        filters: [{
                            propertyName: 'hs_sku',
                            operator: 'EQ',
                            value: sku
                        }]
                    }],
                    properties: ['name', 'hs_sku', 'price', 'description', 'hs_product_id'],
                    limit: 1
                })
            }
        );

        if (!response.ok) {
            const errorData = await response.text();
            throw new Error(`Product search failed for SKU ${sku}: ${response.statusText} - ${errorData}`);
        }

        const searchResult = await response.json();
        console.log('Product search result:', searchResult);

        if (searchResult.results && searchResult.results.length > 0) {
            const product = searchResult.results[0];
            console.log('Found product:', product);
            return product;
        }

        throw new Error(`No product found with SKU: ${sku}`);
    } catch (error) {
        console.error('Error searching for product:', error);
        throw new Error(`Failed to search for product with SKU ${sku}: ${error.message}`);
    }
}

/**
 * Get deal properties from HubSpot
 * @param {string} dealId - The HubSpot deal ID
 * @param {string} accessToken - HubSpot access token
 * @param {Array} properties - Array of property names to fetch
 * @returns {Promise<Object>} Deal properties object
 */
export async function getDealProperties(dealId, accessToken, properties) {
    try {
        const propertiesParam = properties.join(',');
        const response = await fetch(
            `https://api.hubapi.com/crm/v3/objects/deals/${dealId}?properties=${propertiesParam}`,
            {
                headers: {
                    'Authorization': `Bearer ${accessToken}`,
                    'Content-Type': 'application/json'
                }
            }
        );

        if (!response.ok) {
            throw new Error(`Failed to fetch deal properties: ${response.statusText}`);
        }

        const dealData = await response.json();
        return dealData.properties;
    } catch (error) {
        console.error('Error fetching deal properties:', error);
        throw error;
    }
}

/**
 * Update deal properties in HubSpot
 * @param {string} dealId - The HubSpot deal ID
 * @param {string} accessToken - HubSpot access token
 * @param {Object} properties - Object with property names and values to update
 * @returns {Promise<Object>} Updated deal object
 */
export async function updateDealProperties(dealId, accessToken, properties) {
    try {
        console.log(`Updating deal ${dealId} properties:`, properties);

        const response = await fetch(`https://api.hubapi.com/crm/v3/objects/deals/${dealId}`, {
            method: 'PATCH',
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                properties: properties
            })
        });

        if (!response.ok) {
            const errorData = await response.text();
            throw new Error(`Failed to update deal properties: ${response.statusText} - ${errorData}`);
        }

        return await response.json();
    } catch (error) {
        console.error('Error updating deal properties:', error);
        throw error;
    }
}

/**
 * Calculate total amount from all line items and update deal's "Kwota" field
 * @param {string} dealId - The HubSpot deal ID
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<number>} Total amount calculated
 */
export async function updateDealAmount(dealId, accessToken) {
    try {
        console.log(`Calculating and updating total amount for deal ${dealId}`);

        // Get all line items for the deal
        const lineItems = await getDealLineItemsWithDetails(dealId, accessToken);
        console.log(`Found ${lineItems.length} line items for amount calculation`);

        // Calculate total amount from all line items
        let totalAmount = 0;
        for (const item of lineItems) {
            const priceValue = parseFloat(item.properties.price);
            const quantityValue = parseInt(item.properties.quantity);

            // Validate price and quantity
            if (isNaN(priceValue)) {
                throw new Error(`Invalid price for line item ${item.properties.hs_sku || item.properties.name}: ${item.properties.price}`);
            }
            if (isNaN(quantityValue) || quantityValue <= 0) {
                throw new Error(`Invalid quantity for line item ${item.properties.hs_sku || item.properties.name}: ${item.properties.quantity}`);
            }

            const amount = priceValue * quantityValue;
            totalAmount += amount;
            console.log(`Line item ${item.properties.hs_sku || item.properties.name}: ${priceValue} x ${quantityValue} = ${amount}`);
        }

        console.log(`Total calculated amount: ${totalAmount}`);

        // Update the deal's "Kwota" field
        await updateDealProperties(dealId, accessToken, {
            amount: totalAmount
        });

        console.log(`Deal amount updated to ${totalAmount}`);
        return totalAmount;
    } catch (error) {
        console.error('Error updating deal amount:', error);
        throw error;
    }
}