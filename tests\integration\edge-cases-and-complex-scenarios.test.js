import { describe, test, expect, beforeEach, vi } from 'vitest';

// ============================================================================
// IMPORTS - All business logic modules and utilities
// ============================================================================
import {
  handleComprehensiveUpdate
} from '../../src/lib/business-logic-handlers.js';

// ============================================================================
// MOCKS - Centralized mock setup for all dependencies
// ============================================================================
vi.mock('../../src/lib/line-item-manager.js', () => ({
  findLineItemBySku: vi.fn(),
  findLineItemBySkuSafe: vi.fn(),
  validateSkuInProductLibrary: vi.fn(),
  createLineItemFromProduct: vi.fn(),
  createLineItem: vi.fn(),
  createLineItemWithQuantity: vi.fn(),
  createLineItemFromProductWithQuantity: vi.fn(),
  updateLineItemQuantity: vi.fn(),
  deleteLineItem: vi.fn(),
  associateLineItemWithDeal: vi.fn(),
  createAndAssociateLineItem: vi.fn(),
  manageLineItemQuantity: vi.fn(),
  manageBooleanLineItem: vi.fn(),
  createLineItemWithCustomPrice: vi.fn(),
  updateLineItemPrice: vi.fn(),
  manageLineItemWithCustomPrice: vi.fn(),
  updateAccountingPackagesToOptimal: vi.fn(),
  calculateBR00069CustomPrice: vi.fn()
}));

vi.mock('../../src/lib/price-fetcher.js', () => ({
  fetchPricesFromHubSpot: vi.fn(),
  getAccountingPackagePrices: vi.fn(),
  getPayrollPrices: vi.fn(),
  getEcommercePackagePrices: vi.fn()
}));

vi.mock('../../src/lib/hubspot-api.js', () => ({
  getDealProperties: vi.fn(),
  updateLineItemsForDeal: vi.fn(),
  getLineItemsForDeal: vi.fn(),
  findProductBySku: vi.fn(),
  updateDealProperties: vi.fn(),
  getDealLineItemsWithDetails: vi.fn()
}));

import { fetchPricesFromHubSpot, getAccountingPackagePrices, getPayrollPrices, getEcommercePackagePrices } from '../../src/lib/price-fetcher.js';
import { getDealProperties, updateLineItemsForDeal, getLineItemsForDeal, findProductBySku, updateDealProperties, getDealLineItemsWithDetails } from '../../src/lib/hubspot-api.js';

// ============================================================================
// SHARED TEST UTILITIES - Common helpers and mock data
// ============================================================================

/**
 * Creates mock deal properties with default values and optional overrides
 * Used across all test scenarios to ensure consistency
 */
function createMockDealProperties(overrides = {}) {
  return {
    'faktury_rachunki_sprzedazowe___ile_': '0',
    'faktury_rachunki_zakupu___ile_': '0',
    'faktury_walutowe___ile_miesiecznie_': '0',
    'dokumenty_wewnetrzne_wdt__wnt_itp': '0',
    'operacje_kp_kw_walutowe': '0',
    'kp_kw___banki_': '0',
    'kp_kw_gotowka': '0',
    'rodzaj_ksiegowosci': 'Pełna księgowość',
    'jezyk_obslugi': 'Polski',
    'vat___status_podatnika': '',
    'pakiet_kadrowo_placowy': '',
    'umowa_o_prace___liczba_osob': '0',
    'umowy_cywilnoprawne___liczba_pracownikow': '0',
    'ppk___ile_osob_': '0',
    'pfron___ile_osob_': '0',
    'a1___czy_wystepuja_': '0',
    'kto_robi_import_do_moje_ppk': 'Klient',
    'srodki_trwale_i_wartosci_niematerialne_i_prawne___ile_': '0',
    'kasy_fiskalne___ile_': '0',
    'pytania_do_msp': '',
    'ilosc_kont_bankowych___raporty_dzienne': '0',
    'wyciagi_bankowe___liczba_': '0',
    'liczba_kanalow_platnosci': '0',
    'ile_transakcji_sprzedazy_w_miesiacu_': '0',
    'branza': '',
    'uslugi_do_wyceny': 'Księgowość',
    'dodatkowe_skladniki_wynagrodzenia': '',
    ...overrides
  };
}

/**
 * Creates standardized mock prices for accounting packages
 * Ensures consistent pricing across all tests
 */
function createMockPrices() {
  return {
    'BR00003': 499,    // BASE full accounting
    'BR00004': 764,    // SILVER full accounting
    'BR00005': 1374,   // GOLD full accounting
    'BR00006': 2499,   // PLATINUM full accounting
    'BR00007': 234,    // BASE simplified accounting
    'BR00008': 294,    // SILVER simplified accounting
    'BR00009': 524,    // GOLD simplified accounting
    'BR00010': 999,    // PLATINUM simplified accounting
    'BR00022': 12.9,   // Extra documents (50 pack)
    'BR00023': 49.9    // Extra documents (200 pack)
  };
}

/**
 * Creates mock data for edge case testing
 * Tests boundary conditions and edge cases
 */
function createEdgeCaseScenarios() {
  return {
    zeroDocuments: createMockDealProperties({
      'faktury_rachunki_sprzedazowe___ile_': '0',
      'faktury_rachunki_zakupu___ile_': '0',
      'faktury_walutowe___ile_miesiecznie_': '0',
      'dokumenty_wewnetrzne_wdt__wnt_itp': '0',
      'operacje_kp_kw_walutowe': '0',
      'kp_kw___banki_': '0',
      'kp_kw_gotowka': '0',
      'rodzaj_ksiegowosci': 'Pełna księgowość',
      'uslugi_do_wyceny': 'Księgowość'
    }),
    
    negativeValues: createMockDealProperties({
      'faktury_rachunki_sprzedazowe___ile_': '-5',
      'faktury_rachunki_zakupu___ile_': '-3',
      'umowa_o_prace___liczba_osob': '-2',
      'umowy_cywilnoprawne___liczba_pracownikow': '-1',
      'rodzaj_ksiegowosci': 'Pełna księgowość',
      'pakiet_kadrowo_placowy': 'Płace',
      'uslugi_do_wyceny': 'Księgowość;Kadry',
      'dodatkowe_skladniki_wynagrodzenia': '0',
      'kto_robi_import_do_moje_ppk': 'Biuro'
    }),
    
    boundaryConditions: {
      silverBoundary: createMockDealProperties({
        'faktury_rachunki_sprzedazowe___ile_': '104', // Critical boundary for SILVER package bug
        'rodzaj_ksiegowosci': 'Pełna księgowość',
        'uslugi_do_wyceny': 'Księgowość'
      }),
      
      goldBoundary: createMockDealProperties({
        'faktury_rachunki_sprzedazowe___ile_': '110', // GOLD package minimum
        'rodzaj_ksiegowosci': 'Pełna księgowość',
        'uslugi_do_wyceny': 'Księgowość'
      }),
      
      platinumBoundary: createMockDealProperties({
        'faktury_rachunki_sprzedazowe___ile_': '200', // PLATINUM package minimum
        'rodzaj_ksiegowosci': 'Pełna księgowość',
        'uslugi_do_wyceny': 'Księgowość'
      })
    }
  };
}

/**
 * Creates mock data for VAT testing scenarios
 * Tests different VAT status combinations
 */
function createVatTestingScenarios() {
  return {
    vatEU: createMockDealProperties({
      'faktury_rachunki_sprzedazowe___ile_': '15',
      'rodzaj_ksiegowosci': 'Pełna księgowość',
      'vat___status_podatnika': 'VAT EU',
      'uslugi_do_wyceny': 'Księgowość'
    }),
    
    vatOSS: createMockDealProperties({
      'faktury_rachunki_sprzedazowe___ile_': '15',
      'rodzaj_ksiegowosci': 'Pełna księgowość',
      'vat___status_podatnika': 'VAT OSS',
      'uslugi_do_wyceny': 'Księgowość'
    }),
    
    multipleVAT: createMockDealProperties({
      'faktury_rachunki_sprzedazowe___ile_': '15',
      'rodzaj_ksiegowosci': 'Pełna księgowość',
      'vat___status_podatnika': 'VAT EU;VAT OSS;VAT 8;VAT 9M',
      'uslugi_do_wyceny': 'Księgowość'
    }),
    
    noVAT: createMockDealProperties({
      'faktury_rachunki_sprzedazowe___ile_': '15',
      'rodzaj_ksiegowosci': 'Pełna księgowość',
      'vat___status_podatnika': '',
      'uslugi_do_wyceny': 'Księgowość'
    })
  };
}

/**
 * Creates mock data for payroll package testing scenarios
 * Tests different payroll package configurations
 */
function createPayrollTestingScenarios() {
  return {
    premium: createMockDealProperties({
      'faktury_rachunki_sprzedazowe___ile_': '15',
      'rodzaj_ksiegowosci': 'Pełna księgowość',
      'pakiet_kadrowo_placowy': 'Kadry i płace PREMIUM',
      'umowa_o_prace___liczba_osob': '10',
      'umowy_cywilnoprawne___liczba_pracownikow': '5',
      'ppk___ile_osob_': '8',
      'pfron___ile_osob_': '2',
      'a1___czy_wystepuja_': '1',
      'kto_robi_import_do_moje_ppk': 'Biuro',
      'uslugi_do_wyceny': 'Księgowość;Kadry',
      'dodatkowe_skladniki_wynagrodzenia': '3',
      'kto_robi_import_do_moje_ppk': 'Biuro'
    }),
    
    ryczalt: createMockDealProperties({
      'faktury_rachunki_sprzedazowe___ile_': '15',
      'rodzaj_ksiegowosci': 'Pełna księgowość',
      'pakiet_kadrowo_placowy': 'Ryczałt',
      'umowa_o_prace___liczba_osob': '4',
      'umowy_cywilnoprawne___liczba_pracownikow': '2',
      'ppk___ile_osob_': '3',
      'pfron___ile_osob_': '1',
      'a1___czy_wystepuja_': '1',
      'uslugi_do_wyceny': 'Księgowość;Kadry',
      'dodatkowe_skladniki_wynagrodzenia': 'Tak',
      'kto_robi_import_do_moje_ppk': 'Biuro'
    })
  };
}

// ============================================================================
// MAIN TEST SUITE - Edge Cases and Complex Scenarios 
// ============================================================================
describe('Edge Cases and Complex Scenarios ', () => {
  const mockAccessToken = 'test-token';
  const mockDealId = '123456789';

  // Mock fetch for unit tests only
  const mockFetch = vi.fn();
  global.fetch = mockFetch;

  beforeEach(() => {
    // Clear all mocks and setup console mocking
    vi.clearAllMocks();
    vi.spyOn(console, 'log').mockImplementation(() => { });
    vi.spyOn(console, 'warn').mockImplementation(() => { });
    vi.spyOn(console, 'error').mockImplementation(() => { });

    // Reset fetch mock
    mockFetch.mockClear();

    // Setup fetch mock for this test suite only
    global.fetch = mockFetch;

    // Setup standard mock implementations
    updateLineItemsForDeal.mockResolvedValue({ success: true });
    fetchPricesFromHubSpot.mockResolvedValue(createMockPrices());

    getAccountingPackagePrices.mockImplementation((isFullAccounting) => {
      if (isFullAccounting) {
        return Promise.resolve({
          packages: {
            BASE: { price: 499 },
            SILVER: { price: 764 },
            GOLD: { price: 1374 },
            PLATINUM: { price: 2000 }
          },
          individualPrices: {
            BASE: 12.9,
            SILVER: 15.3,
            GOLD: 18.7,
            PLATINUM: 22.1
          },
          additionalPackages: {
            goldPack50: { price: 100 },
            platinumPack50: { price: 120 },
            platinumPack200: { price: 400 }
          },
          prices: createMockPrices()
        });
      } else {
        // Simplified accounting pricing
        return Promise.resolve({
          packages: {
            BASE: { price: 234 },
            SILVER: { price: 294 },
            GOLD: { price: 524 },
            PLATINUM: { price: 999 }
          },
          individualPrices: {
            BASE: 12.9,
            SILVER: 15.3,
            GOLD: 18.7,
            PLATINUM: 22.1
          },
          additionalPackages: {
            goldPack50: { price: 100 },
            platinumPack50: { price: 120 },
            platinumPack200: { price: 400 }
          },
          prices: createMockPrices()
        });
      }
    });

    getPayrollPrices.mockResolvedValue({
      'BR00070': 80,
      'BR00071': 70,
      'BR00080': 50,
      'BR00165': 120,
      'BR00076': 50   // Mobility package price needed for Ryczałt calculation
    });

    findProductBySku.mockResolvedValue({
      id: 'product123',
      properties: {
        name: 'Test Product',
        hs_sku: 'BR00013',
        price: '15'
      }
    });
  });

  // ============================================================================
  // EDGE CASES AND BOUNDARY CONDITIONS - Critical business logic edge cases
  // ============================================================================
  describe('Edge Cases and Boundary Conditions', () => {
    test('Should handle very high document count (stress test)', async () => {
      const mockProperties = createMockDealProperties({
        'faktury_rachunki_sprzedazowe___ile_': '500',
        'faktury_rachunki_zakupu___ile_': '300',
        'faktury_walutowe___ile_miesiecznie_': '50',
        'dokumenty_wewnetrzne_wdt__wnt_itp': '25',
        'rodzaj_ksiegowosci': 'Pełna księgowość',
        'uslugi_do_wyceny': 'Księgowość'
      });
      getDealProperties.mockResolvedValue(mockProperties);

      const result = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      // Should handle large numbers without breaking
      expect(result.completeRecalculationPerformed).toBe(true);
      expect(result).toHaveProperty('br00013Quantity');
      expect(typeof result.br00013Quantity).toBe('number');
      expect(result.br00013Quantity).toBeGreaterThanOrEqual(0);

      // Should use PLATINUM package for very high document count
      expect(result.selectedPackageName).toBe('PLATINUM');

      console.log('High document count stress test result:', {
        br00013Quantity: result.br00013Quantity,
        baseDocumentQuantity: result.baseDocumentQuantity,
        selectedPackageName: result.selectedPackageName
      });
    });

    test('Should handle negative values correctly (convert to 0)', async () => {
      const mockProperties = createEdgeCaseScenarios().negativeValues;
      getDealProperties.mockResolvedValue(mockProperties);

      const result = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      // All quantities should be non-negative
      expect(result.completeRecalculationPerformed).toBe(true);
      expect(result.br00013Quantity).toBeGreaterThanOrEqual(0);
      expect(result.baseDocumentQuantity).toBeGreaterThanOrEqual(0);

      // Check payroll quantities are non-negative
      if (result.br00072Quantity !== undefined) {
        expect(result.br00072Quantity).toBeGreaterThanOrEqual(0);
      }
      if (result.br00073Quantity !== undefined) {
        expect(result.br00073Quantity).toBeGreaterThanOrEqual(0);
      }

      console.log('Negative values handling result:', {
        br00013Quantity: result.br00013Quantity,
        baseDocumentQuantity: result.baseDocumentQuantity,
        payrollQuantities: {
          br00072: result.br00072Quantity,
          br00073: result.br00073Quantity
        }
      });
    });
  });

  // ============================================================================
  // COMPLEX BUSINESS SCENARIOS - Real-world integration scenarios
  // ============================================================================
  describe('Complex Business Scenarios', () => {
    test('Should handle comprehensive update with complex VAT scenarios', async () => {
      const mockProperties = createVatTestingScenarios().multipleVAT;
      getDealProperties.mockResolvedValue(mockProperties);

      const result = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      expect(result.completeRecalculationPerformed).toBe(true);

      // VAT settings should be properly processed
      expect(result).toHaveProperty('shouldHaveBR00032');
      expect(result).toHaveProperty('shouldHaveBR00033');
      expect(typeof result.shouldHaveBR00032).toBe('boolean');
      expect(typeof result.shouldHaveBR00033).toBe('boolean');

      // Multiple VAT types should be handled
      expect(result.shouldHaveBR00032).toBe(true); // VAT EU, VAT 8, VAT 9M
      expect(result.shouldHaveBR00033).toBe(true); // VAT OSS

      // If VAT quantities are present, they should be valid numbers
      if (result.br00032Quantity !== undefined) {
        expect(result.br00032Quantity).toBeGreaterThanOrEqual(0);
      }
      if (result.br00033Quantity !== undefined) {
        expect(result.br00033Quantity).toBeGreaterThanOrEqual(0);
      }

      console.log('Complex VAT scenarios result:', {
        shouldHaveBR00032: result.shouldHaveBR00032,
        shouldHaveBR00033: result.shouldHaveBR00033,
        br00032Quantity: result.br00032Quantity,
        br00033Quantity: result.br00033Quantity
      });
    });

    test('Should handle comprehensive update with payroll package scenarios', async () => {
      const mockProperties = createPayrollTestingScenarios().premium;
      getDealProperties.mockResolvedValue(mockProperties);

      const result = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      expect(result.completeRecalculationPerformed).toBe(true);

      // Check payroll-related properties exist and are valid
      const payrollProperties = [
        'br00069Quantity', 'br00070Quantity', 'br00071Quantity',
        'br00072Quantity', 'br00073Quantity', 'br00074Quantity',
        'br00075Quantity', 'br00077Quantity', 'br00080Quantity',
        'br00165Quantity', 'shouldHaveBR00078', 'shouldHaveBR00079'
      ];

      payrollProperties.forEach(prop => {
        if (result[prop] !== undefined) {
          if (prop.startsWith('shouldHave')) {
            expect(typeof result[prop]).toBe('boolean');
          } else {
            expect(typeof result[prop]).toBe('number');
            expect(result[prop]).toBeGreaterThanOrEqual(0);
          }
        }
      });

      // PREMIUM package specific behavior
      expect(result.br00070Quantity).toBe(10); // Employment contracts
      expect(result.br00071Quantity).toBe(5);  // Civil contracts
      expect(result.shouldHaveBR00078).toBe(false); // No PPK for PREMIUM
      expect(result.shouldHaveBR00079).toBe(false); // No Biuro import for PREMIUM

      console.log('Payroll package scenarios result:', {
        br00069Quantity: result.br00069Quantity,
        br00070Quantity: result.br00070Quantity,
        br00071Quantity: result.br00071Quantity,
        shouldHaveBR00078: result.shouldHaveBR00078,
        shouldHaveBR00079: result.shouldHaveBR00079
      });
    });

    test('Should handle comprehensive update with Ryczałt package logic', async () => {
      const mockProperties = createPayrollTestingScenarios().ryczalt;
      getDealProperties.mockResolvedValue(mockProperties);

      const result = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      expect(result.completeRecalculationPerformed).toBe(true);

      // Ryczałt package should have specific behavior
      if (result.br00069Quantity !== undefined && result.br00069Quantity > 0) {
        // If Ryczałt is active, should have quantity 1 and custom price
        expect(result.br00069Quantity).toBe(1);
        if (result.br00069OverridePrice !== undefined) {
          expect(result.br00069OverridePrice).toBeGreaterThan(0);
        }

        // Other payroll quantities should be cleared for Ryczałt
        expect(result.br00070Quantity || 0).toBe(0);
        expect(result.br00071Quantity || 0).toBe(0);
        expect(result.br00080Quantity || 0).toBe(0);
      }

      console.log('Ryczałt package logic result:', {
        br00069Quantity: result.br00069Quantity,
        br00069OverridePrice: result.br00069OverridePrice,
        br00070Quantity: result.br00070Quantity,
        br00071Quantity: result.br00071Quantity,
        br00080Quantity: result.br00080Quantity
      });
    });
  });
});
