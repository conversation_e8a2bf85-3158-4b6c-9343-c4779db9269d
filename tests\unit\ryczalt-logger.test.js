import { describe, test, expect, beforeEach, vi } from 'vitest';

// Mock fs module
vi.mock('fs', () => ({
  default: {
    existsSync: vi.fn(),
    mkdirSync: vi.fn(),
    writeFileSync: vi.fn()
  },
  existsSync: vi.fn(),
  mkdirSync: vi.fn(),
  writeFileSync: vi.fn()
}));

// Mock path module
vi.mock('path', () => ({
  default: {
    join: vi.fn(),
    dirname: vi.fn()
  },
  join: vi.fn(),
  dirname: vi.fn()
}));

import fs from 'fs';
import path from 'path';
import {
  logRyczaltCalculation,
  logRyczaltCalculationSummary
} from '../../src/lib/ryczalt-logger.js';

describe('Ryczalt Logger', () => {
  const mockDealId = 'test-deal-123';
  const mockCalculationData = {
    umowaOPraceOsob: 5,
    umowyCywilnoprawneOsob: 3,
    ppkIleOsob: 2,
    pfronIleOsob: 1,
    a1CzyWystepuja: 1,
    ryczaltMobilityPackages: 1,
    ryczaltUcpMobilityPackages: 1,
    ryczaltTotalMobilityPackages: 2,
    ktoRobiImport: 'Biuro',
    br00070Cost: 400,
    br00071Cost: 210,
    pfronCost: 50,
    aiCost: 120,
    br00076Cost: 100,
    br00077Cost: 0,
    br00078Cost: 0,
    br00079Cost: 0,
    subtotalRyczaltCost: 880,
    totalRyczaltCost: 968,
    prices: {
      BR00070: 80,
      BR00071: 70,
      BR00080: 50,
      BR00165: 120,
      BR00076: 50
    }
  };
  const mockDealProperties = {
    'pakiet_kadrowo_placowy': 'Ryczałt',
    'umowa_o_prace___liczba_osob': '5',
    'umowy_cywilnoprawne___liczba_pracownikow': '3'
  };

  beforeEach(() => {
    vi.clearAllMocks();
    // Mock console methods to avoid noise in tests
    vi.spyOn(console, 'log').mockImplementation(() => {});
    vi.spyOn(console, 'error').mockImplementation(() => {});

    // Setup default mocks
    path.join.mockImplementation((...args) => args.join('\\'));
    path.dirname.mockImplementation((filePath) => filePath.split('\\').slice(0, -1).join('\\'));

    // Mock process.cwd to return a consistent value
    vi.spyOn(process, 'cwd').mockReturnValue('C:\\test');
  });

  describe('logRyczaltCalculation', () => {
    test('should create logs directory when it does not exist', () => {
      fs.existsSync.mockReturnValue(false);
      fs.mkdirSync.mockImplementation(() => {});
      fs.writeFileSync.mockImplementation(() => {});

      const result = logRyczaltCalculation(mockDealId, mockCalculationData, mockDealProperties);

      expect(fs.existsSync).toHaveBeenCalled();
      expect(fs.mkdirSync).toHaveBeenCalledWith(
        expect.stringContaining('logs'),
        { recursive: true }
      );
      expect(fs.writeFileSync).toHaveBeenCalled();
      expect(result).toMatch(/logs.*\.log$/);
    });

    test('should not create logs directory when it already exists', () => {
      fs.existsSync.mockReturnValue(true);
      fs.writeFileSync.mockImplementation(() => {});

      const result = logRyczaltCalculation(mockDealId, mockCalculationData, mockDealProperties);

      expect(fs.existsSync).toHaveBeenCalled();
      expect(fs.mkdirSync).not.toHaveBeenCalled();
      expect(fs.writeFileSync).toHaveBeenCalled();
      expect(result).toMatch(/logs.*\.log$/);
    });

    test('should handle file system errors gracefully', () => {
      const fsError = new Error('Permission denied');
      fs.existsSync.mockReturnValue(false);
      fs.mkdirSync.mockImplementation(() => {
        throw fsError;
      });

      const result = logRyczaltCalculation(mockDealId, mockCalculationData, mockDealProperties);

      expect(console.error).toHaveBeenCalledWith('Error logging Ryczałt calculation:', fsError);
      expect(result).toBeNull();
    });

    test('should handle writeFileSync errors gracefully', () => {
      const writeError = new Error('Disk full');
      fs.existsSync.mockReturnValue(true);
      fs.writeFileSync.mockImplementation(() => {
        throw writeError;
      });

      const result = logRyczaltCalculation(mockDealId, mockCalculationData, mockDealProperties);

      expect(console.error).toHaveBeenCalledWith('Error logging Ryczałt calculation:', writeError);
      expect(result).toBeNull();
    });

    test('should handle mkdirSync errors gracefully', () => {
      const mkdirError = new Error('Cannot create directory');
      fs.existsSync.mockReturnValue(false);
      fs.mkdirSync.mockImplementation(() => {
        throw mkdirError;
      });

      const result = logRyczaltCalculation(mockDealId, mockCalculationData, mockDealProperties);

      expect(console.error).toHaveBeenCalledWith('Error logging Ryczałt calculation:', mkdirError);
      expect(result).toBeNull();
    });

    test('should generate correct log file path', () => {
      fs.existsSync.mockReturnValue(true);
      fs.writeFileSync.mockImplementation(() => {});
      
      // Mock Date to have predictable timestamp
      const mockDate = new Date('2023-07-29T12:34:56.789Z');
      vi.spyOn(global, 'Date').mockImplementation(() => mockDate);
      vi.spyOn(mockDate, 'toISOString').mockReturnValue('2023-07-29T12:34:56.789Z');

      const result = logRyczaltCalculation(mockDealId, mockCalculationData, mockDealProperties);

      expect(path.join).toHaveBeenCalledWith(
        expect.any(String), // process.cwd()
        'logs',
        'ryczalt-calculation-test-deal-123-2023-07-29T12-34-56-789Z.log'
      );
      expect(result).toContain('ryczalt-calculation-test-deal-123-2023-07-29T12-34-56-789Z.log');
    });

    test('should write comprehensive log content', () => {
      fs.existsSync.mockReturnValue(true);
      let writtenContent = '';
      fs.writeFileSync.mockImplementation((path, content) => {
        writtenContent = content;
      });

      logRyczaltCalculation(mockDealId, mockCalculationData, mockDealProperties);

      expect(writtenContent).toContain('RYCZAŁT PACKAGE CALCULATION REPORT');
      expect(writtenContent).toContain('Deal ID: test-deal-123');
      expect(writtenContent).toContain('Package Type: Ryczałt (BR00069)');
      expect(writtenContent).toContain('INPUT DATA');
      expect(writtenContent).toContain('COST CALCULATION BREAKDOWN');
      expect(writtenContent).toContain('Formula: 5 persons × 80 PLN = 400 PLN');
      expect(writtenContent).toContain('Formula: 3 persons × 70 PLN = 210 PLN');
      expect(writtenContent).toContain('COST SUMMARY');
      expect(writtenContent).toContain('FINAL RYCZAŁT PRICE: 968 PLN');
    });

    test('should handle missing calculation data gracefully', () => {
      fs.existsSync.mockReturnValue(true);
      fs.writeFileSync.mockImplementation(() => {});

      const incompleteData = {
        umowaOPraceOsob: 2,
        // Missing other required fields - this should cause the function to fail gracefully
      };

      const result = logRyczaltCalculation(mockDealId, incompleteData, mockDealProperties);

      // Function should return null when data is incomplete and log an error
      expect(result).toBeNull();
      expect(console.error).toHaveBeenCalledWith(
        'Error logging Ryczałt calculation:',
        expect.any(Error)
      );
    });

    test('should handle empty deal properties', () => {
      fs.existsSync.mockReturnValue(true);
      fs.writeFileSync.mockImplementation(() => {});

      const result = logRyczaltCalculation(mockDealId, mockCalculationData, {});

      expect(result).toMatch(/logs.*\.log$/);
      expect(fs.writeFileSync).toHaveBeenCalled();
    });
  });

  describe('logRyczaltCalculationSummary', () => {
    test('should log calculation summary to console', () => {
      logRyczaltCalculationSummary(mockCalculationData);

      expect(console.log).toHaveBeenCalledWith('=== RYCZAŁT CALCULATION SUMMARY ===');
      expect(console.log).toHaveBeenCalledWith('Employment (5×80): 400 PLN');
      expect(console.log).toHaveBeenCalledWith('Civil (3×70): 210 PLN');
      expect(console.log).toHaveBeenCalledWith('PFRON (1×50): 50 PLN');
      expect(console.log).toHaveBeenCalledWith('A1 (1×120): 120 PLN');
      expect(console.log).toHaveBeenCalledWith('Mobility (2×50): 100 PLN');
      expect(console.log).toHaveBeenCalledWith('Subtotal: 880 PLN');
      expect(console.log).toHaveBeenCalledWith('Final (with 10% markup): 968 PLN');
      expect(console.log).toHaveBeenCalledWith('=== END RYCZAŁT SUMMARY ===');
    });

    test('should handle zero values in calculation data', () => {
      const zeroData = {
        umowaOPraceOsob: 0,
        umowyCywilnoprawneOsob: 0,
        ppkIleOsob: 0,
        pfronIleOsob: 0,
        a1CzyWystepuja: 0,
        ryczaltTotalMobilityPackages: 0,
        br00070Cost: 0,
        br00071Cost: 0,
        pfronCost: 0,
        aiCost: 0,
        br00076Cost: 0,
        subtotalRyczaltCost: 0,
        totalRyczaltCost: 0,
        prices: {
          BR00070: 80,
          BR00071: 70,
          BR00080: 50,
          BR00165: 120,
          BR00076: 50
        }
      };

      logRyczaltCalculationSummary(zeroData);

      expect(console.log).toHaveBeenCalledWith('Employment (0×80): 0 PLN');
      expect(console.log).toHaveBeenCalledWith('Civil (0×70): 0 PLN');
      expect(console.log).toHaveBeenCalledWith('PFRON (0×50): 0 PLN');
      expect(console.log).toHaveBeenCalledWith('A1 (0×120): 0 PLN');
      expect(console.log).toHaveBeenCalledWith('Mobility (0×50): 0 PLN');
      expect(console.log).toHaveBeenCalledWith('Subtotal: 0 PLN');
      expect(console.log).toHaveBeenCalledWith('Final (with 10% markup): 0 PLN');
    });

    test('should handle missing prices gracefully', () => {
      const dataWithoutPrices = {
        ...mockCalculationData,
        prices: {}
      };

      logRyczaltCalculationSummary(dataWithoutPrices);

      expect(console.log).toHaveBeenCalledWith('=== RYCZAŁT CALCULATION SUMMARY ===');
      expect(console.log).toHaveBeenCalledWith('Employment (5×undefined): 400 PLN');
      expect(console.log).toHaveBeenCalledWith('=== END RYCZAŁT SUMMARY ===');
    });

    test('should handle undefined calculation data', () => {
      const undefinedData = {
        umowaOPraceOsob: undefined,
        umowyCywilnoprawneOsob: undefined,
        pfronIleOsob: undefined,
        a1CzyWystepuja: undefined,
        ryczaltTotalMobilityPackages: undefined,
        br00070Cost: undefined,
        br00071Cost: undefined,
        pfronCost: undefined,
        aiCost: undefined,
        br00076Cost: undefined,
        subtotalRyczaltCost: undefined,
        totalRyczaltCost: undefined,
        prices: {
          BR00070: 80,
          BR00071: 70,
          BR00080: 50,
          BR00165: 120,
          BR00076: 50
        }
      };

      // Should not throw an error
      expect(() => logRyczaltCalculationSummary(undefinedData)).not.toThrow();
    });
  });

  describe('Error Handling Edge Cases', () => {
    test('should handle process.cwd() errors', () => {
      const originalCwd = process.cwd;
      process.cwd = vi.fn(() => {
        throw new Error('Cannot get current directory');
      });

      fs.existsSync.mockReturnValue(true);

      const result = logRyczaltCalculation(mockDealId, mockCalculationData, mockDealProperties);

      expect(console.error).toHaveBeenCalledWith(
        'Error logging Ryczałt calculation:',
        expect.any(Error)
      );
      expect(result).toBeNull();

      // Restore original function
      process.cwd = originalCwd;
    });

    test('should handle path.join errors', () => {
      path.join.mockImplementation(() => {
        throw new Error('Path join failed');
      });

      const result = logRyczaltCalculation(mockDealId, mockCalculationData, mockDealProperties);

      expect(console.error).toHaveBeenCalledWith(
        'Error logging Ryczałt calculation:',
        expect.any(Error)
      );
      expect(result).toBeNull();
    });

    test('should handle path.dirname errors', () => {
      fs.existsSync.mockReturnValue(false);
      path.dirname.mockImplementation(() => {
        throw new Error('Path dirname failed');
      });

      const result = logRyczaltCalculation(mockDealId, mockCalculationData, mockDealProperties);

      expect(console.error).toHaveBeenCalledWith(
        'Error logging Ryczałt calculation:',
        expect.any(Error)
      );
      expect(result).toBeNull();
    });
  });
});
