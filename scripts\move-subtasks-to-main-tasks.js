#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to move all subtasks from a main task to become new main tasks
 * in either "Cykliczne Listy Płac" or "Cykliczne DRA" project
 */

import { config } from 'dotenv';
import readline from 'readline';

// Load environment variables
config();

const ASANA_BASE_URL = 'https://app.asana.com/api/1.0';
const ASANA_ACCESS_TOKEN = process.env.ASANA_ACCESS_TOKEN;

// Section mapping
const TARGET_SECTIONS = {
    'listy_plac': 'Cykliczne Listy Płac',
    'dra': 'Cykliczne DRA'
};

/**
 * Make API request to Asana
 */
async function makeAsanaRequest(endpoint, method = 'GET', data = null) {
    const url = `${ASANA_BASE_URL}${endpoint}`;
    const options = {
        method,
        headers: {
            'Authorization': `Bearer ${ASANA_ACCESS_TOKEN}`,
            'Content-Type': 'application/json'
        }
    };

    if (data && (method === 'POST' || method === 'PUT')) {
        options.body = JSON.stringify({ data });
    }

    const response = await fetch(url, options);
    
    if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Asana API error: ${response.status} ${response.statusText} - ${errorText}`);
    }

    return await response.json();
}

/**
 * Get task details
 */
async function getTaskDetails(taskGid) {
    console.log(`Fetching details for task: ${taskGid}`);
    const response = await makeAsanaRequest(`/tasks/${taskGid}`);
    return response.data;
}

/**
 * Get all subtasks of a task
 */
async function getSubtasks(taskGid) {
    console.log(`Fetching subtasks for task: ${taskGid}`);
    const response = await makeAsanaRequest(`/tasks/${taskGid}/subtasks`);
    return response.data;
}

/**
 * Get project details by ID
 */
async function getProjectDetails(projectGid) {
    console.log(`Fetching project details for: ${projectGid}`);
    const response = await makeAsanaRequest(`/projects/${projectGid}`);
    return response.data;
}

/**
 * Get all sections in a project
 */
async function getProjectSections(projectGid) {
    console.log(`Fetching sections for project: ${projectGid}`);
    const response = await makeAsanaRequest(`/projects/${projectGid}/sections`);
    return response.data;
}

/**
 * Find section by name in a project
 */
async function findSectionByName(projectGid, sectionName) {
    console.log(`Searching for section "${sectionName}" in project ${projectGid}`);
    const sections = await getProjectSections(projectGid);

    const section = sections.find(s => s.name === sectionName);
    if (!section) {
        throw new Error(`Section "${sectionName}" not found in project`);
    }

    console.log(`Found section: ${section.name} (${section.gid})`);
    return section;
}

/**
 * Remove subtask relationship (make it a main task)
 */
async function removeSubtaskRelationship(subtaskGid, parentTaskGid) {
    console.log(`Removing subtask relationship: ${subtaskGid} from parent ${parentTaskGid}`);
    await makeAsanaRequest(`/tasks/${subtaskGid}/setParent`, 'POST', {
        parent: null
    });
}

/**
 * Add task to project
 */
async function addTaskToProject(taskGid, projectGid) {
    console.log(`Adding task ${taskGid} to project ${projectGid}`);
    await makeAsanaRequest(`/tasks/${taskGid}/addProject`, 'POST', {
        project: projectGid
    });
}

/**
 * Add task to section
 */
async function addTaskToSection(taskGid, sectionGid) {
    console.log(`Adding task ${taskGid} to section ${sectionGid}`);
    await makeAsanaRequest(`/sections/${sectionGid}/addTask`, 'POST', {
        task: taskGid
    });
}

/**
 * Mark task as not completed
 */
async function markTaskAsNotCompleted(taskGid) {
    console.log(`Marking task ${taskGid} as not completed`);
    await makeAsanaRequest(`/tasks/${taskGid}`, 'PUT', {
        completed: false
    });
}

/**
 * Mark task and all its subtasks as not completed recursively
 */
async function markTaskAndSubtasksAsNotCompleted(taskGid) {
    try {
        // Mark the main task as not completed
        await markTaskAsNotCompleted(taskGid);

        // Get all subtasks of this task
        const subtasks = await getSubtasks(taskGid);

        // Recursively mark each subtask and their subtasks as not completed
        for (const subtask of subtasks) {
            await markTaskAndSubtasksAsNotCompleted(subtask.gid);
        }

        console.log(`   ✅ Marked task and ${subtasks.length} subtasks as not completed`);
    } catch (error) {
        console.error(`   ⚠️  Warning: Failed to mark task ${taskGid} as not completed: ${error.message}`);
        // Don't throw error - this is not critical for the main operation
    }
}

/**
 * Get user input
 */
function getUserInput(question) {
    const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
    });

    return new Promise((resolve) => {
        rl.question(question, (answer) => {
            rl.close();
            resolve(answer.trim());
        });
    });
}

/**
 * Main function
 */
async function main() {
    try {
        // Check if Asana token is configured
        if (!ASANA_ACCESS_TOKEN || ASANA_ACCESS_TOKEN === 'your-asana-personal-access-token-here') {
            console.error('❌ ASANA_ACCESS_TOKEN not configured in .env file');
            console.error('Please add your Asana Personal Access Token to the .env file');
            process.exit(1);
        }

        console.log('🚀 Asana Subtask Mover Script');
        console.log('===============================\n');

        // Get user inputs
        const mainTaskGid = await getUserInput('Enter the main task ID (GID): ');
        if (!mainTaskGid) {
            console.error('❌ Task ID is required');
            process.exit(1);
        }

        const targetProjectGid = await getUserInput('Enter the target project ID (GID) where sections are located: ');
        if (!targetProjectGid) {
            console.error('❌ Target project ID is required');
            process.exit(1);
        }

        console.log('\nTarget section options:');
        console.log('1. Cykliczne Listy Płac (listy_plac)');
        console.log('2. Cykliczne DRA (dra)');

        const targetChoice = await getUserInput('Choose target section (listy_plac or dra): ');
        if (!TARGET_SECTIONS[targetChoice]) {
            console.error('❌ Invalid choice. Please choose "listy_plac" or "dra"');
            process.exit(1);
        }

        const targetSectionName = TARGET_SECTIONS[targetChoice];
        console.log(`\n📋 Selected target section: ${targetSectionName}`);

        // Ask for test mode
        const testModeChoice = await getUserInput('\n🧪 Test mode? Only move the first subtask (yes/no): ');
        const testMode = testModeChoice.toLowerCase() === 'yes' || testModeChoice.toLowerCase() === 'y';
        if (testMode) {
            console.log('🧪 Test mode enabled - will only process the first subtask');
        }

        // Get main task details
        console.log('\n🔍 Step 1: Fetching main task details...');
        const mainTask = await getTaskDetails(mainTaskGid);
        console.log(`✅ Main task: "${mainTask.name}"`);
        console.log(`   Completed: ${mainTask.completed}`);
        console.log(`   Workspace: ${mainTask.workspace.name} (${mainTask.workspace.gid})`);

        // Get subtasks
        console.log('\n🔍 Step 2: Fetching subtasks...');
        const subtasks = await getSubtasks(mainTaskGid);
        console.log(`✅ Found ${subtasks.length} subtasks`);

        if (subtasks.length === 0) {
            console.log('ℹ️  No subtasks found. Nothing to move.');
            return;
        }

        // List subtasks
        console.log('\nSubtasks to move:');
        subtasks.forEach((subtask, index) => {
            console.log(`  ${index + 1}. "${subtask.name}" (${subtask.gid}) - Completed: ${subtask.completed}`);
        });

        // Get target project details
        console.log(`\n🔍 Step 3: Getting target project details...`);
        const targetProject = await getProjectDetails(targetProjectGid);
        console.log(`✅ Target project: ${targetProject.name} (${targetProject.gid})`);

        // Find target section
        console.log(`\n🔍 Step 4: Finding target section "${targetSectionName}"...`);
        const targetSection = await findSectionByName(targetProjectGid, targetSectionName);

        // Determine how many subtasks to process
        const subtasksToProcess = testMode ? subtasks.slice(0, 1) : subtasks;
        const processingMessage = testMode
            ? `1 subtask (TEST MODE)`
            : `${subtasks.length} subtasks`;

        // Confirm before proceeding
        const confirmation = await getUserInput(`\n⚠️  Are you sure you want to move ${processingMessage} to section "${targetSectionName}" in project "${targetProject.name}"? (yes/no): `);
        if (confirmation.toLowerCase() !== 'yes' && confirmation.toLowerCase() !== 'y') {
            console.log('❌ Operation cancelled by user');
            return;
        }

        // Process each subtask
        console.log(`\n🔄 Step 5: Moving subtasks... ${testMode ? '(TEST MODE - only first subtask)' : ''}`);
        const results = {
            success: [],
            failed: []
        };

        for (let i = 0; i < subtasksToProcess.length; i++) {
            const subtask = subtasksToProcess[i];
            console.log(`\n📝 Processing subtask ${i + 1}/${subtasksToProcess.length}: "${subtask.name}"`);

            try {
                // Mark task and all its subtasks as not completed
                await markTaskAndSubtasksAsNotCompleted(subtask.gid);
                console.log('   ✅ Marked as not completed (including nested subtasks)');

                // Remove subtask relationship
                await removeSubtaskRelationship(subtask.gid, mainTaskGid);
                console.log('   ✅ Removed subtask relationship');

                // Add to target project
                await addTaskToProject(subtask.gid, targetProject.gid);
                console.log('   ✅ Added to target project');

                // Add to target section
                await addTaskToSection(subtask.gid, targetSection.gid);
                console.log('   ✅ Added to target section');

                results.success.push({
                    gid: subtask.gid,
                    name: subtask.name
                });

                console.log(`   ✅ Successfully moved: "${subtask.name}"`);

            } catch (error) {
                console.error(`   ❌ Failed to move "${subtask.name}": ${error.message}`);
                results.failed.push({
                    gid: subtask.gid,
                    name: subtask.name,
                    error: error.message
                });
            }
        }

        // Summary
        console.log('\n📊 SUMMARY');
        console.log('===========');
        if (testMode) {
            console.log('🧪 TEST MODE: Only processed the first subtask');
            console.log(`📊 Total subtasks available: ${subtasks.length}`);
        }
        console.log(`✅ Successfully moved: ${results.success.length} subtasks`);
        console.log(`❌ Failed to move: ${results.failed.length} subtasks`);
        console.log(`📋 Target project: ${targetProject.name} (${targetProject.gid})`);
        console.log(`📋 Target section: ${targetSectionName} (${targetSection.gid})`);

        if (results.success.length > 0) {
            console.log('\n✅ Successfully moved subtasks:');
            results.success.forEach(task => {
                console.log(`   • "${task.name}" (${task.gid})`);
            });
        }

        if (results.failed.length > 0) {
            console.log('\n❌ Failed to move subtasks:');
            results.failed.forEach(task => {
                console.log(`   • "${task.name}" (${task.gid}) - ${task.error}`);
            });
        }

        console.log('\n🎉 Script completed!');

    } catch (error) {
        console.error('\n💥 Script failed:', error.message);
        process.exit(1);
    }
}

// Run the script
main().catch(console.error);
