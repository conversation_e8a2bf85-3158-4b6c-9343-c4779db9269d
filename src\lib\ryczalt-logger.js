/**
 * Ryczałt Calculation Logger
 * Provides detailed logging of Ryczałt package calculations to separate files
 */

import fs from 'fs';
import path from 'path';

/**
 * Log detailed Ryczałt calculation to a separate file
 * @param {string} dealId - Deal ID for file naming
 * @param {Object} calculationData - All data used in the calculation
 * @param {Object} dealProperties - Original deal properties
 */
export function logRyczaltCalculation(dealId, calculationData, dealProperties) {
    try {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `ryczalt-calculation-${dealId}-${timestamp}.log`;
        const logPath = path.join(process.cwd(), 'logs', filename);
        
        // Ensure logs directory exists
        const logsDir = path.dirname(logPath);
        if (!fs.existsSync(logsDir)) {
            fs.mkdirSync(logsDir, { recursive: true });
        }

        const logContent = generateRyczaltCalculationReport(dealId, calculationData, dealProperties);
        
        fs.writeFileSync(logPath, logContent, 'utf8');
        console.log(`Ryczałt calculation details logged to: ${logPath}`);
        
        return logPath;
    } catch (error) {
        console.error('Error logging Ryczałt calculation:', error);
        // Don't throw - logging should not break the main process
        return null;
    }
}

/**
 * Generate detailed Ryczałt calculation report
 * @param {string} dealId - Deal ID
 * @param {Object} calculationData - Calculation data
 * @param {Object} dealProperties - Deal properties
 * @returns {string} Formatted report
 */
function generateRyczaltCalculationReport(dealId, calculationData, dealProperties) {
    const {
        umowaOPraceOsob,
        umowyCywilnoprawneOsob,
        ppkIleOsob,
        pfronIleOsob,
        a1CzyWystepuja,
        ryczaltMobilityPackages,
        ryczaltUcpMobilityPackages,
        ryczaltTotalMobilityPackages,
        ktoRobiImport,
        br00070Cost,
        br00071Cost,
        pfronCost,
        aiCost,
        br00076Cost,
        br00077Cost,
        br00078Cost,
        br00079Cost,
        subtotalRyczaltCost,
        totalRyczaltCost,
        prices
    } = calculationData;

    return `
================================================================================
                        RYCZAŁT PACKAGE CALCULATION REPORT
================================================================================

Deal ID: ${dealId}
Calculation Date: ${new Date().toISOString()}
Package Type: Ryczałt (BR00069)

================================================================================
                              INPUT DATA
================================================================================

Employee Counts:
  • Employment Contracts (umowa_o_prace___liczba_osob): ${umowaOPraceOsob}
  • Civil Contracts (umowy_cywilnoprawne___liczba_pracownikow): ${umowyCywilnoprawneOsob}
  • PPK Persons (ppk___ile_osob_): ${ppkIleOsob}
  • PFRON Persons (pfron___ile_osob_): ${pfronIleOsob}
  • A1 Certificates (a1___czy_wystepuja_): ${a1CzyWystepuja}

Mobility Packages:
  • Standard Mobility Packages (ile_jest_pakietow_mobilnosci_): ${ryczaltMobilityPackages}
  • UCP Mobility Packages (ucp___ile_jest_pakietow_mobilnosci_): ${ryczaltUcpMobilityPackages}
  • Total Mobility Packages: ${ryczaltTotalMobilityPackages}

PPK Import Setting:
  • Who handles PPK import (kto_robi_import_do_moje_ppk): "${ktoRobiImport}"
  • Biuro handles import: ${ktoRobiImport.toLowerCase().includes('biuro') ? 'YES' : 'NO'}

================================================================================
                           COST CALCULATION BREAKDOWN
================================================================================

Individual Component Costs:

1. Employment Contracts (BR00070 equivalent):
   Formula: ${umowaOPraceOsob} persons × ${prices.BR00070} PLN = ${br00070Cost} PLN

2. Civil Contracts (BR00071 equivalent):
   Formula: ${umowyCywilnoprawneOsob} persons × ${prices.BR00071} PLN = ${br00071Cost} PLN

3. PFRON Handling (BR00080 equivalent):
   Formula: ${pfronIleOsob} persons × ${prices.BR00080} PLN = ${pfronCost} PLN

4. A1 Certificates (BR00165 equivalent):
   Formula: ${a1CzyWystepuja} certificates × ${prices.BR00165} PLN = ${aiCost} PLN

5. Mobility Packages (BR00076 equivalent):
   Formula: ${ryczaltTotalMobilityPackages} packages × ${prices.BR00076} PLN = ${br00076Cost} PLN

================================================================================
                              COST SUMMARY
================================================================================

Subtotal (before markup):
  BR00070 equivalent: ${br00070Cost.toString().padStart(8)} PLN
  BR00071 equivalent: ${br00071Cost.toString().padStart(8)} PLN
  BR00080 equivalent: ${pfronCost.toString().padStart(8)} PLN
  BR00165 equivalent: ${aiCost.toString().padStart(8)} PLN
  BR00076 equivalent: ${br00076Cost.toString().padStart(8)} PLN
  ────────────────────────────────
  SUBTOTAL:          ${subtotalRyczaltCost.toString().padStart(8)} PLN

Markup Calculation:
  Subtotal: ${subtotalRyczaltCost} PLN
  Markup: 10%
  Markup amount: ${Math.round(subtotalRyczaltCost * 0.1)} PLN
  
FINAL RYCZAŁT PRICE: ${totalRyczaltCost} PLN

================================================================================
                            BUSINESS LOGIC NOTES
================================================================================

• The Ryczałt package (BR00069) consolidates all payroll-related services into 
  a single line item with a custom price.

• Individual line items (BR00070, BR00071, BR00076, BR00080, BR00165) are NOT
  added separately when Ryczałt is selected.

• The final price includes a 10% markup over the sum of individual component costs.

• Mobility packages are set to 0 quantity for separate line items since they're
  included in the Ryczałt price.

• This calculation ensures the client pays a bundled rate that's slightly 
  discounted compared to individual service pricing.

================================================================================
                              RAW DEAL PROPERTIES
================================================================================

All relevant deal properties used in calculation:
${Object.entries(dealProperties)
    .filter(([key]) => [
        'pakiet_kadrowo_placowy',
        'umowa_o_prace___liczba_osob',
        'umowy_cywilnoprawne___liczba_pracownikow',
        'ppk___ile_osob_',
        'pfron___ile_osob_',
        'a1___czy_wystepuja_',
        'ile_jest_pakietow_mobilnosci_',
        'ucp___ile_jest_pakietow_mobilnosci_',
        'kto_robi_import_do_moje_ppk',
        'dodatkowe_skladniki_wynagrodzenia'
    ].includes(key))
    .map(([key, value]) => `  ${key}: "${value}"`)
    .join('\n')}

================================================================================
                                END OF REPORT
================================================================================
`;
}

/**
 * Log Ryczałt calculation summary to console with enhanced formatting
 * @param {Object} calculationData - Calculation data
 */
export function logRyczaltCalculationSummary(calculationData) {
    const {
        umowaOPraceOsob,
        umowyCywilnoprawneOsob,
        ppkIleOsob,
        pfronIleOsob,
        a1CzyWystepuja,
        ryczaltTotalMobilityPackages,
        br00070Cost,
        br00071Cost,
        pfronCost,
        aiCost,
        br00076Cost,
        br00077Cost,
        br00078Cost,
        br00079Cost,
        subtotalRyczaltCost,
        totalRyczaltCost,
        prices
    } = calculationData;

    console.log('=== RYCZAŁT CALCULATION SUMMARY ===');
    console.log(`Employment (${umowaOPraceOsob}×${prices.BR00070}): ${br00070Cost} PLN`);
    console.log(`Civil (${umowyCywilnoprawneOsob}×${prices.BR00071}): ${br00071Cost} PLN`);
    console.log(`PFRON (${pfronIleOsob}×${prices.BR00080}): ${pfronCost} PLN`);
    console.log(`A1 (${a1CzyWystepuja}×${prices.BR00165}): ${aiCost} PLN`);
    console.log(`Mobility (${ryczaltTotalMobilityPackages}×${prices.BR00076}): ${br00076Cost} PLN`);
    console.log(`Subtotal: ${subtotalRyczaltCost} PLN`);
    console.log(`Final (with 10% markup): ${totalRyczaltCost} PLN`);
    console.log('=== END RYCZAŁT SUMMARY ===');
}
