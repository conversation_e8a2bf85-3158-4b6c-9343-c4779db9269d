#!/usr/bin/env node

/**
 * Standalone Test Environment Validation Script
 * 
 * This script runs the environment validation and exits with appropriate codes.
 * It's designed to be called directly from npm scripts.
 */

// Load environment variables
import { config } from 'dotenv';
config();

import { validateEnvironment } from './validate-test-environment.js';

validateEnvironment()
  .then(isValid => {
    if (isValid) {
      console.log('\n🎉 Environment validation passed! Ready to run tests.');
      process.exit(0);
    } else {
      console.log('\n❌ Environment validation failed. Please fix the issues above.');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error(`\n💥 Validation failed with error: ${error.message}`);
    console.error(error.stack);
    process.exit(1);
  });
