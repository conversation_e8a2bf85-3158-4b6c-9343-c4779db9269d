import tailwindcss from '@tailwindcss/vite';
import { sveltekit } from '@sveltejs/kit/vite';
import { defineConfig } from 'vite';

export default defineConfig({
	plugins: [tailwindcss(), sveltekit()],
	optimizeDeps: {
		exclude: ['chromium-bidi', 'playwright-core']
	},
	test: {
		environment: 'node',
		globals: true,
		setupFiles: ['./tests/setup.js'],
		include: [
			'tests/**/*.test.js',
			'tests/**/*.spec.js'
		],
		coverage: {
			provider: 'v8',
			reporter: ['text', 'lcov', 'html'],
			reportsDirectory: './coverage',
			include: [
				'src/lib/**/*.js',
				'src/routes/api/**/*.js'
			],
			exclude: [
				'src/lib/**/*.test.js',
				'src/lib/**/*.spec.js'
			]
		},
		testTimeout: 30000,
		alias: {
			'$lib': new URL('./src/lib', import.meta.url).pathname,
			'$app': new URL('./src/app', import.meta.url).pathname
		}
	}
});
