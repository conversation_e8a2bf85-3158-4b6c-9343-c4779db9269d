import { describe, test, expect, beforeEach, vi } from 'vitest';

/**
 * Unit Tests for PIT Packages Module
 * 
 * Tests only the pit-packages.js module functions in isolation.
 * All external dependencies are mocked.
 */

// Mock dependencies
vi.mock('../../src/lib/price-fetcher.js', () => ({
  getProductPrice: vi.fn()
}));

import {
  getPitPackagePrices,
  selectPitPackageForSimplifiedAccounting
} from '../../src/lib/pit-packages.js';

import { getProductPrice } from '../../src/lib/price-fetcher.js';

describe('PIT Packages', () => {
  const mockAccessToken = 'test-token-**********';

  beforeEach(() => {
    vi.clearAllMocks();
    // Mock console methods to avoid noise in tests
    vi.spyOn(console, 'log').mockImplementation(() => {});
    vi.spyOn(console, 'error').mockImplementation(() => {});
  });

  describe('getPitPackagePrices', () => {
    test('should fetch PIT package prices successfully', async () => {
      const mockPrices = {
        'BR00094': 150,  // PIT BASE
        'BR00095': 250,  // PIT SILVER
        'BR00096': 350,  // PIT GOLD
        'BR00097': 450   // PIT PLATINUM
      };

      getProductPrice.mockImplementation((sku) => {
        return Promise.resolve(mockPrices[sku] || null);
      });

      const result = await getPitPackagePrices(mockAccessToken);

      expect(getProductPrice).toHaveBeenCalledWith('BR00094', mockAccessToken);
      expect(getProductPrice).toHaveBeenCalledWith('BR00095', mockAccessToken);
      expect(getProductPrice).toHaveBeenCalledWith('BR00096', mockAccessToken);
      expect(getProductPrice).toHaveBeenCalledWith('BR00097', mockAccessToken);

      expect(result).toEqual({
        packages: {
          BASE: { sku: 'BR00094', price: 150 },
          SILVER: { sku: 'BR00095', price: 250 },
          GOLD: { sku: 'BR00096', price: 350 },
          PLATINUM: { sku: 'BR00097', price: 450 }
        }
      });
    });

    test('should handle missing prices gracefully', async () => {
      getProductPrice.mockResolvedValue(null);

      const result = await getPitPackagePrices(mockAccessToken);

      expect(result).toEqual({
        packages: {
          BASE: { sku: 'BR00094', price: null },
          SILVER: { sku: 'BR00095', price: null },
          GOLD: { sku: 'BR00096', price: null },
          PLATINUM: { sku: 'BR00097', price: null }
        }
      });
    });

    test('should handle price fetching errors', async () => {
      getProductPrice.mockRejectedValue(new Error('Price fetch failed'));

      await expect(getPitPackagePrices(mockAccessToken))
        .rejects.toThrow('Failed to fetch PIT package prices: Price fetch failed');
    });
  });

  describe('selectPitPackageForSimplifiedAccounting', () => {
    test('should select BASE package for BASE accounting package', async () => {
      const mockPricingData = {
        packages: {
          BASE: { sku: 'BR00094', price: 150 },
          SILVER: { sku: 'BR00095', price: 250 },
          GOLD: { sku: 'BR00096', price: 350 },
          PLATINUM: { sku: 'BR00097', price: 450 }
        }
      };

      getProductPrice.mockImplementation((sku) => {
        const prices = {
          'BR00094': 150,
          'BR00095': 250,
          'BR00096': 350,
          'BR00097': 450
        };
        return Promise.resolve(prices[sku]);
      });

      const result = await selectPitPackageForSimplifiedAccounting('BASE', mockAccessToken);

      expect(result).toEqual([{
        sku: 'BR00094',
        quantity: 1,
        description: 'PIT BASE package for simplified accounting',
        price: 150
      }]);
    });

    test('should select SILVER package for SILVER accounting package', async () => {
      getProductPrice.mockImplementation((sku) => {
        const prices = {
          'BR00094': 150,
          'BR00095': 250,
          'BR00096': 350,
          'BR00097': 450
        };
        return Promise.resolve(prices[sku]);
      });

      const result = await selectPitPackageForSimplifiedAccounting('SILVER', mockAccessToken);

      expect(result).toEqual([{
        sku: 'BR00095',
        quantity: 1,
        description: 'PIT SILVER package for simplified accounting',
        price: 250
      }]);
    });

    test('should return empty array when no package name provided', async () => {
      const result = await selectPitPackageForSimplifiedAccounting(null, mockAccessToken);

      expect(result).toEqual([]);
    });

    test('should return empty array when empty package name provided', async () => {
      const result = await selectPitPackageForSimplifiedAccounting('', mockAccessToken);

      expect(result).toEqual([]);
    });

    test('should return empty array for unknown package name', async () => {
      getProductPrice.mockImplementation((sku) => {
        const prices = {
          'BR00094': 150,
          'BR00095': 250,
          'BR00096': 350,
          'BR00097': 450
        };
        return Promise.resolve(prices[sku]);
      });

      const result = await selectPitPackageForSimplifiedAccounting('UNKNOWN', mockAccessToken);

      expect(result).toEqual([]);
    });

    test('should handle price fetching errors', async () => {
      getProductPrice.mockRejectedValue(new Error('Price fetch failed'));

      await expect(selectPitPackageForSimplifiedAccounting('BASE', mockAccessToken))
        .rejects.toThrow('Failed to select PIT package: Failed to fetch PIT package prices: Price fetch failed');
    });
  });
});
