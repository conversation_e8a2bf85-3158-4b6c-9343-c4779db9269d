import { json, error } from '@sveltejs/kit';
import { env } from '$env/dynamic/private';
import { chromium } from 'playwright-core';
import { initializeBrowsers, getBrowserLaunchOptions } from '$lib/browser-setup.js';
import fs from 'fs';
import path from 'path';
import os from 'os';

/**
 * Download Kaczmarski PDF using <PERSON>wright and attach it to a HubSpot deal
 * @param {import('@sveltejs/kit').RequestEvent} event - The request event
 * @returns {Promise<Response>} - JSON response with the operation result
 */
export async function POST({ request }) {
    const consoleLogs = [];
    const originalConsoleLog = console.log;
    const originalConsoleError = console.error;
    
    // Capture console logs for response
    console.log = (...args) => {
        const message = args.map(arg => 
            typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
        ).join(' ');
        consoleLogs.push(`[LOG] ${message}`);
        originalConsoleLog(...args);
    };
    
    console.error = (...args) => {
        const message = args.map(arg => 
            typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
        ).join(' ');
        consoleLogs.push(`[ERROR] ${message}`);
        originalConsoleError(...args);
    };

    const restore = () => {
        console.log = originalConsoleLog;
        console.error = originalConsoleError;
    };

    let browser = null;
    let tempFilePath = null;

    try {
        // Check API key
        const apiKey = request.headers.get('x-api-key');
        const expectedApiKey = env.API_KEY;

        if (!apiKey || apiKey !== expectedApiKey) {
            restore();
            throw error(401, 'Unauthorized: Invalid or missing API key');
        }

        // Get HubSpot access token from environment
        const hubspotAccessToken = env.HUBSPOT_ACCESS_TOKEN;
        if (!hubspotAccessToken) {
            restore();
            throw error(500, 'Server Error: HubSpot access token not configured');
        }

        // Get Kaczmarski credentials from environment
        const kaczmarskiLogin = env.KACZMARSKI_LOGIN;
        const kaczmarskiPassword = env.KACZMARSKI_PASSWORD;

        // Extract request data
        let requestData = {};
        try {
            requestData = await request.json();
        } catch (err) {
            restore();
            return json({
                success: false,
                error: 'Invalid JSON in request body',
                consoleLogs: consoleLogs
            }, { status: 400 });
        }

        const { dealId } = requestData;

        if (!dealId) {
            restore();
            return json({
                success: false,
                error: 'dealId is required',
                consoleLogs: consoleLogs
            }, { status: 400 });
        }

        console.log(`Starting Kaczmarski PDF download for deal: ${dealId}`);

        // Check if Kaczmarski PDF attachment already exists
        console.log('Checking for existing Kaczmarski PDF attachments...');
        const existingAttachment = await checkForExistingKaczmarskiPdf(dealId, hubspotAccessToken);

        if (existingAttachment) {
            console.log('Kaczmarski PDF attachment already exists for this deal');
            restore();
            return json({
                success: true,
                message: `Kaczmarski PDF already attached to deal ${dealId}`,
                attachment: existingAttachment,
                filename: existingAttachment.fileName,
                skipped: true,
                consoleLogs: consoleLogs
            });
        }

        // Ensure browsers are installed
        console.log('Ensuring browsers are available...');
        const browsersReady = await initializeBrowsers();

        if (!browsersReady) {
            throw new Error('Failed to initialize browsers. Cannot proceed with PDF download.');
        }

        // Launch browser
        console.log('Launching browser...');
        browser = await chromium.launch(getBrowserLaunchOptions());
        const context = await browser.newContext();
        const page = await context.newPage();

        // Add page error handlers with filtering for known non-fatal errors
        page.on('console', msg => {
            const text = msg.text();
            // Filter out known non-fatal console messages
            if (!text.includes('Hotjar not launching') &&
                !text.includes('suspicious userAgent') &&
                !text.includes('GAOptions')) {
                console.log(`Browser console: ${text}`);
            }
        });

        page.on('pageerror', err => {
            const message = err.message;
            // Filter out known non-fatal page errors
            if (!message.includes('GAOptions is not defined') &&
                !message.includes('Hotjar') &&
                !message.includes('analytics')) {
                console.error(`Browser page error: ${message}`);
            }
        });

        // Step 1: Login to Kaczmarski website
        console.log('Navigating to Kaczmarski login page...');
        await page.goto("https://panel.kaczmarskigroup.pl/Client/Authentication/Login", {
            waitUntil: "networkidle"
        });

        // Fill in the login form
        console.log('Filling login credentials...');
        await page.fill('#LoginName', kaczmarskiLogin);
        await page.fill('#Password', kaczmarskiPassword);

        // Click login and wait for navigation
        console.log('Logging in...');
        await Promise.all([
            page.click('#Login-btn'),
            page.waitForNavigation({ waitUntil: 'networkidle' })
        ]);

        console.log('Successfully logged in');

        // Get cookies from the browser session
        const cookies = await context.cookies();
        const cookieString = cookies.map(cookie => `${cookie.name}=${cookie.value}`).join('; ');
        console.log('Obtained cookies from browser session');

        // Step 2: Make the initial request to prepare report
        console.log('Preparing report request...');
        const initialResponse = await fetch("https://panel.kaczmarskigroup.pl/Client/mod/Calvin/Search/PrepareReportPlusAboutMyselfAsync", {
            method: "POST",
            headers: {
                "accept": "*/*",
                "accept-language": "pl-PL,pl;q=0.9,en-US;q=0.8,en;q=0.7",
                "cache-control": "no-cache",
                "content-type": "application/json; charset=UTF-8",
                "pragma": "no-cache",
                "sec-ch-ua": "\"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"",
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": "\"Windows\"",
                "sec-fetch-dest": "empty",
                "sec-fetch-mode": "cors",
                "sec-fetch-site": "same-origin",
                "x-requested-with": "XMLHttpRequest",
                "cookie": cookieString
            },
            body: JSON.stringify({
                "IsOwuAccepted": true
            })
        });

        if (!initialResponse.ok) {
            throw new Error(`Failed to prepare report: ${initialResponse.status} ${initialResponse.statusText}`);
        }

        const initialData = await initialResponse.json();
        console.log('Initial response received:', initialData);

        const { RequestId, TaxId } = initialData;

        if (!RequestId || !TaxId) {
            throw new Error('Missing RequestId or TaxId in initial response');
        }

        // Step 3: Poll until the report is ready
        let isReady = false;
        let fileDownloadName = null;
        let attempts = 0;
        const maxAttempts = 30; // 30 attempts * 2 seconds = 1 minute max

        console.log(`Starting polling for RequestId: ${RequestId}`);

        while (!isReady && attempts < maxAttempts) {
            attempts++;
            
            // Wait for 2 seconds between polls
            await new Promise(resolve => setTimeout(resolve, 2000));

            const progressResponse = await fetch(`https://panel.kaczmarskigroup.pl/Client/mod/Calvin/Search/GetReportPlusProgress?requestId=${RequestId}&taxId=${TaxId}`, {
                method: "POST",
                headers: {
                    "accept": "*/*",
                    "accept-language": "pl-PL,pl;q=0.9,en-US;q=0.8,en;q=0.7",
                    "cache-control": "no-cache",
                    "content-type": "application/json; charset=utf-8",
                    "pragma": "no-cache",
                    "sec-ch-ua": "\"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"",
                    "sec-ch-ua-mobile": "?0",
                    "sec-ch-ua-platform": "\"Windows\"",
                    "sec-fetch-dest": "empty",
                    "sec-fetch-mode": "cors",
                    "sec-fetch-site": "same-origin",
                    "x-requested-with": "XMLHttpRequest",
                    "cookie": cookieString
                }
            });

            if (!progressResponse.ok) {
                throw new Error(`Failed to check progress: ${progressResponse.status} ${progressResponse.statusText}`);
            }

            const progressData = await progressResponse.json();
            console.log(`Progress: ${progressData.PercentProgress}%, IsReady: ${progressData.IsReady}, Attempt: ${attempts}/${maxAttempts}`);

            if (progressData.IsReady) {
                isReady = true;
                fileDownloadName = progressData.FileDownloadName;
            }
        }

        if (!isReady) {
            throw new Error(`Report preparation timed out after ${maxAttempts} attempts`);
        }

        // Step 4: Download the PDF file
        console.log(`Report is ready. Downloading file: ${fileDownloadName}`);

        const encodedFileName = encodeURIComponent(fileDownloadName);
        const pdfResponse = await fetch(`https://panel.kaczmarskigroup.pl/Client/mod/Calvin/Search/GetDisclosureReportPlus?requestId=${RequestId}&fileDownloadName=${encodedFileName}`, {
            method: "GET",
            headers: {
                "accept": "*/*",
                "accept-language": "pl-PL,pl;q=0.9,en-US;q=0.8,en;q=0.7",
                "cache-control": "no-cache",
                "pragma": "no-cache",
                "sec-ch-ua": "\"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"",
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": "\"Windows\"",
                "sec-fetch-dest": "empty",
                "sec-fetch-mode": "cors",
                "sec-fetch-site": "same-origin",
                "cookie": cookieString
            }
        });

        if (!pdfResponse.ok) {
            throw new Error(`Failed to download PDF: ${pdfResponse.status} ${pdfResponse.statusText}`);
        }

        // Get the PDF as an array buffer
        const pdfBuffer = await pdfResponse.arrayBuffer();

        // Save the PDF to temp location
        const filename = fileDownloadName || `Kaczmarski_Report_${TaxId}_${Date.now()}.pdf`;
        tempFilePath = path.join(os.tmpdir(), filename);

        fs.writeFileSync(tempFilePath, Buffer.from(pdfBuffer));
        console.log(`PDF saved temporarily to: ${tempFilePath}`);

        // Step 5: Upload PDF as attachment to HubSpot deal
        console.log(`Uploading PDF as attachment to deal ${dealId}...`);
        
        const attachmentResult = await uploadFileToHubSpotDeal(dealId, tempFilePath, filename, hubspotAccessToken);
        
        console.log('PDF successfully attached to HubSpot deal:', attachmentResult);

        // Clean up
        await context.close();
        await browser.close();
        browser = null;

        // Delete temp file
        if (tempFilePath && fs.existsSync(tempFilePath)) {
            fs.unlinkSync(tempFilePath);
            console.log('Temporary file deleted');
            tempFilePath = null;
        }

        restore();

        return json({
            success: true,
            message: `Kaczmarski PDF successfully downloaded and attached to deal ${dealId}`,
            attachment: attachmentResult,
            filename: filename,
            consoleLogs: consoleLogs
        });

    } catch (err) {
        console.error('Error in Kaczmarski PDF download:', err);
        
        // Clean up resources
        if (browser) {
            try {
                await browser.close();
            } catch (e) {
                console.error('Error closing browser:', e);
            }
        }

        // Clean up temp file
        if (tempFilePath && fs.existsSync(tempFilePath)) {
            try {
                fs.unlinkSync(tempFilePath);
                console.log('Temporary file deleted after error');
            } catch (e) {
                console.error('Error deleting temp file:', e);
            }
        }

        restore();

        return json({
            success: false,
            error: err.message,
            consoleLogs: consoleLogs
        }, { status: 500 });
    }
}

/**
 * Check if a Kaczmarski PDF attachment already exists for the deal
 * @param {string} dealId - HubSpot deal ID
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<Object|null>} Existing attachment info or null if not found
 */
async function checkForExistingKaczmarskiPdf(dealId, accessToken) {
    try {
        // Get notes associated with the deal
        const notesResponse = await fetch(`https://api.hubapi.com/crm/v4/objects/deals/${dealId}/associations/notes`, {
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!notesResponse.ok) {
            console.log(`Warning: Could not fetch deal notes: ${notesResponse.status} ${notesResponse.statusText}`);
            return null;
        }

        const notesData = await notesResponse.json();
        const noteIds = notesData.results?.map(result => result.toObjectId) || [];

        if (noteIds.length === 0) {
            console.log('No notes found on deal');
            return null;
        }

        console.log(`Found ${noteIds.length} note(s) on deal`);

        // Check each note for attachments
        for (const noteId of noteIds) {
            try {
                const noteResponse = await fetch(`https://api.hubapi.com/crm/v3/objects/notes/${noteId}?properties=hs_attachment_ids,hs_note_body`, {
                    headers: {
                        'Authorization': `Bearer ${accessToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (!noteResponse.ok) {
                    console.log(`Warning: Could not fetch note ${noteId}`);
                    continue;
                }

                const noteData = await noteResponse.json();
                const attachmentIds = noteData.properties?.hs_attachment_ids;
                const noteBody = noteData.properties?.hs_note_body || '';

                if (!attachmentIds) {
                    continue;
                }

                // Parse attachment IDs (they might be comma-separated)
                const attachmentIdList = attachmentIds.split(',').map(id => id.trim()).filter(id => id);

                if (attachmentIdList.length === 0) {
                    continue;
                }

                console.log(`Found ${attachmentIdList.length} attachment(s) in note ${noteId}`);

                // Check each attachment to see if it's a Kaczmarski PDF
                for (const attachmentId of attachmentIdList) {
                    try {
                        const fileResponse = await fetch(`https://api.hubapi.com/files/v3/files/${attachmentId}`, {
                            headers: {
                                'Authorization': `Bearer ${accessToken}`,
                                'Content-Type': 'application/json'
                            }
                        });

                        if (!fileResponse.ok) {
                            console.log(`Warning: Could not fetch file details for attachment ${attachmentId}`);
                            continue;
                        }

                        const fileData = await fileResponse.json();
                        const fileName = fileData.name || '';

                        // Check if this looks like a Kaczmarski PDF
                        // Look for patterns like "Raport o firmie", "Kaczmarski", or similar naming conventions
                        const isKaczmarskiPdf = fileName.toLowerCase().includes('raport o firmie') ||
                                               fileName.toLowerCase().includes('kaczmarski') ||
                                               fileName.toLowerCase().includes('disclosure') ||
                                               noteBody.toLowerCase().includes('kaczmarski') ||
                                               (fileName.toLowerCase().endsWith('.pdf') && fileName.includes('NIP'));

                        if (isKaczmarskiPdf) {
                            console.log(`Found existing Kaczmarski PDF: ${fileName}`);
                            return {
                                fileId: fileData.id,
                                fileUrl: fileData.url,
                                fileName: fileData.name,
                                dealId: dealId,
                                noteId: noteId,
                                createdAt: fileData.createdAt
                            };
                        }
                    } catch (fileError) {
                        console.log(`Error checking attachment ${attachmentId}:`, fileError.message);
                        continue;
                    }
                }
            } catch (noteError) {
                console.log(`Error checking note ${noteId}:`, noteError.message);
                continue;
            }
        }

        console.log('No Kaczmarski PDF attachments found');
        return null;

    } catch (error) {
        console.log('Error checking for existing attachments:', error.message);
        return null; // Don't fail the entire process if we can't check attachments
    }
}

/**
 * Upload file as attachment to HubSpot deal
 * @param {string} dealId - HubSpot deal ID
 * @param {string} filePath - Path to the file to upload
 * @param {string} filename - Name of the file
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<Object>} Upload result
 */
async function uploadFileToHubSpotDeal(dealId, filePath, filename, accessToken) {
    // Step 1: Upload file to HubSpot Files API
    const fileBuffer = fs.readFileSync(filePath);
    const formData = new FormData();
    
    // Create a Blob from the buffer for FormData
    const blob = new Blob([fileBuffer], { type: 'application/pdf' });
    formData.append('file', blob, filename);
    formData.append('options', JSON.stringify({
        access: 'PRIVATE',
        ttl: 'P3M', // 3 months
        overwrite: false
    }));
    formData.append('folderPath', '/kaczmarski-pdfs'); // Add required folder path

    const uploadResponse = await fetch('https://api.hubapi.com/files/v3/files', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${accessToken}`
        },
        body: formData
    });

    if (!uploadResponse.ok) {
        const errorText = await uploadResponse.text();
        throw new Error(`Failed to upload file to HubSpot: ${uploadResponse.status} ${uploadResponse.statusText} - ${errorText}`);
    }

    const uploadResult = await uploadResponse.json();
    console.log('File uploaded to HubSpot:', uploadResult);

    // Step 2: Create a Note engagement with the file attachment and associate it with the deal
    const noteResponse = await fetch('https://api.hubapi.com/crm/v3/objects/notes', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            properties: {
                hs_timestamp: new Date().toISOString(),
                hs_note_body: `Kaczmarski PDF report attached: ${filename}`,
                hs_attachment_ids: uploadResult.id
            },
            associations: [
                {
                    to: {
                        id: dealId
                    },
                    types: [
                        {
                            associationCategory: "HUBSPOT_DEFINED",
                            associationTypeId: 214 // Note to Deal association
                        }
                    ]
                }
            ]
        })
    });

    if (!noteResponse.ok) {
        const errorText = await noteResponse.text();
        throw new Error(`Failed to create note with attachment: ${noteResponse.status} ${noteResponse.statusText} - ${errorText}`);
    }

    const noteResult = await noteResponse.json();
    console.log('Note with attachment created:', noteResult);

    return {
        fileId: uploadResult.id,
        fileUrl: uploadResult.url,
        fileName: uploadResult.name,
        dealId: dealId,
        noteId: noteResult.id,
        noteCreatedAt: noteResult.createdAt
    };
}
