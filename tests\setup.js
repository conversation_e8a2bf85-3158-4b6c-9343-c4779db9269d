// Global test setup
import { vi } from 'vitest';
import { config } from 'dotenv';
config();

// Mock console methods to reduce noise in tests unless explicitly needed
global.console = {
  ...console,
  log: vi.fn(),
  debug: vi.fn(),
  info: vi.fn(),
  warn: vi.fn(),
  error: console.error // Keep error for debugging
};

// Ensure we have a real HubSpot access token for integration tests
if (!process.env.HUBSPOT_ACCESS_TOKEN) {
  console.warn('⚠️  HUBSPOT_ACCESS_TOKEN not found. Integration tests will be limited.');
  console.warn('   Set HUBSPOT_ACCESS_TOKEN environment variable for full testing.');
}

// Global test utilities
global.testUtils = {
  // Helper to restore console for specific tests
  enableConsole: () => {
    global.console.log = console.log;
    global.console.debug = console.debug;
    global.console.info = console.info;
    global.console.warn = console.warn;
  },

  // Helper to mock console again
  mockConsole: () => {
    global.console.log = vi.fn();
    global.console.debug = vi.fn();
    global.console.info = vi.fn();
    global.console.warn = vi.fn();
  },
  
  // Helper to create mock deal properties
  createMockDealProperties: (overrides = {}) => ({
    'faktury_rachunki_sprzedazowe___ile_': '0',
    'faktury_rachunki_zakupu___ile_': '0',
    'faktury_walutowe___ile_miesiecznie_': '0',
    'dokumenty_wewnetrzne_wdt__wnt_itp': '0',
    'operacje_kp_kw_walutowe': '0',
    'kp_kw___banki_': '0',
    'kp_kw_gotowka': '0',
    'rodzaj_ksiegowosci': 'Pełna księgowość',
    'vat___status_podatnika': '',
    'jezyk_obslugi': 'Polski',
    'pakiet_kadrowo_placowy': '',
    'umowa_o_prace___liczba_osob': '0',
    'umowy_cywilnoprawne___liczba_pracownikow': '0',
    'ppk___ile_osob_': '0',
    'pfron___ile_osob_': '0',
    'a1___czy_wystepuja_': '0',
    'ile_transakcji_sprzedazy_w_miesiacu_': '0',
    'srodki_trwale_i_wartosci_niematerialne_i_prawne___ile_': '0',
    'kasy_fiskalne___ile_': '0',
    'pytania_do_msp': '',
    'ilosc_kont_bankowych___raporty_dzienne': '0',
    'wyciagi_bankowe___liczba_': '0',
    'liczba_kanalow_platnosci': '0',
    ...overrides
  })
};

// Global constants for testing
global.TEST_CONSTANTS = {
  PACKAGE_MINIMUMS: {
    BASE: 5,
    SILVER: 50,
    GOLD: 110,
    PLATINUM: 200
  },
  PACKAGE_JUMP_THRESHOLDS: {
    BASE_TO_SILVER: 22,
    SILVER_TO_GOLD: 104,
    GOLD_TO_PLATINUM: 189
  },
  SAMPLE_DEAL_ID: '************'
};
