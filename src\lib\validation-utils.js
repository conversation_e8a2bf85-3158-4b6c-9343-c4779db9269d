/**
 * Validation utilities and constants for the line item management system
 */

// Property type constants - now only supports the update trigger field
export const PROPERTY_TYPES = {
    UPDATE_TRIGGER: 'aktualizuj_dane'
};

// VAT configuration constants
export const VAT_CONFIG = {
    VALUES_REQUIRING_BR00032: ['VAT EU', 'VAT 8', 'VAT 9M'],
    VALUES_REQUIRING_BR00033: ['VAT OSS'],
    VALUES_REQUIRING_DOUBLE_VAT_OSS: ['VAT IOSS'] // When combined with VAT OSS, adds two VAT OSS positions
};

// SKU constants
export const SKUS = {
    // VAT related
    BR00032: 'BR00032', // VAT EU/8/9M
    BR00033: 'BR00033', // VAT OSS

    // Language related
    BR00129: 'BR00129', // Non-Polish language

    // Quantity-based
    BR00030: 'BR00030', // Fixed assets
    BR00031: 'BR00031', // Cash registers
    BR00076: 'BR00076', // Mobility packages
    BR00077: 'BR00077', // PPK persons
    BR00080: 'BR00080', // PFRON persons
    BR00165: 'BR00165', // A1 certificates

    // Payroll packages
    BR00069: 'BR00069', // Ryczałt package (sum of BR00070+BR00071+PFRON+AI)
    BR00070: 'BR00070', // Premium payroll package (umowa o pracę)
    BR00071: 'BR00071', // Premium payroll package (umowy cywilnoprawne)
    BR00072: 'BR00072', // Employment contracts
    BR00073: 'BR00073', // Civil law contracts
    BR00074: 'BR00074', // Kadry i płace - z teczkami
    BR00075: 'BR00075', // Additional salary components
    BR00078: 'BR00078', // PPK present
    BR00079: 'BR00079', // PPK import by office

    // MSP
    BR00111: 'BR00111', // MSP audit

    // New payroll-related SKUs for KADRY
    BR00114: 'BR00114', // PIT 11
    BR00115: 'BR00115', // PIT 4
    BR00117: 'BR00117', // IWA (for 10+ employees)
    BR00118: 'BR00118', // IFT (for foreign workers)
    BR00119: 'BR00119', // Additional SKU (for 25+ employees)
    BR00081: 'BR00081', // Additional SKU (for 25+ employees)

    // Banking and payment channels
    BR00012: 'BR00012', // Monthly bank reports and payment channels
    BR00013: 'BR00013', // Bank statement processing
    BR00130: 'BR00130', // Daily bank reports

    // Individual document pricing
    BR00022: 'BR00022', // Individual document price for full accounting
    BR00023: 'BR00023', // Individual document price for SILVER full accounting
    BR00024: 'BR00024', // Individual document price for GOLD full accounting
    BR00025: 'BR00025', // Individual document price for PLATINUM full accounting
    BR00015: 'BR00015', // Individual document price for BASE simplified accounting
    BR00016: 'BR00016', // Individual document price for SILVER simplified accounting
    BR00017: 'BR00017', // Individual document price for GOLD simplified accounting
    BR00018: 'BR00018', // Individual document price for PLATINUM simplified accounting

    // Accounting packages - Full accounting
    BR00003: 'BR00003', // BASE package for full accounting
    BR00004: 'BR00004', // SILVER package for full accounting
    BR00005: 'BR00005', // GOLD package for full accounting
    BR00006: 'BR00006', // PLATINUM package for full accounting

    // Accounting packages - Simplified accounting
    BR00007: 'BR00007', // BASE package for simplified accounting
    BR00008: 'BR00008', // SILVER package for simplified accounting
    BR00009: 'BR00009', // GOLD package for simplified accounting
    BR00010: 'BR00010', // PLATINUM package for simplified accounting

    // Additional accounting packages - Full accounting
    BR00027: 'BR00027', // GOLD additional 50 documents pack for full accounting
    BR00028: 'BR00028', // PLATINUM additional 50 documents pack for full accounting
    BR00029: 'BR00029', // PLATINUM additional 200 documents pack for full accounting

    // Additional accounting packages - Simplified accounting
    BR00019: 'BR00019', // GOLD additional 50 documents pack for simplified accounting
    BR00020: 'BR00020', // PLATINUM additional 50 documents pack for simplified accounting
    BR00021: 'BR00021', // PLATINUM additional 200 documents pack for simplified accounting

    // E-commerce packages
    BR00058: 'BR00058', // E-commerce up to 200 transactions (full accounting)
    BR00059: 'BR00059', // E-commerce up to 1,000 transactions (full accounting)
    BR00060: 'BR00060', // E-commerce up to 5,000 transactions (full accounting)
    BR00061: 'BR00061', // E-commerce up to 20,000 transactions (full accounting)
    BR00090: 'BR00090', // E-commerce up to 200 transactions (simplified accounting)
    BR00091: 'BR00091', // E-commerce up to 1,000 transactions (simplified accounting)
    BR00092: 'BR00092', // E-commerce up to 5,000 transactions (simplified accounting)
    BR00093: 'BR00093', // E-commerce up to 20,000 transactions (simplified accounting)

    // Additional e-commerce transactions (simplified accounting)
    BR00166: 'BR00166', // Additional transactions exceeding 200 (simplified)
    BR00167: 'BR00167', // Additional transactions exceeding 1,000 (simplified)
    BR00168: 'BR00168', // Additional transactions exceeding 5,000 (simplified)
    BR00169: 'BR00169', // Additional transactions exceeding 20,000 (simplified)

    // Additional e-commerce transactions (full accounting)
    BR00170: 'BR00170', // Additional transactions exceeding 200 (full)
    BR00171: 'BR00171', // Additional transactions exceeding 1,000 (full)
    BR00172: 'BR00172', // Additional transactions exceeding 5,000 (full)
    BR00173: 'BR00173', // Additional transactions exceeding 20,000 (full)

    // PIT packages for simplified accounting
    BR00094: 'BR00094', // PIT Base package for simplified accounting
    BR00095: 'BR00095', // PIT Silver package for simplified accounting
    BR00096: 'BR00096', // PIT Gold package for simplified accounting
    BR00097: 'BR00097', // PIT Platinum package for simplified accounting

    // Financial statement packages for full accounting
    BR00099: 'BR00099', // Financial statement Base package for full accounting
    BR00100: 'BR00100', // Financial statement Silver package for full accounting
    BR00101: 'BR00101', // Financial statement Gold package for full accounting
    BR00102: 'BR00102'  // Financial statement Platinum package for full accounting
};

/**
 * Validate request parameters
 * @param {Object} params - Request parameters
 * @returns {Object} Validation result with success flag and error message
 */
export function validateRequestParams(params) {
    const { dealId, propertyValue, propertyName } = params;

    if (!dealId) {
        return {
            success: false,
            error: 'dealId parameter is required',
            statusCode: 400
        };
    }

    if (propertyValue === undefined || propertyValue === null) {
        return {
            success: false,
            error: 'propertyValue parameter is required (empty string is allowed)',
            statusCode: 400
        };
    }

    if (!propertyName) {
        return {
            success: false,
            error: 'propertyName parameter is required',
            statusCode: 400
        };
    }

    return { success: true };
}

/**
 * Check if a property is supported for line item changes
 * @param {string} propertyName - Property name to check
 * @returns {Object} Object with property type flags
 */
export function getPropertyTypeFlags(propertyName) {
    return {
        isUpdateTrigger: propertyName === PROPERTY_TYPES.UPDATE_TRIGGER
    };
}

/**
 * Check if any property type is supported
 * @param {Object} flags - Property type flags from getPropertyTypeFlags
 * @returns {boolean} True if any property type is supported
 */
export function isSupportedProperty(flags) {
    return Boolean(flags && flags.isUpdateTrigger);
}

/**
 * Setup console logging capture
 * @returns {Object} Object with console logs array and restore function
 */
export function setupConsoleCapture() {
    const consoleLogs = [];
    const originalConsoleLog = console.log;
    const originalConsoleError = console.error;

    // Override console.log and console.error to capture logs
    console.log = (...args) => {
        const logMessage = args.map(arg =>
            typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
        ).join(' ');
        consoleLogs.push({ type: 'log', message: logMessage, timestamp: new Date().toISOString() });
        originalConsoleLog(...args);
    };

    console.error = (...args) => {
        const errorMessage = args.map(arg =>
            typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
        ).join(' ');
        consoleLogs.push({ type: 'error', message: errorMessage, timestamp: new Date().toISOString() });
        originalConsoleError(...args);
    };

    return {
        consoleLogs,
        restore: () => {
            console.log = originalConsoleLog;
            console.error = originalConsoleError;
        }
    };
}

/**
 * Determine appropriate HTTP status code based on error type
 * @param {Error} error - The error object
 * @returns {number} HTTP status code
 */
export function getErrorStatusCode(error) {
    // If the error has a status property (from SvelteKit error function), use it
    if (error.status) {
        return error.status;
    }

    const message = error.message || '';

    if (message.includes('Bad Request:') || message.includes('parameter is required')) {
        return 400; // Bad Request
    }

    if (message.includes('Unauthorized') || message.includes('Invalid access token')) {
        return 401; // Unauthorized
    }

    if (message.includes('Forbidden') || message.includes('Access denied')) {
        return 403; // Forbidden
    }

    if (message.includes('Not found') || message.includes('Deal not found')) {
        return 404; // Not Found
    }

    if (message.includes('Rate limit') || message.includes('Too many requests')) {
        return 429; // Too Many Requests
    }

    if (error instanceof TypeError && message.includes('Cannot read properties of undefined')) {
        return 400; // Invalid request: Missing required property value or malformed data
    }

    if (error.name === 'SyntaxError' || message.includes('JSON')) {
        return 400; // Invalid request: Malformed JSON in request body
    }

    return 500; // Default to internal server error
}

/**
 * Process VAT status values and calculate BR00032/BR00033 quantities
 * @param {string} vatStatus - VAT status value (semicolon-separated)
 * @returns {Object} VAT processing result with quantities and flags
 */
export function processVatStatus(vatStatus) {
    const result = {
        br00032Quantity: 0,
        br00033Quantity: 0,
        shouldHaveBR00032: false,
        shouldHaveBR00033: false
    };

    // Handle empty or null VAT status
    if (!vatStatus || vatStatus.trim() === '') {
        console.log('VAT status is empty - no VAT line items needed');
        return result;
    }

    // Split propertyValue by semicolon and trim whitespace for VAT status
    const vatValues = vatStatus.split(';').map(value => value.trim()).filter(value => value !== '');
    console.log('VAT property values after split:', vatValues);

    // Count instances for BR00032 - one line item for each instance of VAT EU, VAT 8, or VAT 9M
    let br00032Count = 0;
    vatValues.forEach(value => {
        if (VAT_CONFIG.VALUES_REQUIRING_BR00032.includes(value)) {
            br00032Count++;
        }
    });
    result.br00032Quantity = br00032Count;
    result.shouldHaveBR00032 = br00032Count > 0;

    // Count instances for BR00033 - one line item for each instance of VAT OSS
    // Special case: when VAT OSS and VAT IOSS are together, add two VAT OSS positions
    let br00033Count = 0;
    let hasVatOss = false;
    let hasVatIoss = false;

    vatValues.forEach(value => {
        if (VAT_CONFIG.VALUES_REQUIRING_BR00033.includes(value)) {
            br00033Count++;
            hasVatOss = true;
        }
        if (VAT_CONFIG.VALUES_REQUIRING_DOUBLE_VAT_OSS.includes(value)) {
            hasVatIoss = true;
        }
    });

    // If both VAT OSS and VAT IOSS are present, add one more VAT OSS position
    if (hasVatOss && hasVatIoss) {
        br00033Count++;
        console.log('VAT OSS+IOSS detected - adding extra VAT OSS position');
    }

    result.br00033Quantity = br00033Count;
    result.shouldHaveBR00033 = br00033Count > 0;

    console.log('VAT processing result - BR00032:', result.shouldHaveBR00032, 'quantity:', result.br00032Quantity, 'BR00033:', result.shouldHaveBR00033, 'quantity:', result.br00033Quantity);

    return result;
}

/**
 * Process language property and determine BR00129 requirement
 * @param {string} language - Language property value
 * @returns {Object} Language processing result
 */
export function processLanguageProperty(language) {
    // If language is empty, null, or NOT "Polski", add BR00129
    // Empty language is treated as non-Polish
    const shouldHaveBR00129 = !language || language.trim() === '' || language !== 'Polski';

    console.log('Language processing result - BR00129:', shouldHaveBR00129, 'Language:', language);

    return { shouldHaveBR00129 };
}

/**
 * Package minimums for BR00013 quantity calculations
 */
export const PACKAGE_MINIMUMS = {
    // Full accounting minimums
    FULL: {
        BASE: 5,
        SILVER: 50,
        GOLD: 110,
        PLATINUM: 200
    },
    // Simplified accounting minimums
    SIMPLIFIED: {
        BASE: 5,
        SILVER: 20,
        GOLD: 80,
        PLATINUM: 150
    }
};

/**
 * Document field definitions for accounting calculations
 */
export const DOCUMENT_FIELDS = {
    CASH_BANK_OPERATIONS: [
        'operacje_kp_kw_walutowe',
        'kp_kw___banki_',
        'kp_kw_gotowka'
    ],
    BOOKING_OPERATIONS: [
        'faktury_rachunki_sprzedazowe___ile_',
        'faktury_rachunki_zakupu___ile_',
        'faktury_walutowe___ile_miesiecznie_',
        'dokumenty_wewnetrzne_wdt__wnt_itp'
    ],
    ALL_DOCUMENT_FIELDS: [
        'operacje_kp_kw_walutowe',
        'kp_kw___banki_',
        'kp_kw_gotowka',
        'faktury_rachunki_sprzedazowe___ile_',
        'faktury_rachunki_zakupu___ile_',
        'faktury_walutowe___ile_miesiecznie_',
        'dokumenty_wewnetrzne_wdt__wnt_itp',
        'rodzaj_ksiegowosci'
    ]
};

/**
 * Calculate sum of document fields
 * @param {Object} dealProperties - Deal properties object
 * @param {Array} fieldNames - Array of field names to sum
 * @returns {number} Sum of field values
 */
export function calculateDocumentSum(dealProperties, fieldNames) {
    return fieldNames.reduce((sum, prop) => {
        const rawValue = dealProperties[prop];

        // Handle empty string as 0 (business requirement)
        if (rawValue === '' || rawValue === null || rawValue === undefined) {
            console.log(`${prop}: 0 (empty/null/undefined)`);
            return sum + 0;
        }

        const value = parseInt(rawValue);

        // Validate that the parsed value is a valid number
        if (isNaN(value)) {
            throw new Error(`Invalid numeric value for field '${prop}': ${rawValue}`);
        }

        // Business logic: negative values should be converted to 0
        const finalValue = Math.max(0, value);
        console.log(`${prop}: ${finalValue}`);
        return sum + finalValue;
    }, 0);
}

/**
 * Parse and validate a numeric field from deal properties
 * @param {Object} dealProperties - Deal properties object
 * @param {string} fieldName - Name of the field to parse
 * @returns {number} Validated number (negative values converted to 0)
 */
export function parseAndValidateNumericField(dealProperties, fieldName) {
    const rawValue = dealProperties[fieldName];

    // Handle empty string as 0 (business requirement)
    if (rawValue === '' || rawValue === null || rawValue === undefined) {
        return 0;
    }

    const value = parseInt(rawValue);

    // Validate that the parsed value is a valid number
    if (isNaN(value)) {
        throw new Error(`Invalid numeric value for field '${fieldName}': ${rawValue}`);
    }

    // Business logic: negative values should be converted to 0
    return Math.max(0, value);
}

/**
 * Determine if accounting type is full accounting
 * @param {string} accountingType - Accounting type value
 * @returns {boolean} True if full accounting
 */
export function isFullAccountingType(accountingType) {
    if (!accountingType) return false;

    const lowerType = accountingType.toLowerCase();
    return lowerType.includes('pełna księgowość') ||
           lowerType.includes('fundacje rodzinne') ||
           lowerType.includes('fundacje non profit i ngo');
}

/**
 * Calculate base document quantity for package selection - now uses only bookings sum
 * @param {number} cashBankSum - Sum of cash-bank operations (not used for package selection)
 * @param {number} bookingsSum - Sum of booking operations
 * @param {boolean} isFullAccounting - Whether it's full accounting (not used for package selection)
 * @returns {number} Base document quantity for package selection
 */
export function calculateBaseDocumentQuantity(cashBankSum, bookingsSum, isFullAccounting) {
    // Package calculation now depends only on bookings sum regardless of accounting type
    return bookingsSum;
}

/**
 * Calculate BR00013 document quantity - supports both old and new signatures
 *
 * New signature: calculateBR00013DocumentQuantity(dealProperties)
 * - Uses the greater of two specific sums from deal properties
 * - Sum A: invoices + employees (faktury_rachunki_sprzedazowe___ile_ + faktury_rachunki_zakupu___ile_ + umowa_o_prace___liczba_osob + umowy_cywilnoprawne___liczba_pracownikow)
 * - Sum B: cash-bank operations (kp_kw___banki_ + kp_kw_gotowka + operacje_kp_kw_walutowe)
 *
 * Old signature: calculateBR00013DocumentQuantity(cashBankSum, bookingsSum, isFullAccounting)
 * - Returns the maximum of cashBankSum and bookingsSum (for backward compatibility)
 *
 * @param {Object|number} dealPropertiesOrCashBankSum - Deal properties object OR cash-bank sum for old signature
 * @param {number} [bookingsSum] - Bookings sum (only for old signature)
 * @param {boolean} [isFullAccounting] - Whether it's full accounting (only for old signature)
 * @returns {Object|number} Object with detailed breakdown (new signature) OR simple number (old signature)
 */
export function calculateBR00013DocumentQuantity(dealPropertiesOrCashBankSum, bookingsSum, isFullAccounting) {
    // Check if this is the old signature (three parameters with first being a number)
    if (typeof dealPropertiesOrCashBankSum === 'number' && typeof bookingsSum === 'number') {
        // Old signature: return simple max calculation
        return Math.max(dealPropertiesOrCashBankSum, bookingsSum);
    }

    // New signature: detailed calculation from deal properties
    const dealProperties = dealPropertiesOrCashBankSum;

    // Sum A: booking operations + employees
    const faktury_sprzedazowe = parseAndValidateNumericField(dealProperties, 'faktury_rachunki_sprzedazowe___ile_');
    const faktury_zakupu = parseAndValidateNumericField(dealProperties, 'faktury_rachunki_zakupu___ile_');
    const faktury_walutowe = parseAndValidateNumericField(dealProperties, 'faktury_walutowe___ile_miesiecznie_');
    const dokumenty_wewnetrzne = parseAndValidateNumericField(dealProperties, 'dokumenty_wewnetrzne_wdt__wnt_itp');
    const umowa_o_prace = parseAndValidateNumericField(dealProperties, 'umowa_o_prace___liczba_osob');
    const umowy_cywilnoprawne = parseAndValidateNumericField(dealProperties, 'umowy_cywilnoprawne___liczba_pracownikow');

    const sumA = faktury_sprzedazowe + faktury_zakupu + faktury_walutowe + dokumenty_wewnetrzne + umowa_o_prace + umowy_cywilnoprawne;

    // Sum B: cash-bank operations
    const kp_kw_banki = parseAndValidateNumericField(dealProperties, 'kp_kw___banki_');
    const kp_kw_gotowka = parseAndValidateNumericField(dealProperties, 'kp_kw_gotowka');
    const operacje_walutowe = parseAndValidateNumericField(dealProperties, 'operacje_kp_kw_walutowe');

    const sumB = kp_kw_banki + kp_kw_gotowka + operacje_walutowe;

    // Choose the greater sum
    const chosenSum = sumA >= sumB ? 'A' : 'B';
    const quantity = Math.max(sumA, sumB);

    console.log(`BR00013 calculation: Sum A (booking operations+employees) = ${sumA} (${faktury_sprzedazowe}+${faktury_zakupu}+${faktury_walutowe}+${dokumenty_wewnetrzne}+${umowa_o_prace}+${umowy_cywilnoprawne})`);
    console.log(`BR00013 calculation: Sum B (cash-bank) = ${sumB} (${kp_kw_banki}+${kp_kw_gotowka}+${operacje_walutowe})`);
    console.log(`BR00013 calculation: Chosen sum ${chosenSum} with quantity ${quantity}`);

    return {
        sumA,
        sumB,
        chosenSum,
        quantity,
        breakdown: {
            sumA: {
                faktury_sprzedazowe,
                faktury_zakupu,
                umowa_o_prace,
                umowy_cywilnoprawne
            },
            sumB: {
                kp_kw_banki,
                kp_kw_gotowka,
                operacje_walutowe
            }
        }
    };
}

/**
 * Package capacity definitions (base documents + max extra)
 */
export const PACKAGE_CAPACITIES = {
    BASE: { base: 5, maxExtra: 20, totalCapacity: 25 },
    SILVER: { base: 50, maxExtra: 60, totalCapacity: 110 },
    GOLD: { base: 110, maxExtra: 88, totalCapacity: 198 },
    PLATINUM: { base: 200, maxExtra: Infinity, totalCapacity: Infinity }
};

/**
 * Calculate BR00013 quantity based on document count and selected package
 * This function should be used with the actual package selection result to ensure consistency
 * @param {number} documentCount - Base document count
 * @param {string} selectedPackageName - The package name selected by the optimization logic (e.g., 'BASE', 'SILVER')
 * @param {string} context - Context for logging (optional)
 * @param {boolean} isFullAccounting - Whether it's full accounting or simplified (default: true)
 * @returns {number} BR00013 quantity
 * @throws {Error} If the package name is unknown
 */
export function calculateBR00013QuantityForPackage(documentCount, selectedPackageName, context = '', isFullAccounting = true) {
    if (documentCount <= 0) {
        return 0;
    }

    const accountingType = isFullAccounting ? 'FULL' : 'SIMPLIFIED';
    const packageMinimums = PACKAGE_MINIMUMS[accountingType];

    if (!packageMinimums[selectedPackageName]) {
        throw new Error(`Unknown package name: ${selectedPackageName}. Valid packages are: ${Object.keys(packageMinimums).join(', ')}`);
    }

    const packageMinimum = packageMinimums[selectedPackageName];
    const br00013Quantity = Math.max(documentCount, packageMinimum);

    console.log(`BR00013 quantity${context ? ' ' + context : ''}: ${documentCount} documents with ${selectedPackageName} package (${accountingType}) -> BR00013 = ${br00013Quantity} (min: ${packageMinimum})`);

    return br00013Quantity;
}



/**
 * Payroll package constants and configurations
 */
export const PAYROLL_CONFIG = {
    PACKAGES: {
        PREMIUM: 'Kadry i płace PREMIUM',
        PAYROLL: 'Płace',
        PAYROLL_WITH_FILES: 'Kadry i płace - z teczkami',
        RYCZALT: 'Ryczałt'
    },
    REQUIRED_PROPERTIES: [
        'pakiet_kadrowo_placowy',
        'umowa_o_prace___liczba_osob',
        'umowy_cywilnoprawne___liczba_pracownikow',
        'dodatkowe_skladniki_wynagrodzenia',
        'ppk___ile_osob_',
        'kto_robi_import_do_moje_ppk',
        'pfron___ile_osob_',
        'a1___czy_wystepuja_',
        'zatrudnienie_cudzoziemcow___czy_wystepuje_',
        'vat___status_podatnika',
        'jezyk_obslugi'
    ]
};

/**
 * Clear all payroll quantities in a result object
 * @param {Object} result - Result object to clear
 */
export function clearAllPayrollQuantities(result) {
    result.br00069Quantity = 0;
    result.br00070Quantity = 0;
    result.br00071Quantity = 0;
    result.br00072Quantity = 0;
    result.br00073Quantity = 0;
    result.br00074Quantity = 0;
    result.br00075Quantity = 0;
    result.br00077Quantity = 0;
    result.br00080Quantity = 0;
    result.br00165Quantity = 0;
    result.shouldHaveBR00078 = false;
    result.shouldHaveBR00079 = false;

    // Clear new KADRY-related quantities
    result.br00114Quantity = 0;
    result.shouldHaveBR00115 = false;
    result.shouldHaveBR00117 = false;
    result.shouldHaveBR00118 = false;
    result.shouldHaveBR00119 = false;
    result.shouldHaveBR00081 = false;
}

/**
 * Process payroll package logic and set appropriate quantities
 * @param {Object} result - Result object to modify
 * @param {Object} dealProperties - Deal properties object
 * @param {string} dealId - Deal ID for logging purposes (optional)
 * @param {Object} payrollPrices - Dynamic prices from HubSpot for payroll items
 * @returns {Promise<Object>} Modified result object
 */
export async function processPayrollPackage(result, dealProperties, dealId = 'unknown', payrollPrices = null) {
    const pakietKadrowoPlacowy = dealProperties['pakiet_kadrowo_placowy'];

    // Validate required payroll package field
    if (pakietKadrowoPlacowy === null || pakietKadrowoPlacowy === undefined) {
        throw new Error('Missing required field: pakiet_kadrowo_placowy');
    }

    // Prices should always be provided by the caller - no direct environment access
    if (!payrollPrices) {
        throw new Error('Payroll prices must be provided by the caller - cannot fetch prices directly from validation utils');
    }
    const prices = payrollPrices;

    // Parse and validate numeric fields
    const umowaOPraceOsob = parseAndValidateNumericField(dealProperties, 'umowa_o_prace___liczba_osob');
    const umowyCywilnoprawneOsob = parseAndValidateNumericField(dealProperties, 'umowy_cywilnoprawne___liczba_pracownikow');
    const ppkIleOsob = parseAndValidateNumericField(dealProperties, 'ppk___ile_osob_');
    const pfronIleOsob = parseAndValidateNumericField(dealProperties, 'pfron___ile_osob_');
    const a1CzyWystepuja = parseAndValidateNumericField(dealProperties, 'a1___czy_wystepuja_');

    // Parse foreign workers field
    const zatrudnienieCudzoziemcow = dealProperties['zatrudnienie_cudzoziemcow___czy_wystepuje_'] || '';

    console.log('=== PROCESS PAYROLL PACKAGE ===');
    console.log('Processing payroll package:', pakietKadrowoPlacowy);
    console.log('Raw field values from dealProperties:');
    console.log('  umowa_o_prace___liczba_osob:', dealProperties['umowa_o_prace___liczba_osob']);
    console.log('  umowy_cywilnoprawne___liczba_pracownikow:', dealProperties['umowy_cywilnoprawne___liczba_pracownikow']);
    console.log('Parsed employee counts:', { umowaOPraceOsob, umowyCywilnoprawneOsob, ppkIleOsob, pfronIleOsob, a1CzyWystepuja });
    console.log('Foreign workers field:', zatrudnienieCudzoziemcow);

    // Clear all quantities first
    clearAllPayrollQuantities(result);

    // Handle payroll package logic
    switch (pakietKadrowoPlacowy) {
        case PAYROLL_CONFIG.PACKAGES.PREMIUM:
            // For PREMIUM: differentiate umowa o pracę (BR00070) and umowy cywilnoprawne (BR00071)
            result.br00070Quantity = umowaOPraceOsob;
            result.br00071Quantity = umowyCywilnoprawneOsob;
            console.log('PREMIUM package - BR00070 quantity:', result.br00070Quantity, 'BR00071 quantity:', result.br00071Quantity);
            break;

        case PAYROLL_CONFIG.PACKAGES.PAYROLL:
            // For Płace: use separate SKUs for different employee types
            result.br00072Quantity = umowaOPraceOsob;
            result.br00073Quantity = umowyCywilnoprawneOsob;
            // BR00075 quantity equals the value in 'dodatkowe_skladniki_wynagrodzenia' field
            const dodatkoweSkladnikiPayroll = parseAndValidateNumericField(dealProperties, 'dodatkowe_skladniki_wynagrodzenia');
            result.br00075Quantity = dodatkoweSkladnikiPayroll;
            console.log('Płace package - BR00072 quantity:', result.br00072Quantity, 'BR00073 quantity:', result.br00073Quantity, 'BR00075 quantity:', result.br00075Quantity);
            break;

        case PAYROLL_CONFIG.PACKAGES.PAYROLL_WITH_FILES:
            // For Kadry i płace - z teczkami: behaves like Płace but BR00074 quantity equals sum minus mobility packages
            result.br00072Quantity = umowaOPraceOsob;
            result.br00073Quantity = umowyCywilnoprawneOsob;

            // Get mobility package quantities to subtract from BR00074
            const payrollFilesMobilityPackages = parseAndValidateNumericField(dealProperties, 'ile_jest_pakietow_mobilnosci_');
            const payrollFilesUcpMobilityPackages = parseAndValidateNumericField(dealProperties, 'ucp___ile_jest_pakietow_mobilnosci_');
            const payrollFilesTotalMobilityPackages = payrollFilesMobilityPackages + payrollFilesUcpMobilityPackages;

            // BR00074 = sum of employees minus mobility packages (minimum 0)
            result.br00074Quantity = Math.max(0, (umowaOPraceOsob + umowyCywilnoprawneOsob) - payrollFilesTotalMobilityPackages);

            // BR00075 quantity equals the value in 'dodatkowe_skladniki_wynagrodzenia' field
            const dodatkoweSkladnikiPayrollWithFiles = parseAndValidateNumericField(dealProperties, 'dodatkowe_skladniki_wynagrodzenia');
            result.br00075Quantity = dodatkoweSkladnikiPayrollWithFiles;
            console.log('Payroll with files package - BR00072:', result.br00072Quantity, 'BR00073:', result.br00073Quantity, 'BR00074:', result.br00074Quantity, '(after subtracting', payrollFilesTotalMobilityPackages, 'mobility packages)', 'BR00075:', result.br00075Quantity);
            break;

        case PAYROLL_CONFIG.PACKAGES.RYCZALT:
            // For Ryczałt: use quantity 1 and override the default value instead of creating multiple quantities
            // Calculate the total cost that would be charged for individual items using mobility-adjusted counts and dynamic prices
            const adjustedUmowaOPraceForRyczalt = result.adjustedUmowaOPrace !== undefined ? result.adjustedUmowaOPrace : umowaOPraceOsob;
            const adjustedUmowyCywilnoprawneForRyczalt = result.adjustedUmowyCywilnoprawne !== undefined ? result.adjustedUmowyCywilnoprawne : umowyCywilnoprawneOsob;
            const br00070Cost = adjustedUmowaOPraceForRyczalt * prices.BR00070; // BR00070 price from HubSpot
            const br00071Cost = adjustedUmowyCywilnoprawneForRyczalt * prices.BR00071; // BR00071 price from HubSpot
            const pfronCost = pfronIleOsob * prices.BR00080; // BR00080 price from HubSpot
            const aiCost = a1CzyWystepuja * prices.BR00165; // BR00165 price from HubSpot

            // Add additional SKUs for Ryczałt package: BR00076 only (PPK items excluded like in PREMIUM)
            const ryczaltMobilityPackages = parseAndValidateNumericField(dealProperties, 'ile_jest_pakietow_mobilnosci_');
            const ryczaltUcpMobilityPackages = parseAndValidateNumericField(dealProperties, 'ucp___ile_jest_pakietow_mobilnosci_');
            const ryczaltTotalMobilityPackages = ryczaltMobilityPackages + ryczaltUcpMobilityPackages;
            const br00076Cost = ryczaltTotalMobilityPackages * prices.BR00076; // BR00076 price from HubSpot

            // PPK items (BR00077, BR00078, BR00079) are excluded from Ryczałt calculation to be consistent with PREMIUM package
            const br00077Cost = 0; // Excluded like in PREMIUM package
            const br00078Cost = 0; // Excluded like in PREMIUM package
            const br00079Cost = 0; // Excluded like in PREMIUM package

            // Get ktoRobiImport for logging purposes (even though PPK costs are 0)
            const ktoRobiImport = dealProperties['kto_robi_import_do_moje_ppk'] || '';

            const subtotalRyczaltCost = br00070Cost + br00071Cost + pfronCost + aiCost + br00076Cost + br00077Cost + br00078Cost + br00079Cost;

            // Add 10% to the total price
            const totalRyczaltCost = Math.round(subtotalRyczaltCost * 1.1);

            result.br00069Quantity = 1; // Always quantity 1
            result.br00069OverridePrice = totalRyczaltCost; // Override price with calculated total including 10% markup

            // Set quantities to remove these items from line items (they're included in BR00069)
            result.br00076Quantity = 0; // Will be removed from line items
            result.br00077Quantity = 0; // Will be removed from line items
            result.shouldHaveBR00078 = false; // Will be removed from line items
            result.shouldHaveBR00079 = false; // Will be removed from line items

            // Prepare detailed calculation data for logging
            const calculationData = {
                umowaOPraceOsob: adjustedUmowaOPraceForRyczalt,
                umowyCywilnoprawneOsob: adjustedUmowyCywilnoprawneForRyczalt,
                ppkIleOsob,
                pfronIleOsob,
                a1CzyWystepuja,
                ryczaltMobilityPackages,
                ryczaltUcpMobilityPackages,
                ryczaltTotalMobilityPackages,
                ktoRobiImport,
                br00070Cost,
                br00071Cost,
                pfronCost,
                aiCost,
                br00076Cost,
                br00077Cost,
                br00078Cost,
                br00079Cost,
                subtotalRyczaltCost,
                totalRyczaltCost,
                // Include actual prices used for transparency
                prices: {
                    BR00070: prices.BR00070,
                    BR00071: prices.BR00071,
                    BR00080: prices.BR00080,
                    BR00165: prices.BR00165,
                    BR00076: prices.BR00076
                }
            };

            // Import and use detailed logging (dynamic import to avoid circular dependencies)
            try {
                const { logRyczaltCalculation, logRyczaltCalculationSummary } = await import('./ryczalt-logger.js');

                // Log detailed calculation to file
                logRyczaltCalculation(dealId, calculationData, dealProperties);

                // Log summary to console
                logRyczaltCalculationSummary(calculationData);
            } catch (error) {
                console.error('Error with detailed Ryczałt logging:', error);
                // Fallback to basic console logging
                console.log('Ryczałt package - BR00069 quantity: 1, override price:', totalRyczaltCost, 'PLN (includes 10% markup)');
                console.log('Cost breakdown (before 10% markup):', {
                    employment: `${umowaOPraceOsob} × 80 = ${br00070Cost}`,
                    civil: `${umowyCywilnoprawneOsob} × 70 = ${br00071Cost}`,
                    pfron: `${pfronIleOsob} × 50 = ${pfronCost}`,
                    ai: `${a1CzyWystepuja} × 120 = ${aiCost}`,
                    mobility: `${ryczaltTotalMobilityPackages} × 50 = ${br00076Cost}`,
                    ppk: `${ppkIleOsob} × 30 = ${br00077Cost}`,
                    ppkPresence: `${br00078Cost}`,
                    ppkImport: `${br00079Cost}`,
                    subtotal: subtotalRyczaltCost,
                    withMarkup: totalRyczaltCost
                });
            }
            break;

        default:
            // No specific payroll package selected - no Kadry items should be added
            console.log('No specific payroll package selected - clearing all payroll quantities');
            break;
    }

    // Set BR00077 based on PPK persons ONLY if NOT "Kadry i płace PREMIUM" and NOT "Ryczałt"
    if (pakietKadrowoPlacowy !== PAYROLL_CONFIG.PACKAGES.PREMIUM && pakietKadrowoPlacowy !== PAYROLL_CONFIG.PACKAGES.RYCZALT) {
        result.br00077Quantity = ppkIleOsob;
        console.log('BR00077 (PPK) quantity:', result.br00077Quantity, 'Package allows PPK items');
    } else {
        console.log('BR00077 (PPK) not added for package:', pakietKadrowoPlacowy);
    }

    // Set BR00080 based on PFRON persons (unless Ryczałt package)
    if (pakietKadrowoPlacowy !== PAYROLL_CONFIG.PACKAGES.RYCZALT) {
        result.br00080Quantity = pfronIleOsob;
        console.log('BR00080 (PFRON) quantity:', result.br00080Quantity);
    }

    // Set BR00165 based on A1 certificates (unless Ryczałt package which includes it in BR00069)
    if (pakietKadrowoPlacowy !== PAYROLL_CONFIG.PACKAGES.RYCZALT) {
        result.br00165Quantity = a1CzyWystepuja;
        console.log('BR00165 (A1) quantity:', result.br00165Quantity);
    } else {
        console.log('BR00165 (A1) not added for Ryczałt package - included in BR00069');
    }

    // Set BR00078 and BR00079 based on additional components (excluded for PREMIUM and RYCZALT)
    if (pakietKadrowoPlacowy !== PAYROLL_CONFIG.PACKAGES.PREMIUM && pakietKadrowoPlacowy !== PAYROLL_CONFIG.PACKAGES.RYCZALT) {
        const dodatkoweSkladniki = dealProperties['dodatkowe_skladniki_wynagrodzenia'];
        if (dodatkoweSkladniki === null || dodatkoweSkladniki === undefined) {
            throw new Error('Missing required field: dodatkowe_skladniki_wynagrodzenia');
        }
        result.shouldHaveBR00078 = ppkIleOsob > 0;

        const ktoRobiImport = dealProperties['kto_robi_import_do_moje_ppk'];
        if (ktoRobiImport === null || ktoRobiImport === undefined) {
            throw new Error('Missing required field: kto_robi_import_do_moje_ppk');
        }
        result.shouldHaveBR00079 = ktoRobiImport.toLowerCase().includes('biuro');

        console.log('BR00078 (PPK presence):', result.shouldHaveBR00078, 'BR00079 (Biuro import):', result.shouldHaveBR00079);
    } else if (pakietKadrowoPlacowy === PAYROLL_CONFIG.PACKAGES.PREMIUM) {
        console.log('BR00078 and BR00079 not added for PREMIUM package - PPK items excluded');
    } else if (pakietKadrowoPlacowy === PAYROLL_CONFIG.PACKAGES.RYCZALT) {
        console.log('BR00078 and BR00079 not added for Ryczałt package - included in BR00069');
    }

    // Add new KADRY-related SKUs for all payroll packages (when package is not empty)
    if (pakietKadrowoPlacowy && pakietKadrowoPlacowy.trim() !== '') {
        const totalEmployees = umowaOPraceOsob + umowyCywilnoprawneOsob;

        // BR00114 (PIT 11) - quantity equals sum of both employee types
        result.br00114Quantity = totalEmployees;
        console.log('BR00114 (PIT 11) quantity:', result.br00114Quantity);

        // BR00115 (PIT 4) - 1 item when either employee type > 0
        result.shouldHaveBR00115 = (umowaOPraceOsob > 0 || umowyCywilnoprawneOsob > 0);
        console.log('BR00115 (PIT 4) needed:', result.shouldHaveBR00115);

        // BR00117 (IWA) - 1 item when employment contracts >= 10 (only umowa_o_prace)
        result.shouldHaveBR00117 = umowaOPraceOsob >= 10;
        console.log('BR00117 (IWA) needed:', result.shouldHaveBR00117, '(employment contracts only:', umowaOPraceOsob, ')');

        // BR00119 and BR00081 - 1 item each when employment contracts >= 25 (only umowa_o_prace)
        result.shouldHaveBR00119 = umowaOPraceOsob >= 25;
        result.shouldHaveBR00081 = umowaOPraceOsob >= 25;
        console.log('BR00119 needed:', result.shouldHaveBR00119, '(employment contracts only:', umowaOPraceOsob, ')');
        console.log('BR00081 needed:', result.shouldHaveBR00081, '(employment contracts only:', umowaOPraceOsob, ')');

        // BR00118 (IFT) - 1 item when foreign workers field is not empty
        result.shouldHaveBR00118 = zatrudnienieCudzoziemcow.trim() !== '';
        console.log('BR00118 (IFT) needed:', result.shouldHaveBR00118, '(foreign workers field:', zatrudnienieCudzoziemcow, ')');
    }

    return result;
}

/**
 * Get user-friendly error message based on error type
 * @param {Error} error - The error object
 * @returns {string} User-friendly error message
 */
export function getErrorMessage(error) {
    if (error.body && error.body.message) {
        return error.body.message;
    }

    const message = error.message || 'An unexpected error occurred';

    if (error instanceof TypeError && message.includes('Cannot read properties of undefined')) {
        return 'Invalid request: Missing required property value or malformed data';
    }

    if (error.name === 'SyntaxError' || message.includes('JSON')) {
        return 'Invalid request: Malformed JSON in request body';
    }

    return message;
}