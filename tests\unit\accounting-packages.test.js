import { describe, test, expect, beforeEach, vi } from 'vitest';

// Mock dependencies
vi.mock('../../src/lib/price-fetcher.js', () => ({
  getAccountingPackagePrices: vi.fn()
}));

import {
  calculateOptimalAccountingPackages,
  selectOptimalAccountingPackage,
  calculateAccountingPackageCost,
  getAccountingPackageConfig
} from '../../src/lib/accounting-packages.js';

import { getAccountingPackagePrices } from '../../src/lib/price-fetcher.js';

describe('Accounting Packages', () => {
  const mockAccessToken = 'test-token';

  beforeEach(() => {
    vi.clearAllMocks();
    // Mock console methods to avoid noise in tests
    vi.spyOn(console, 'log').mockImplementation(() => {});
    vi.spyOn(console, 'error').mockImplementation(() => {});
  });

  describe('calculateOptimalAccountingPackages - Edge Cases', () => {
    test('should return empty array for zero documents', async () => {
      const result = await calculateOptimalAccountingPackages(0, true, mockAccessToken);

      expect(result).toEqual([]);
      // Should not call pricing API for zero documents
      expect(getAccountingPackagePrices).not.toHaveBeenCalled();
    });

    test('should handle PLATINUM package with no upper limit', async () => {
      const mockPricingData = {
        packages: {
          BASE: { price: 499 },
          SILVER: { price: 899 },
          GOLD: { price: 1599 },
          PLATINUM: { price: 2599 }
        },
        individualPrices: {
          BASE: 12.9,
          SILVER: 12.9,
          GOLD: 12.9,
          PLATINUM: 12.9
        },
        additionalPackages: {
          goldPack50: { price: 500 },
          platinumPack50: { price: 500 },
          platinumPack200: { price: 1800 }
        }
      };

      getAccountingPackagePrices.mockResolvedValue(mockPricingData);

      // Test with very high document count that would exceed PLATINUM minimum
      const result = await calculateOptimalAccountingPackages(500, true, mockAccessToken);

      expect(result).toHaveLength(3); // PLATINUM package + 200-doc pack + 50-doc packs
      expect(result[0].sku).toBe('BR00006'); // Full accounting PLATINUM
      expect(result[1].sku).toBe('BR00029'); // 200-document additional pack for PLATINUM
      expect(result[1].quantity).toBe(1); // One 200-document pack
      expect(result[2].sku).toBe('BR00028'); // 50-document additional packs for PLATINUM
      expect(result[2].quantity).toBe(2); // 500 - 200 (base) - 200 (pack) = 100 documents = 2 x 50-doc packs
    });

    test('should handle case where current package exceeds 95% of next package', async () => {
      const mockPricingData = {
        packages: {
          BASE: { price: 950 }, // Very high BASE price
          SILVER: { price: 1000 }, // Only slightly higher SILVER price
          GOLD: { price: 1599 },
          PLATINUM: { price: 2599 }
        },
        individualPrices: {
          BASE: 12.9,
          SILVER: 12.9,
          GOLD: 12.9,
          PLATINUM: 12.9
        },
        additionalPackages: {
          goldPack50: { price: 500 },
          platinumPack50: { price: 500 },
          platinumPack200: { price: 1800 }
        }
      };

      getAccountingPackagePrices.mockResolvedValue(mockPricingData);

      // BASE price (950) already exceeds 95% of SILVER (1000 * 0.95 = 950)
      // So maxExtra for BASE should be 0
      const result = await calculateOptimalAccountingPackages(8, true, mockAccessToken);

      // Should select a package (the exact package depends on the 95% rule logic)
      expect(result[0].sku).toBe('BR00004');
    });

    test('should handle pricing API errors', async () => {
      getAccountingPackagePrices.mockRejectedValue(new Error('HubSpot API error'));

      await expect(calculateOptimalAccountingPackages(10, true, mockAccessToken))
        .rejects.toThrow('HubSpot API error');
    });

    test('should handle invalid pricing data', async () => {
      const invalidPricingData = {
        packages: {
          BASE: { price: null }, // Invalid price
          SILVER: { price: 899 },
          GOLD: { price: 1599 },
          PLATINUM: { price: 2599 }
        },
        individualPrices: {
          BASE: 12.9,
          SILVER: 12.9,
          GOLD: 12.9,
          PLATINUM: 12.9
        },
        additionalPackages: {
          goldPack50: { price: 500 },
          platinumPack50: { price: 500 },
          platinumPack200: { price: 1800 }
        }
      };

      getAccountingPackagePrices.mockResolvedValue(invalidPricingData);

      await expect(calculateOptimalAccountingPackages(10, true, mockAccessToken))
        .rejects.toThrow('Invalid or missing price for BASE package: null');
    });

    test('should handle zero or negative pricing', async () => {
      const invalidPricingData = {
        packages: {
          BASE: { price: 0 }, // Zero price
          SILVER: { price: -100 }, // Negative price
          GOLD: { price: 1599 },
          PLATINUM: { price: 2599 }
        },
        individualPrices: {
          BASE: 12.9,
          SILVER: 12.9,
          GOLD: 12.9,
          PLATINUM: 12.9
        },
        additionalPackages: {
          goldPack50: { price: 500 },
          platinumPack50: { price: 500 },
          platinumPack200: { price: 1800 }
        }
      };

      getAccountingPackagePrices.mockResolvedValue(invalidPricingData);

      await expect(calculateOptimalAccountingPackages(10, true, mockAccessToken))
        .rejects.toThrow('Invalid or missing price for BASE package: 0');
    });

    test('should handle NaN pricing', async () => {
      const invalidPricingData = {
        packages: {
          BASE: { price: 'invalid' }, // NaN when parsed
          SILVER: { price: 899 },
          GOLD: { price: 1599 },
          PLATINUM: { price: 2599 }
        },
        individualPrices: {
          BASE: 12.9,
          SILVER: 12.9,
          GOLD: 12.9,
          PLATINUM: 12.9
        },
        additionalPackages: {
          goldPack50: { price: 500 },
          platinumPack50: { price: 500 },
          platinumPack200: { price: 1800 }
        }
      };

      getAccountingPackagePrices.mockResolvedValue(invalidPricingData);

      await expect(calculateOptimalAccountingPackages(10, true, mockAccessToken))
        .rejects.toThrow('Invalid or missing price for BASE package: invalid');
    });

    test('should handle simplified accounting packages correctly', async () => {
      const mockPricingData = {
        packages: {
          BASE: { price: 399 },
          SILVER: { price: 699 },
          GOLD: { price: 1299 },
          PLATINUM: { price: 2199 }
        },
        individualPrices: {
          BASE: 10.9,
          SILVER: 10.9,
          GOLD: 10.9,
          PLATINUM: 10.9
        },
        additionalPackages: {
          goldPack50: { price: 400 },
          platinumPack50: { price: 400 },
          platinumPack200: { price: 1600 }
        }
      };

      getAccountingPackagePrices.mockResolvedValue(mockPricingData);

      // Test simplified accounting with 25 documents
      const result = await calculateOptimalAccountingPackages(25, false, mockAccessToken);

      expect(result).toHaveLength(2);
      expect(result[0].sku).toBe('BR00007');
      expect(result[1].sku).toBe('BR00015');
    });

    test('should apply 95% rule correctly for package selection', async () => {
      const mockPricingData = {
        packages: {
          BASE: { price: 500 },
          SILVER: { price: 900 },
          GOLD: { price: 1600 },
          PLATINUM: { price: 2600 }
        },
        individualPrices: {
          BASE: 15,
          SILVER: 15,
          GOLD: 15,
          PLATINUM: 15
        },
        additionalPackages: {
          goldPack50: { price: 500 },
          platinumPack50: { price: 500 },
          platinumPack200: { price: 1800 }
        }
      };

      getAccountingPackagePrices.mockResolvedValue(mockPricingData);

      // Test case where BASE + extras would exceed 95% of SILVER
      // BASE (500) + 25 extra docs (25 * 15 = 375) = 875
      // SILVER 95% threshold = 900 * 0.95 = 855
      // Since 875 > 855, should upgrade to SILVER
      const result = await calculateOptimalAccountingPackages(30, true, mockAccessToken);

      expect(result[0]).toHaveProperty('sku'); // Should select appropriate package
    });

    test('should handle edge case at exactly 95% threshold', async () => {
      const mockPricingData = {
        packages: {
          BASE: { price: 500 },
          SILVER: { price: 900 },
          GOLD: { price: 1600 },
          PLATINUM: { price: 2600 }
        },
        individualPrices: {
          BASE: 15,
          SILVER: 15,
          GOLD: 15,
          PLATINUM: 15
        },
        additionalPackages: {
          goldPack50: { price: 500 },
          platinumPack50: { price: 500 },
          platinumPack200: { price: 1800 }
        }
      };

      getAccountingPackagePrices.mockResolvedValue(mockPricingData);

      // Calculate documents that would result in exactly 95% of SILVER
      // SILVER 95% = 900 * 0.95 = 855
      // BASE = 500, so extra cost needed = 355
      // Extra documents = 355 / 15 = 23.67, round down to 23
      // Total documents = 5 + 23 = 28
      const result = await calculateOptimalAccountingPackages(28, true, mockAccessToken);

      // Should select appropriate package based on 95% rule
      expect(result[0].sku).toBe('BR00003');
      expect(result[1].sku).toBe('BR00022');
      expect(result).toHaveLength(2); // Should have package + extras
    });

    test('should handle very small document counts', async () => {
      const mockPricingData = {
        packages: {
          BASE: { price: 500 },
          SILVER: { price: 900 },
          GOLD: { price: 1600 },
          PLATINUM: { price: 2600 }
        },
        individualPrices: {
          BASE: 15,
          SILVER: 15,
          GOLD: 15,
          PLATINUM: 15
        },
        additionalPackages: {
          goldPack50: { price: 500 },
          platinumPack50: { price: 500 },
          platinumPack200: { price: 1800 }
        }
      };

      getAccountingPackagePrices.mockResolvedValue(mockPricingData);

      // Test with 1 document (should select BASE with no extras)
      const result = await calculateOptimalAccountingPackages(1, true, mockAccessToken);

      expect(result).toHaveLength(1);
      expect(result[0].sku).toBe('BR00003'); // BASE
      expect(result[0].quantity).toBe(1);
    });
  });

  describe('calculateAccountingPackageCost', () => {
    test('should calculate cost correctly with extras', async () => {
      const mockPricingData = {
        prices: {
          'BR00003': 500,
          'BR00022': 15
        }
      };

      getAccountingPackagePrices.mockResolvedValue(mockPricingData);

      const cost = await calculateAccountingPackageCost('BASE', 'BR00003', 10, 'BR00022', mockAccessToken);

      expect(cost).toBe(650); // 500 + (10 * 15)
    });

    test('should handle missing pricing data', async () => {
      getAccountingPackagePrices.mockResolvedValue({ prices: {} });

      const cost = await calculateAccountingPackageCost('BASE', 'BR00003', 10, 'BR00022', mockAccessToken);

      expect(cost).toBe(0); // Should default to 0 when prices are missing
    });
  });

  describe('getAccountingPackageConfig', () => {
    test('should throw deprecation error', () => {
      expect(() => getAccountingPackageConfig(true))
        .toThrow('getAccountingPackageConfig is deprecated. Use selectOptimalAccountingPackage with real pricing data instead.');
    });
  });
});
