/**
 * Asana API utilities for managing tasks and subtasks
 */

const ASANA_BASE_URL = 'https://app.asana.com/api/1.0';

/**
 * Get all subtasks of a main task from Asana
 * @param {string} taskGid - The main task GID
 * @param {string} accessToken - Asana access token
 * @returns {Promise<Array>} Array of subtasks
 */
export async function getTaskSubtasks(taskGid, accessToken) {
    try {
        const response = await fetch(
            `${ASANA_BASE_URL}/tasks/${taskGid}/subtasks`,
            {
                headers: {
                    'Authorization': `Bearer ${accessToken}`,
                    'Content-Type': 'application/json'
                }
            }
        );

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Failed to fetch subtasks: ${response.status} ${response.statusText} - ${errorText}`);
        }

        const data = await response.json();
        return data.data || [];
    } catch (error) {
        console.error('Error fetching subtasks:', error);
        throw error;
    }
}

/**
 * Mark a task as completed in Asana
 * @param {string} taskGid - The task GID to complete
 * @param {string} accessToken - Asana access token
 * @returns {Promise<Object>} Updated task object
 */
export async function completeTask(taskGid, accessToken) {
    try {
        const response = await fetch(
            `${ASANA_BASE_URL}/tasks/${taskGid}`,
            {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${accessToken}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    data: {
                        completed: true
                    }
                })
            }
        );

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Failed to complete task ${taskGid}: ${response.status} ${response.statusText} - ${errorText}`);
        }

        const data = await response.json();
        return data.data;
    } catch (error) {
        console.error(`Error completing task ${taskGid}:`, error);
        throw error;
    }
}

/**
 * Get task details from Asana
 * @param {string} taskGid - The task GID
 * @param {string} accessToken - Asana access token
 * @returns {Promise<Object>} Task object with details
 */
export async function getTaskDetails(taskGid, accessToken) {
    try {
        const response = await fetch(
            `${ASANA_BASE_URL}/tasks/${taskGid}`,
            {
                headers: {
                    'Authorization': `Bearer ${accessToken}`,
                    'Content-Type': 'application/json'
                }
            }
        );

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Failed to fetch task details: ${response.status} ${response.statusText} - ${errorText}`);
        }

        const data = await response.json();
        return data.data;
    } catch (error) {
        console.error('Error fetching task details:', error);
        throw error;
    }
}

/**
 * Complete all subtasks of a main task
 * @param {string} mainTaskGid - The main task GID
 * @param {string} accessToken - Asana access token
 * @returns {Promise<Object>} Result object with completion details
 */
export async function completeAllSubtasks(mainTaskGid, accessToken) {
    try {
        console.log(`Starting to complete all subtasks for main task: ${mainTaskGid}`);

        // Get all subtasks
        const subtasks = await getTaskSubtasks(mainTaskGid, accessToken);
        console.log(`Found ${subtasks.length} subtasks`);

        if (subtasks.length === 0) {
            return {
                success: true,
                message: 'No subtasks found to complete',
                mainTaskGid,
                completedSubtasks: [],
                totalSubtasks: 0
            };
        }

        // Complete each subtask
        const completedSubtasks = [];
        const failedSubtasks = [];

        for (const subtask of subtasks) {
            try {
                // Skip if already completed
                if (subtask.completed) {
                    console.log(`Subtask ${subtask.gid} (${subtask.name}) is already completed`);
                    completedSubtasks.push({
                        gid: subtask.gid,
                        name: subtask.name,
                        status: 'already_completed'
                    });
                    continue;
                }

                console.log(`Completing subtask: ${subtask.gid} (${subtask.name})`);
                await completeTask(subtask.gid, accessToken);
                
                completedSubtasks.push({
                    gid: subtask.gid,
                    name: subtask.name,
                    status: 'completed'
                });

                console.log(`Successfully completed subtask: ${subtask.gid}`);
            } catch (error) {
                console.error(`Failed to complete subtask ${subtask.gid}:`, error);
                failedSubtasks.push({
                    gid: subtask.gid,
                    name: subtask.name,
                    error: error.message
                });
            }
        }

        const result = {
            success: failedSubtasks.length === 0,
            message: failedSubtasks.length === 0 
                ? `Successfully completed all ${completedSubtasks.length} subtasks`
                : `Completed ${completedSubtasks.length} subtasks, failed ${failedSubtasks.length}`,
            mainTaskGid,
            totalSubtasks: subtasks.length,
            completedSubtasks,
            failedSubtasks: failedSubtasks.length > 0 ? failedSubtasks : undefined
        };

        console.log('Subtask completion result:', result);
        return result;

    } catch (error) {
        console.error('Error in completeAllSubtasks:', error);
        throw error;
    }
}
