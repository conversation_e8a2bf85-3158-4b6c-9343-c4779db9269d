import { describe, test, expect } from 'vitest';

import {
  createBaseResult,
  createNumericBasedResult,
  createBooleanBasedResult,
  createAccountingResult,
  createMspResult,
  createPayrollResult,
  createFinancialStatementResult,
  createEcommerceResult,
  createResultWithPreservedSettings,
  mergeVatResult,
  mergeLanguageResult
} from '../../src/lib/result-structure-utils.js';

describe('Result Structure Utils', () => {
  describe('createBaseResult', () => {
    test('should create base result object with default properties', () => {
      const result = createBaseResult();

      expect(result).toEqual({
        shouldHaveBR00032: false,
        shouldHaveBR00033: false,
        shouldHaveBR00129: false,
        br00032Quantity: 0,
        br00033Quantity: 0
      });
    });

    test('should create new object instances each time', () => {
      const result1 = createBaseResult();
      const result2 = createBaseResult();

      expect(result1).not.toBe(result2);
      expect(result1).toEqual(result2);
    });
  });

  describe('createNumericBasedResult', () => {
    test('should create numeric-based result with additional properties', () => {
      const result = createNumericBasedResult();

      expect(result).toEqual({
        shouldHaveBR00032: false,
        shouldHaveBR00033: false,
        shouldHaveBR00129: false,
        br00032Quantity: 0,
        br00033Quantity: 0,
        br00030Quantity: 0,
        br00031Quantity: 0,
        br00077Quantity: 0,
        br00080Quantity: 0,
        shouldHaveBR00078: false,
        numericValue: 0
      });
    });

    test('should include all base result properties', () => {
      const result = createNumericBasedResult();
      const baseResult = createBaseResult();

      // Check that all base properties are included
      Object.keys(baseResult).forEach(key => {
        expect(result).toHaveProperty(key);
        expect(result[key]).toBe(baseResult[key]);
      });
    });
  });

  describe('createBooleanBasedResult', () => {
    test('should create boolean-based result with additional properties', () => {
      const result = createBooleanBasedResult();

      expect(result).toEqual({
        shouldHaveBR00032: false,
        shouldHaveBR00033: false,
        shouldHaveBR00129: false,
        br00032Quantity: 0,
        br00033Quantity: 0,
        br00165Quantity: 0
      });
    });

    test('should include all base result properties', () => {
      const result = createBooleanBasedResult();
      const baseResult = createBaseResult();

      Object.keys(baseResult).forEach(key => {
        expect(result).toHaveProperty(key);
        expect(result[key]).toBe(baseResult[key]);
      });
    });
  });

  describe('createAccountingResult', () => {
    test('should create accounting result with comprehensive properties', () => {
      const result = createAccountingResult();

      expect(result).toHaveProperty('shouldHaveBR00032', false);
      expect(result).toHaveProperty('shouldHaveBR00033', false);
      expect(result).toHaveProperty('shouldHaveBR00129', false);
      expect(result).toHaveProperty('br00032Quantity', 0);
      expect(result).toHaveProperty('br00033Quantity', 0);
      expect(result).toHaveProperty('br00013Quantity', 0);
      expect(result).toHaveProperty('br00013BankStatementQuantity', 0);
      expect(result).toHaveProperty('accountingPackages', null);
      expect(result).toHaveProperty('isFullAccounting', false);
      expect(result).toHaveProperty('selectedPackageName', 'BASE');
      expect(result).toHaveProperty('selectedEcommercePackage', null);
      expect(result).toHaveProperty('ecommercePackageQuantity', 1);
      expect(result).toHaveProperty('additionalTransactions', 0);
      expect(result).toHaveProperty('totalEcommerceCost', 0);
      expect(result).toHaveProperty('additionalTransactionSku', null);
      expect(result).toHaveProperty('additionalTransactionQuantity', 0);
      expect(result).toHaveProperty('ecommerceRefreshed', false);
      expect(result).toHaveProperty('ecommerceTransactionCount', 0);
    });

    test('should include payroll properties', () => {
      const result = createAccountingResult();

      expect(result).toHaveProperty('br00069Quantity', 0);
      expect(result).toHaveProperty('br00070Quantity', 0);
      expect(result).toHaveProperty('br00071Quantity', 0);
      expect(result).toHaveProperty('br00072Quantity', 0);
      expect(result).toHaveProperty('br00073Quantity', 0);
      expect(result).toHaveProperty('br00074Quantity', 0);
      expect(result).toHaveProperty('br00075Quantity', 0);
      expect(result).toHaveProperty('br00077Quantity', 0);
      expect(result).toHaveProperty('br00080Quantity', 0);
      expect(result).toHaveProperty('br00165Quantity', 0);
      expect(result).toHaveProperty('shouldHaveBR00078', false);
      expect(result).toHaveProperty('shouldHaveBR00079', false);
    });

    test('should include KADRY-related properties', () => {
      const result = createAccountingResult();

      expect(result).toHaveProperty('br00114Quantity', 0);
      expect(result).toHaveProperty('shouldHaveBR00115', false);
      expect(result).toHaveProperty('shouldHaveBR00117', false);
      expect(result).toHaveProperty('shouldHaveBR00118', false);
      expect(result).toHaveProperty('shouldHaveBR00119', false);
      expect(result).toHaveProperty('shouldHaveBR00081', false);
    });
  });

  describe('createMspResult', () => {
    test('should create MSP result with specific properties', () => {
      const result = createMspResult();

      expect(result).toEqual({
        shouldHaveBR00032: false,
        shouldHaveBR00033: false,
        shouldHaveBR00129: false,
        br00032Quantity: 0,
        br00033Quantity: 0,
        shouldHaveBR00111: false,
        br00013Quantity: 0
      });
    });
  });

  describe('createPayrollResult', () => {
    test('should create payroll result with comprehensive payroll properties', () => {
      const result = createPayrollResult();

      expect(result).toHaveProperty('shouldHaveBR00032', false);
      expect(result).toHaveProperty('shouldHaveBR00033', false);
      expect(result).toHaveProperty('shouldHaveBR00129', false);
      expect(result).toHaveProperty('br00032Quantity', 0);
      expect(result).toHaveProperty('br00033Quantity', 0);
      expect(result).toHaveProperty('br00069Quantity', 0);
      expect(result).toHaveProperty('br00070Quantity', 0);
      expect(result).toHaveProperty('br00071Quantity', 0);
      expect(result).toHaveProperty('br00072Quantity', 0);
      expect(result).toHaveProperty('br00073Quantity', 0);
      expect(result).toHaveProperty('br00074Quantity', 0);
      expect(result).toHaveProperty('br00075Quantity', 0);
      expect(result).toHaveProperty('br00077Quantity', 0);
      expect(result).toHaveProperty('br00080Quantity', 0);
      expect(result).toHaveProperty('br00165Quantity', 0);
      expect(result).toHaveProperty('shouldHaveBR00078', false);
      expect(result).toHaveProperty('shouldHaveBR00079', false);
      expect(result).toHaveProperty('br00114Quantity', 0);
      expect(result).toHaveProperty('shouldHaveBR00115', false);
      expect(result).toHaveProperty('shouldHaveBR00117', false);
      expect(result).toHaveProperty('shouldHaveBR00118', false);
      expect(result).toHaveProperty('shouldHaveBR00119', false);
      expect(result).toHaveProperty('shouldHaveBR00081', false);
    });
  });

  describe('createFinancialStatementResult', () => {
    test('should create financial statement result with specific properties', () => {
      const result = createFinancialStatementResult();

      expect(result).toEqual({
        shouldHaveBR00032: false,
        shouldHaveBR00033: false,
        shouldHaveBR00129: false,
        br00032Quantity: 0,
        br00033Quantity: 0,
        selectedFinancialStatementPackage: null,
        financialStatementPackageItems: [],
        financialStatementBasePrice: 0,
        shouldHaveFinancialStatementPackage: false
      });
    });
  });

  describe('createEcommerceResult', () => {
    test('should create e-commerce result with specific properties', () => {
      const result = createEcommerceResult();

      expect(result).toEqual({
        shouldHaveBR00032: false,
        shouldHaveBR00033: false,
        shouldHaveBR00129: false,
        br00032Quantity: 0,
        br00033Quantity: 0,
        selectedEcommercePackage: null,
        ecommercePackageQuantity: 1,
        additionalTransactions: 0,
        totalEcommerceCost: 0,
        additionalTransactionSku: null,
        additionalTransactionQuantity: 0
      });
    });
  });

  describe('createResultWithPreservedSettings', () => {
    test('should create result with preserved settings merged', () => {
      const preservedSettings = {
        shouldHaveBR00032: true,
        shouldHaveBR00033: true,
        br00032Quantity: 2,
        br00033Quantity: 1,
        shouldHaveBR00129: true
      };

      const result = createResultWithPreservedSettings(preservedSettings);

      expect(result.shouldHaveBR00032).toBe(true);
      expect(result.shouldHaveBR00033).toBe(true);
      expect(result.br00032Quantity).toBe(2);
      expect(result.br00033Quantity).toBe(1);
      expect(result.shouldHaveBR00129).toBe(true);
    });

    test('should include all base result properties', () => {
      const preservedSettings = {
        shouldHaveBR00032: true
      };

      const result = createResultWithPreservedSettings(preservedSettings);
      const baseResult = createBaseResult();

      // Should have all base properties
      Object.keys(baseResult).forEach(key => {
        expect(result).toHaveProperty(key);
      });

      // Preserved setting should override base
      expect(result.shouldHaveBR00032).toBe(true);
      // Non-preserved settings should remain as base defaults
      expect(result.shouldHaveBR00033).toBe(false);
    });

    test('should handle empty preserved settings', () => {
      const result = createResultWithPreservedSettings({});
      const baseResult = createBaseResult();

      expect(result).toEqual(baseResult);
    });
  });

  describe('mergeVatResult', () => {
    test('should merge VAT result into existing result object', () => {
      const result = createBaseResult();
      const vatResult = {
        shouldHaveBR00032: true,
        shouldHaveBR00033: true,
        br00032Quantity: 2,
        br00033Quantity: 1
      };

      const mergedResult = mergeVatResult(result, vatResult);

      expect(mergedResult).toBe(result); // Should modify in place
      expect(result.shouldHaveBR00032).toBe(true);
      expect(result.shouldHaveBR00033).toBe(true);
      expect(result.br00032Quantity).toBe(2);
      expect(result.br00033Quantity).toBe(1);
    });

    test('should preserve other properties in result object', () => {
      const result = createAccountingResult();
      result.br00013Quantity = 5;
      result.isFullAccounting = true;

      const vatResult = {
        shouldHaveBR00032: true,
        shouldHaveBR00033: false,
        br00032Quantity: 1,
        br00033Quantity: 0
      };

      mergeVatResult(result, vatResult);

      expect(result.br00013Quantity).toBe(5);
      expect(result.isFullAccounting).toBe(true);
      expect(result.shouldHaveBR00032).toBe(true);
      expect(result.shouldHaveBR00033).toBe(false);
    });
  });

  describe('mergeLanguageResult', () => {
    test('should merge language result into existing result object', () => {
      const result = createBaseResult();
      const languageResult = {
        shouldHaveBR00129: true
      };

      const mergedResult = mergeLanguageResult(result, languageResult);

      expect(mergedResult).toBe(result); // Should modify in place
      expect(result.shouldHaveBR00129).toBe(true);
    });

    test('should preserve other properties in result object', () => {
      const result = createAccountingResult();
      result.br00013Quantity = 10;
      result.shouldHaveBR00032 = true;

      const languageResult = {
        shouldHaveBR00129: true
      };

      mergeLanguageResult(result, languageResult);

      expect(result.br00013Quantity).toBe(10);
      expect(result.shouldHaveBR00032).toBe(true);
      expect(result.shouldHaveBR00129).toBe(true);
    });

    test('should handle false language result', () => {
      const result = createBaseResult();
      result.shouldHaveBR00129 = true; // Set to true initially

      const languageResult = {
        shouldHaveBR00129: false
      };

      mergeLanguageResult(result, languageResult);

      expect(result.shouldHaveBR00129).toBe(false);
    });
  });
});
