import { describe, test, expect, beforeEach, vi } from 'vitest';

import {
  validateRequestParams,
  getPropertyTypeFlags,
  isSupportedProperty,
  setupConsoleCapture,
  getErrorStatusCode,
  processPayrollPackage,
  PROPERTY_TYPES
} from '../../src/lib/validation-utils.js';

describe('Validation Utils', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Restore real console methods for setupConsoleCapture tests
    if (global.testUtils && global.testUtils.enableConsole) {
      global.testUtils.enableConsole();
    }
  });

  describe('validateRequestParams', () => {
    test('should return success for valid parameters', () => {
      const params = {
        dealId: 'deal123',
        propertyValue: 'test-value',
        propertyName: 'test-property'
      };

      const result = validateRequestParams(params);

      expect(result).toEqual({ success: true });
    });

    test('should return error when dealId is missing', () => {
      const params = {
        propertyValue: 'test-value',
        propertyName: 'test-property'
      };

      const result = validateRequestParams(params);

      expect(result).toEqual({
        success: false,
        error: 'dealId parameter is required',
        statusCode: 400
      });
    });

    test('should return error when dealId is empty string', () => {
      const params = {
        dealId: '',
        propertyValue: 'test-value',
        propertyName: 'test-property'
      };

      const result = validateRequestParams(params);

      expect(result).toEqual({
        success: false,
        error: 'dealId parameter is required',
        statusCode: 400
      });
    });

    test('should return error when propertyValue is undefined', () => {
      const params = {
        dealId: 'deal123',
        propertyValue: undefined,
        propertyName: 'test-property'
      };

      const result = validateRequestParams(params);

      expect(result).toEqual({
        success: false,
        error: 'propertyValue parameter is required (empty string is allowed)',
        statusCode: 400
      });
    });

    test('should return error when propertyValue is null', () => {
      const params = {
        dealId: 'deal123',
        propertyValue: null,
        propertyName: 'test-property'
      };

      const result = validateRequestParams(params);

      expect(result).toEqual({
        success: false,
        error: 'propertyValue parameter is required (empty string is allowed)',
        statusCode: 400
      });
    });

    test('should allow empty string as propertyValue', () => {
      const params = {
        dealId: 'deal123',
        propertyValue: '',
        propertyName: 'test-property'
      };

      const result = validateRequestParams(params);

      expect(result).toEqual({ success: true });
    });

    test('should return error when propertyName is missing', () => {
      const params = {
        dealId: 'deal123',
        propertyValue: 'test-value'
      };

      const result = validateRequestParams(params);

      expect(result).toEqual({
        success: false,
        error: 'propertyName parameter is required',
        statusCode: 400
      });
    });

    test('should return error when propertyName is empty string', () => {
      const params = {
        dealId: 'deal123',
        propertyValue: 'test-value',
        propertyName: ''
      };

      const result = validateRequestParams(params);

      expect(result).toEqual({
        success: false,
        error: 'propertyName parameter is required',
        statusCode: 400
      });
    });
  });

  describe('getPropertyTypeFlags', () => {
    test('should return true for update trigger property', () => {
      const result = getPropertyTypeFlags(PROPERTY_TYPES.UPDATE_TRIGGER);

      expect(result).toEqual({
        isUpdateTrigger: true
      });
    });

    test('should return false for non-update trigger property', () => {
      const result = getPropertyTypeFlags('some_other_property');

      expect(result).toEqual({
        isUpdateTrigger: false
      });
    });

    test('should handle empty string property name', () => {
      const result = getPropertyTypeFlags('');

      expect(result).toEqual({
        isUpdateTrigger: false
      });
    });

    test('should handle null property name', () => {
      const result = getPropertyTypeFlags(null);

      expect(result).toEqual({
        isUpdateTrigger: false
      });
    });
  });

  describe('isSupportedProperty', () => {
    test('should return true when isUpdateTrigger is true', () => {
      const flags = { isUpdateTrigger: true };
      const result = isSupportedProperty(flags);

      expect(result).toBe(true);
    });

    test('should return false when isUpdateTrigger is false', () => {
      const flags = { isUpdateTrigger: false };
      const result = isSupportedProperty(flags);

      expect(result).toBe(false);
    });

    test('should handle missing flags', () => {
      const flags = {};
      const result = isSupportedProperty(flags);

      expect(result).toBe(false);
    });
  });

  describe('setupConsoleCapture', () => {
    test('should capture console.log messages', () => {
      const { consoleLogs, restore } = setupConsoleCapture();

      console.log('Test message 1');
      console.log('Test message 2', { key: 'value' });

      expect(consoleLogs).toHaveLength(2);
      expect(consoleLogs[0]).toMatchObject({
        type: 'log',
        message: 'Test message 1'
      });
      expect(consoleLogs[1]).toMatchObject({
        type: 'log',
        message: expect.stringContaining('Test message 2')
      });
      expect(consoleLogs[1].message).toContain('"key": "value"');

      restore();
    });

    test('should capture console.error messages', () => {
      const { consoleLogs, restore } = setupConsoleCapture();

      console.error('Error message 1');
      console.error('Error message 2', new Error('Test error'));

      expect(consoleLogs).toHaveLength(2);
      expect(consoleLogs[0]).toMatchObject({
        type: 'error',
        message: 'Error message 1'
      });
      expect(consoleLogs[1]).toMatchObject({
        type: 'error',
        message: expect.stringContaining('Error message 2')
      });

      restore();
    });

    test('should capture mixed log and error messages', () => {
      const { consoleLogs, restore } = setupConsoleCapture();

      console.log('Log message');
      console.error('Error message');
      console.log('Another log message');

      expect(consoleLogs).toHaveLength(3);
      expect(consoleLogs[0].type).toBe('log');
      expect(consoleLogs[1].type).toBe('error');
      expect(consoleLogs[2].type).toBe('log');

      restore();
    });

    test('should include timestamps in captured logs', () => {
      const { consoleLogs, restore } = setupConsoleCapture();

      console.log('Test message');

      expect(consoleLogs[0]).toHaveProperty('timestamp');
      expect(consoleLogs[0].timestamp).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/);

      restore();
    });

    test('should restore original console functions', () => {
      const originalLog = console.log;
      const originalError = console.error;

      const { restore } = setupConsoleCapture();

      // Console functions should be different now
      expect(console.log).not.toBe(originalLog);
      expect(console.error).not.toBe(originalError);

      restore();

      // Console functions should be restored
      expect(console.log).toBe(originalLog);
      expect(console.error).toBe(originalError);
    });

    test('should handle object serialization in logs', () => {
      const { consoleLogs, restore } = setupConsoleCapture();

      const testObject = { nested: { value: 123 }, array: [1, 2, 3] };
      console.log('Object test:', testObject);

      expect(consoleLogs[0].message).toContain('"nested"');
      expect(consoleLogs[0].message).toContain('"value": 123');
      expect(consoleLogs[0].message).toContain('"array"');

      restore();
    });
  });

  describe('getErrorStatusCode', () => {
    test('should return status property when available', () => {
      const error = { status: 422, message: 'Validation failed' };
      const result = getErrorStatusCode(error);

      expect(result).toBe(422);
    });

    test('should return 400 for bad request messages', () => {
      const testCases = [
        { message: 'Bad Request: Invalid data' },
        { message: 'dealId parameter is required' },
        { message: 'propertyValue parameter is required' }
      ];

      testCases.forEach(error => {
        expect(getErrorStatusCode(error)).toBe(400);
      });
    });

    test('should return 401 for unauthorized messages', () => {
      const testCases = [
        { message: 'Unauthorized access' },
        { message: 'Invalid access token provided' }
      ];

      testCases.forEach(error => {
        expect(getErrorStatusCode(error)).toBe(401);
      });
    });

    test('should return 403 for forbidden messages', () => {
      const testCases = [
        { message: 'Forbidden operation' },
        { message: 'Access denied to resource' }
      ];

      testCases.forEach(error => {
        expect(getErrorStatusCode(error)).toBe(403);
      });
    });

    test('should return 404 for not found messages', () => {
      const testCases = [
        { message: 'Not found in database' },
        { message: 'Deal not found with ID 123' }
      ];

      testCases.forEach(error => {
        expect(getErrorStatusCode(error)).toBe(404);
      });
    });

    test('should return 429 for rate limit messages', () => {
      const testCases = [
        { message: 'Rate limit exceeded' },
        { message: 'Too many requests per minute' }
      ];

      testCases.forEach(error => {
        expect(getErrorStatusCode(error)).toBe(429);
      });
    });

    test('should return 400 for TypeError with undefined properties', () => {
      const error = new TypeError('Cannot read properties of undefined (reading "someProperty")');
      const result = getErrorStatusCode(error);

      expect(result).toBe(400);
    });

    test('should return 400 for SyntaxError', () => {
      const error = new SyntaxError('Unexpected token in JSON at position 0');
      const result = getErrorStatusCode(error);

      expect(result).toBe(400);
    });

    test('should return 400 for JSON-related errors', () => {
      const error = { message: 'Invalid JSON format in request body' };
      const result = getErrorStatusCode(error);

      expect(result).toBe(400);
    });

    test('should return 500 for unknown errors', () => {
      const testCases = [
        { message: 'Unknown database error' },
        { message: 'Internal processing failed' },
        {},
        { message: '' }
      ];

      testCases.forEach(error => {
        expect(getErrorStatusCode(error)).toBe(500);
      });
    });

    test('should handle errors without message property', () => {
      const error = { code: 'UNKNOWN_ERROR' };
      const result = getErrorStatusCode(error);

      expect(result).toBe(500);
    });
  });

  // ============================================================================
  // KADRY LOGIC TESTS (merged from kadry-logic.test.js)
  // ============================================================================

  describe('KADRY Logic Unit Tests', () => {
    // Mock prices for testing payroll functions
    const mockPayrollPrices = {
      BR00070: 80,  // Employment contract price
      BR00071: 70,  // Civil contract price
      BR00080: 50,  // PFRON price
      BR00165: 120, // A1 price
      BR00076: 50   // Mobility package price
    };

    // Mock result structure for payroll tests
    function createMockPayrollResult() {
      return {
        br00069Quantity: 0,
        br00070Quantity: 0,
        br00071Quantity: 0,
        br00072Quantity: 0,
        br00073Quantity: 0,
        br00074Quantity: 0,
        br00075Quantity: 0,
        br00077Quantity: 0,
        br00080Quantity: 0,
        br00165Quantity: 0,
        shouldHaveBR00078: false,
        shouldHaveBR00079: false,
        shouldHaveBR00117: false,
        shouldHaveBR00118: false,
        shouldHaveBR00119: false,
        shouldHaveBR00081: false
      };
    }

    // Mock deal properties for payroll tests
    function createMockPayrollDealProperties(overrides = {}) {
      return {
        'pakiet_kadrowo_placowy': 'Kadry i płace PREMIUM',
        'umowa_o_prace___liczba_osob': '0',
        'umowy_cywilnoprawne___liczba_pracownikow': '0',
        'ppk___ile_osob_': '0',
        'pfron___ile_osob_': '0',
        'a1___czy_wystepuja_': '0',
        'zatrudnienie_cudzoziemcow___czy_wystepuje_': '',
        'dodatkowe_skladniki_wynagrodzenia': '0',
        'kto_robi_import_do_moje_ppk': 'Klient',
        ...overrides
      };
    }

    describe('BR00114 (PIT 11) Logic', () => {
      test('should set BR00114 quantity to sum of both employee types', async () => {
        const dealProperties = createMockPayrollDealProperties({
          'umowa_o_prace___liczba_osob': '5',
          'umowy_cywilnoprawne___liczba_pracownikow': '3'
        });

        const result = createMockPayrollResult();
        await processPayrollPackage(result, dealProperties, 'test-deal', mockPayrollPrices);

        expect(result.br00114Quantity).toBe(8); // 5 + 3
      });

      test('should work with zero employees', async () => {
        const dealProperties = createMockPayrollDealProperties({
          'umowa_o_prace___liczba_osob': '0',
          'umowy_cywilnoprawne___liczba_pracownikow': '0'
        });

        const result = createMockPayrollResult();
        await processPayrollPackage(result, dealProperties, 'test-deal', mockPayrollPrices);

        expect(result.br00114Quantity).toBe(0);
      });
    });
  });
});
