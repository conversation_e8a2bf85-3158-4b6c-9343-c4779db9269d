import { json, error } from '@sveltejs/kit';
import { env } from '$env/dynamic/private';
import { completeAllSubtasks, getTaskDetails } from '$lib/asana-api.js';

/**
 * SvelteKit POST endpoint handler for completing all subtasks of an Asana task
 * Expected payload from Make platform with task data
 * @param {import('@sveltejs/kit').RequestEvent} event - The request event
 * @returns {Promise<Response>} - JSON response with the operation result
 */
export async function POST({ request }) {
    const consoleLogs = [];
    const originalConsoleLog = console.log;
    const originalConsoleError = console.error;
    
    // Capture console logs for response
    console.log = (...args) => {
        const message = args.map(arg => 
            typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
        ).join(' ');
        consoleLogs.push(`[LOG] ${message}`);
        originalConsoleLog(...args);
    };
    
    console.error = (...args) => {
        const message = args.map(arg => 
            typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
        ).join(' ');
        consoleLogs.push(`[ERROR] ${message}`);
        originalConsoleError(...args);
    };

    const restore = () => {
        console.log = originalConsoleLog;
        console.error = originalConsoleError;
    };

    try {
        // Check API key
        const apiKey = request.headers.get('x-api-key');
        const expectedApiKey = env.API_KEY;

        if (!apiKey || apiKey !== expectedApiKey) {
            restore();
            throw error(401, 'Unauthorized: Invalid or missing API key');
        }

        // Get Asana access token from environment
        const asanaAccessToken = env.ASANA_ACCESS_TOKEN;
        if (!asanaAccessToken) {
            restore();
            throw error(500, 'Server Error: Asana access token not configured');
        }

        // Extract request data from JSON body
        let requestData = {};
        try {
            requestData = await request.json();
        } catch (err) {
            restore();
            return json({
                success: false,
                error: 'Invalid JSON in request body',
                consoleLogs: consoleLogs
            }, { status: 400 });
        }

        console.log('Received request data:', requestData);

        // Handle both single task object and array of tasks from Make
        let taskData;
        if (Array.isArray(requestData)) {
            if (requestData.length === 0) {
                restore();
                return json({
                    success: false,
                    error: 'Empty array received',
                    consoleLogs: consoleLogs
                }, { status: 400 });
            }
            taskData = requestData[0]; // Take the first task from the array
        } else {
            taskData = requestData;
        }

        // Validate required fields
        if (!taskData.gid) {
            restore();
            return json({
                success: false,
                error: 'Missing required field: gid (task ID)',
                consoleLogs: consoleLogs
            }, { status: 400 });
        }

        const mainTaskGid = taskData.gid;
        const taskName = taskData.name || 'Unknown Task';
        const workspaceGid = taskData.workspace?.gid;

        console.log(`Processing Asana task completion request:`);
        console.log(`- Main Task GID: ${mainTaskGid}`);
        console.log(`- Task Name: ${taskName}`);
        console.log(`- Workspace GID: ${workspaceGid || 'Not provided'}`);

        // Get task details to verify it exists and get current status
        let mainTaskDetails;
        try {
            mainTaskDetails = await getTaskDetails(mainTaskGid, asanaAccessToken);
            console.log(`Main task details retrieved: ${mainTaskDetails.name} (completed: ${mainTaskDetails.completed})`);
        } catch (taskError) {
            console.error('Failed to retrieve main task details:', taskError);
            restore();
            return json({
                success: false,
                error: `Failed to retrieve task details: ${taskError.message}`,
                consoleLogs: consoleLogs
            }, { status: 404 });
        }

        // Check if the main task is completed before processing subtasks
        if (!mainTaskDetails.completed) {
            console.log(`Skipping subtask completion - main task "${mainTaskDetails.name}" is not completed yet`);
            restore();
            return json({
                success: true,
                message: `Skipped: Main task "${mainTaskDetails.name}" is not completed yet`,
                data: {
                    mainTask: {
                        gid: mainTaskGid,
                        name: mainTaskDetails.name,
                        completed: mainTaskDetails.completed,
                        permalink_url: mainTaskDetails.permalink_url
                    },
                    subtasks: {
                        total: 0,
                        completed: 0,
                        failed: 0
                    },
                    completedSubtasks: [],
                    failedSubtasks: [],
                    skipped: true,
                    skipReason: 'Main task not completed'
                },
                consoleLogs: consoleLogs
            });
        }

        console.log(`Main task is completed - proceeding to complete subtasks`);

        // Complete all subtasks
        const result = await completeAllSubtasks(mainTaskGid, asanaAccessToken);

        // Prepare response
        const response = {
            success: result.success,
            message: result.message,
            data: {
                mainTask: {
                    gid: mainTaskGid,
                    name: mainTaskDetails.name,
                    completed: mainTaskDetails.completed,
                    permalink_url: mainTaskDetails.permalink_url
                },
                subtasks: {
                    total: result.totalSubtasks,
                    completed: result.completedSubtasks.length,
                    failed: result.failedSubtasks?.length || 0
                },
                completedSubtasks: result.completedSubtasks,
                failedSubtasks: result.failedSubtasks
            },
            consoleLogs: consoleLogs
        };

        console.log('Operation completed successfully');
        restore();
        return json(response);

    } catch (err) {
        console.error('Error processing Asana subtask completion:', err);
        restore();

        // Determine appropriate status code based on error type
        let statusCode = 500;
        let errorMessage = err.message || 'An unexpected error occurred';

        if (err.status) {
            statusCode = err.status;
            if (err.body && err.body.message) {
                errorMessage = err.body.message;
            }
        } else if (err.message && err.message.includes('fetch')) {
            statusCode = 503;
            errorMessage = 'Failed to connect to Asana API';
        } else if (err.message && err.message.includes('Unauthorized')) {
            statusCode = 401;
            errorMessage = 'Invalid Asana access token';
        } else if (err.message && err.message.includes('Not Found')) {
            statusCode = 404;
            errorMessage = 'Task not found in Asana';
        }

        return json({
            success: false,
            error: errorMessage,
            consoleLogs: consoleLogs
        }, { status: statusCode });
    }
}
