/**
 * PIT Package Selection Logic
 * Handles selection of appropriate PIT (Personal Income Tax) packages for simplified accounting
 */

import { SKUS } from './validation-utils.js';
import { getProductPrice } from './price-fetcher.js';

/**
 * Get PIT package prices from HubSpot
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<Object>} PIT package pricing configuration
 */
export async function getPitPackagePrices(accessToken) {
    try {
        const pitSkus = [SKUS.BR00094, SKUS.BR00095, SKUS.BR00096, SKUS.BR00097];
        
        console.log('Fetching PIT package prices from HubSpot for SKUs:', pitSkus);
        
        const prices = {};
        for (const sku of pitSkus) {
            const price = await getProductPrice(sku, accessToken);
            prices[sku] = price;
            console.log(`Fetched price for ${sku}: ${price}`);
        }

        return {
            packages: {
                BASE: { sku: SKUS.BR00094, price: prices[SKUS.BR00094] },
                SILVER: { sku: SKUS.BR00095, price: prices[SKUS.BR00095] },
                GOLD: { sku: SKUS.BR00096, price: prices[SKUS.BR00096] },
                PLATINUM: { sku: SKUS.BR00097, price: prices[SKUS.BR00097] }
            }
        };
    } catch (error) {
        console.error('Error fetching PIT package prices:', error);
        throw new Error(`Failed to fetch PIT package prices: ${error.message}`);
    }
}

/**
 * Select appropriate PIT package based on accounting package level
 * @param {string} selectedPackageName - The selected accounting package name (BASE, SILVER, GOLD, PLATINUM)
 * @param {string} accessToken - HubSpot access token for fetching prices
 * @returns {Promise<Array>} Array of PIT package line items
 */
export async function selectPitPackageForSimplifiedAccounting(selectedPackageName, accessToken) {
    console.log('Selecting PIT package for simplified accounting with package:', selectedPackageName);
    
    // Only add PIT packages for simplified accounting
    if (!selectedPackageName) {
        console.log('No accounting package selected - no PIT package needed');
        return [];
    }

    try {
        // Fetch PIT package prices from HubSpot
        const pricingData = await getPitPackagePrices(accessToken);
        console.log('PIT package pricing data:', pricingData);

        // Map accounting package to corresponding PIT package
        const pitPackageMapping = {
            'BASE': pricingData.packages.BASE,
            'SILVER': pricingData.packages.SILVER,
            'GOLD': pricingData.packages.GOLD,
            'PLATINUM': pricingData.packages.PLATINUM
        };

        const selectedPitPackage = pitPackageMapping[selectedPackageName];
        
        if (!selectedPitPackage) {
            console.warn(`No PIT package mapping found for accounting package: ${selectedPackageName}`);
            return [];
        }

        console.log(`Selected PIT package ${selectedPitPackage.sku} for accounting package ${selectedPackageName}`);
        
        return [{
            sku: selectedPitPackage.sku,
            quantity: 1,
            description: `PIT ${selectedPackageName} package for simplified accounting`,
            price: selectedPitPackage.price
        }];

    } catch (error) {
        console.error('Error selecting PIT package:', error);
        throw new Error(`Failed to select PIT package: ${error.message}`);
    }
}
