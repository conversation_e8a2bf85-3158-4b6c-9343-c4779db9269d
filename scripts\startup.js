#!/usr/bin/env node

/**
 * Application startup script
 * 
 * This script handles initialization tasks that need to happen
 * before the main application starts, including browser setup.
 */

import { initializeBrowsers } from '../src/lib/browser-setup.js';

async function startup() {
  console.log('🚀 Starting application initialization...');
  
  try {
    // Initialize browsers
    console.log('📦 Initializing browsers...');
    const browsersReady = await initializeBrowsers();
    
    if (browsersReady) {
      console.log('✅ Browser initialization completed successfully');
    } else {
      console.warn('⚠️  Browser initialization failed, but continuing startup');
      console.warn('   Browser-dependent features may not work properly');
    }
    
    console.log('🎉 Application initialization completed');
    return true;
    
  } catch (error) {
    console.error('❌ Application initialization failed:', error.message);
    console.error('   The application may not work properly');
    return false;
  }
}

// Run startup - this script is designed to be executed directly
startup()
  .then(success => {
    if (success) {
      console.log('✅ Startup completed successfully');
      process.exit(0);
    } else {
      console.error('❌ Startup completed with warnings');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('💥 Startup failed:', error);
    process.exit(1);
  });
