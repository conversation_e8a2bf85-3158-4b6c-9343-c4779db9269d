#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to complete all subtasks and their children for completed main tasks in a project
 */

import { config } from 'dotenv';
import readline from 'readline';

// Load environment variables
config();

const ASANA_BASE_URL = 'https://app.asana.com/api/1.0';
const ASANA_ACCESS_TOKEN = process.env.ASANA_ACCESS_TOKEN;

/**
 * Make API request to Asana
 */
async function makeAsanaRequest(endpoint, method = 'GET', data = null) {
    const url = `${ASANA_BASE_URL}${endpoint}`;
    const options = {
        method,
        headers: {
            'Authorization': `Bearer ${ASANA_ACCESS_TOKEN}`,
            'Content-Type': 'application/json'
        }
    };

    if (data && (method === 'POST' || method === 'PUT')) {
        options.body = JSON.stringify({ data });
    }

    const response = await fetch(url, options);
    
    if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Asana API error: ${response.status} ${response.statusText} - ${errorText}`);
    }

    return await response.json();
}

/**
 * Get all tasks in a project
 */
async function getProjectTasks(projectGid) {
    console.log(`Fetching tasks for project: ${projectGid}`);
    const response = await makeAsanaRequest(`/projects/${projectGid}/tasks?opt_fields=gid,name,completed,subtasks`);
    return response.data;
}

/**
 * Get task details including subtasks
 */
async function getTaskDetails(taskGid) {
    console.log(`Fetching details for task: ${taskGid}`);
    const response = await makeAsanaRequest(`/tasks/${taskGid}?opt_fields=gid,name,completed,subtasks`);
    return response.data;
}

/**
 * Get all subtasks of a task
 */
async function getSubtasks(taskGid) {
    console.log(`Fetching subtasks for task: ${taskGid}`);
    const response = await makeAsanaRequest(`/tasks/${taskGid}/subtasks?opt_fields=gid,name,completed`);
    return response.data;
}

/**
 * Mark task as completed
 */
async function markTaskAsCompleted(taskGid) {
    console.log(`Marking task ${taskGid} as completed`);
    await makeAsanaRequest(`/tasks/${taskGid}`, 'PUT', {
        completed: true
    });
}

/**
 * Complete task and all its subtasks recursively
 */
async function completeTaskAndSubtasksRecursively(taskGid, depth = 0) {
    const indent = '  '.repeat(depth);
    
    try {
        // Get task details
        const task = await getTaskDetails(taskGid);
        
        // Skip if already completed
        if (task.completed) {
            console.log(`${indent}⏭️  Task "${task.name}" is already completed`);
            return { completed: 0, skipped: 1 };
        }

        console.log(`${indent}📝 Processing: "${task.name}"`);
        
        // Get all subtasks
        const subtasks = await getSubtasks(taskGid);
        
        let totalCompleted = 0;
        let totalSkipped = 0;
        
        // First, recursively complete all subtasks
        for (const subtask of subtasks) {
            const result = await completeTaskAndSubtasksRecursively(subtask.gid, depth + 1);
            totalCompleted += result.completed;
            totalSkipped += result.skipped;
        }
        
        // Then complete the main task
        await markTaskAsCompleted(taskGid);
        totalCompleted += 1;
        
        console.log(`${indent}✅ Completed: "${task.name}" (and ${subtasks.length} subtasks)`);
        
        return { completed: totalCompleted, skipped: totalSkipped };
        
    } catch (error) {
        console.error(`${indent}❌ Failed to complete task ${taskGid}: ${error.message}`);
        return { completed: 0, skipped: 0, failed: 1 };
    }
}

/**
 * Get user input
 */
function getUserInput(question) {
    const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
    });

    return new Promise((resolve) => {
        rl.question(question, (answer) => {
            rl.close();
            resolve(answer.trim());
        });
    });
}

/**
 * Main function
 */
async function main() {
    try {
        // Check if Asana token is configured
        if (!ASANA_ACCESS_TOKEN || ASANA_ACCESS_TOKEN === 'your-asana-personal-access-token-here') {
            console.error('❌ ASANA_ACCESS_TOKEN not configured in .env file');
            console.error('Please add your Asana Personal Access Token to the .env file');
            process.exit(1);
        }

        console.log('🚀 Asana Subtask Completer Script');
        console.log('=====================================');
        console.log('This script completes all subtasks for completed main tasks in a project\n');

        // Get user inputs
        const projectGid = await getUserInput('Enter the project ID (GID): ');
        if (!projectGid) {
            console.error('❌ Project ID is required');
            process.exit(1);
        }

        // Ask for test mode
        const testModeChoice = await getUserInput('\n🧪 Test mode? Only process the first completed task (yes/no): ');
        const testMode = testModeChoice.toLowerCase() === 'yes' || testModeChoice.toLowerCase() === 'y';
        if (testMode) {
            console.log('🧪 Test mode enabled - will only process the first completed task');
        }

        // Get all tasks in the project
        console.log('\n🔍 Step 1: Fetching all tasks in the project...');
        const allTasks = await getProjectTasks(projectGid);
        console.log(`✅ Found ${allTasks.length} tasks in the project`);

        // Filter for completed main tasks (tasks that are completed but might have incomplete subtasks)
        console.log('\n🔍 Step 2: Finding completed main tasks...');
        const completedMainTasks = allTasks.filter(task => task.completed);
        console.log(`✅ Found ${completedMainTasks.length} completed main tasks`);

        if (completedMainTasks.length === 0) {
            console.log('ℹ️  No completed main tasks found. Nothing to process.');
            return;
        }

        // Determine which tasks to process
        const tasksToProcess = testMode ? completedMainTasks.slice(0, 1) : completedMainTasks;
        const processingMessage = testMode 
            ? `1 completed task (TEST MODE)` 
            : `${completedMainTasks.length} completed tasks`;

        // List tasks that will be processed
        console.log('\nCompleted main tasks to process:');
        tasksToProcess.forEach((task, index) => {
            console.log(`  ${index + 1}. "${task.name}" (${task.gid})`);
        });

        // Confirm before proceeding
        const confirmation = await getUserInput(`\n⚠️  Are you sure you want to complete all subtasks for ${processingMessage}? (yes/no): `);
        if (confirmation.toLowerCase() !== 'yes' && confirmation.toLowerCase() !== 'y') {
            console.log('❌ Operation cancelled by user');
            return;
        }

        // Process each completed main task
        console.log(`\n🔄 Step 3: Processing completed main tasks... ${testMode ? '(TEST MODE)' : ''}`);
        const results = {
            totalCompleted: 0,
            totalSkipped: 0,
            totalFailed: 0,
            processedTasks: []
        };

        for (let i = 0; i < tasksToProcess.length; i++) {
            const mainTask = tasksToProcess[i];
            console.log(`\n📋 Processing main task ${i + 1}/${tasksToProcess.length}: "${mainTask.name}"`);
            
            try {
                const result = await completeTaskAndSubtasksRecursively(mainTask.gid);
                
                results.totalCompleted += result.completed || 0;
                results.totalSkipped += result.skipped || 0;
                results.totalFailed += result.failed || 0;
                
                results.processedTasks.push({
                    gid: mainTask.gid,
                    name: mainTask.name,
                    completed: result.completed || 0,
                    skipped: result.skipped || 0,
                    failed: result.failed || 0
                });

                console.log(`✅ Finished processing: "${mainTask.name}"`);

            } catch (error) {
                console.error(`❌ Failed to process main task "${mainTask.name}": ${error.message}`);
                results.totalFailed += 1;
                results.processedTasks.push({
                    gid: mainTask.gid,
                    name: mainTask.name,
                    completed: 0,
                    skipped: 0,
                    failed: 1,
                    error: error.message
                });
            }
        }

        // Summary
        console.log('\n📊 SUMMARY');
        console.log('===========');
        if (testMode) {
            console.log('🧪 TEST MODE: Only processed the first completed task');
            console.log(`📊 Total completed tasks available: ${completedMainTasks.length}`);
        }
        console.log(`✅ Tasks completed: ${results.totalCompleted}`);
        console.log(`⏭️  Tasks skipped (already completed): ${results.totalSkipped}`);
        console.log(`❌ Tasks failed: ${results.totalFailed}`);
        console.log(`📋 Main tasks processed: ${results.processedTasks.length}`);

        if (results.processedTasks.length > 0) {
            console.log('\n📋 Processed main tasks:');
            results.processedTasks.forEach(task => {
                const status = task.error ? `❌ ${task.error}` : `✅ ${task.completed} completed, ${task.skipped} skipped`;
                console.log(`   • "${task.name}" (${task.gid}) - ${status}`);
            });
        }

        console.log('\n🎉 Script completed!');

    } catch (error) {
        console.error('\n💥 Script failed:', error.message);
        process.exit(1);
    }
}

// Run the script
main().catch(console.error);
