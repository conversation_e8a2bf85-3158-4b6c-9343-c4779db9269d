/**
 * Accounting package calculation and optimization utilities
 */

import { getAccountingPackagePrices } from './price-fetcher.js';

/**
 * Calculate maximum extra documents for a package based on 95% rule
 * @param {Object} currentPackage - Current package configuration
 * @param {Object} nextPackage - Next package configuration (null for PLATINUM)
 * @returns {number} Maximum extra documents allowed before upgrading
 */
function calculateMaxExtraDocuments(currentPackage, nextPackage) {
    if (!nextPackage) {
        // PLATINUM package has no upper limit
        return Infinity;
    }

    // Calculate how many extra documents we can add before hitting 95% of next package price
    const threshold95Percent = nextPackage.price * 0.95;
    const remainingBudget = threshold95Percent - currentPackage.price;

    if (remainingBudget <= 0) {
        // If current package already exceeds 95% of next package, no extra documents allowed
        return 0;
    }

    // Calculate maximum extra documents within budget
    const maxExtraDocuments = Math.floor(remainingBudget / currentPackage.extraPrice);
    return Math.max(0, maxExtraDocuments);
}

/**
 * Calculate optimal accounting packages based on document quantity and accounting type
 * @param {number} documentCount - Number of documents needed
 * @param {boolean} isFullAccounting - Whether it's full accounting or simplified
 * @param {string} accessToken - HubSpot access token for fetching prices
 * @returns {Promise<Array>} Array of optimal package configurations
 */
export async function calculateOptimalAccountingPackages(documentCount, isFullAccounting, accessToken) {
    console.log(`Calculating packages for ${documentCount} documents, full accounting: ${isFullAccounting}`);

    if (documentCount === 0) {
        return [];
    }

    // Fetch dynamic pricing from HubSpot - will throw error if prices not available
    const pricingData = await getAccountingPackagePrices(isFullAccounting, accessToken);

    // Define base package configurations with dynamic pricing
    const simplifiedPackages = {
        BASE: { sku: 'BR00007', price: pricingData.packages.BASE.price, documents: 5, extraPrice: pricingData.individualPrices.BASE },
        SILVER: { sku: 'BR00008', price: pricingData.packages.SILVER.price, documents: 20, extraPrice: pricingData.individualPrices.SILVER },
        GOLD: { sku: 'BR00009', price: pricingData.packages.GOLD.price, documents: 80, extraPrice: pricingData.individualPrices.GOLD },
        PLATINUM: { sku: 'BR00010', price: pricingData.packages.PLATINUM.price, documents: 150, extraPrice: pricingData.individualPrices.PLATINUM }
    };

    const fullPackages = {
        BASE: { sku: 'BR00003', price: pricingData.packages.BASE.price, documents: 5, extraPrice: pricingData.individualPrices.BASE },
        SILVER: { sku: 'BR00004', price: pricingData.packages.SILVER.price, documents: 50, extraPrice: pricingData.individualPrices.SILVER },
        GOLD: { sku: 'BR00005', price: pricingData.packages.GOLD.price, documents: 110, extraPrice: pricingData.individualPrices.GOLD },
        PLATINUM: { sku: 'BR00006', price: pricingData.packages.PLATINUM.price, documents: 200, extraPrice: pricingData.individualPrices.PLATINUM }
    };

    // Calculate maxExtra for each package based on 95% rule
    simplifiedPackages.BASE.maxExtra = calculateMaxExtraDocuments(simplifiedPackages.BASE, simplifiedPackages.SILVER);
    simplifiedPackages.SILVER.maxExtra = calculateMaxExtraDocuments(simplifiedPackages.SILVER, simplifiedPackages.GOLD);
    simplifiedPackages.GOLD.maxExtra = calculateMaxExtraDocuments(simplifiedPackages.GOLD, simplifiedPackages.PLATINUM);
    simplifiedPackages.PLATINUM.maxExtra = Infinity;

    fullPackages.BASE.maxExtra = calculateMaxExtraDocuments(fullPackages.BASE, fullPackages.SILVER);
    fullPackages.SILVER.maxExtra = calculateMaxExtraDocuments(fullPackages.SILVER, fullPackages.GOLD);
    fullPackages.GOLD.maxExtra = calculateMaxExtraDocuments(fullPackages.GOLD, fullPackages.PLATINUM);
    fullPackages.PLATINUM.maxExtra = Infinity;

    // Log calculated maxExtra values
    console.log('Calculated maxExtra values for simplified accounting:');
    console.log(`- BASE: ${simplifiedPackages.BASE.maxExtra}`);
    console.log(`- SILVER: ${simplifiedPackages.SILVER.maxExtra}`);
    console.log(`- GOLD: ${simplifiedPackages.GOLD.maxExtra}`);

    console.log('Calculated maxExtra values for full accounting:');
    console.log(`- BASE: ${fullPackages.BASE.maxExtra}`);
    console.log(`- SILVER: ${fullPackages.SILVER.maxExtra}`);
    console.log(`- GOLD: ${fullPackages.GOLD.maxExtra}`);

    // Combine into final packages structure
    const packages = {
        simplified: simplifiedPackages,
        full: fullPackages
    };

    // Define additional packages with dynamic pricing
    const additionalPackages = {
        simplified: {
            goldPack50: { sku: 'BR00019', price: pricingData.additionalPackages.goldPack50.price, documents: 50 }, // For GOLD
            platinumPack50: { sku: 'BR00020', price: pricingData.additionalPackages.platinumPack50.price, documents: 50 }, // For PLATINUM
            platinumPack200: { sku: 'BR00021', price: pricingData.additionalPackages.platinumPack200.price, documents: 200 } // For PLATINUM
        },
        full: {
            goldPack50: { sku: 'BR00027', price: pricingData.additionalPackages.goldPack50.price, documents: 50 }, // For GOLD
            platinumPack50: { sku: 'BR00028', price: pricingData.additionalPackages.platinumPack50.price, documents: 50 }, // For PLATINUM
            platinumPack200: { sku: 'BR00029', price: pricingData.additionalPackages.platinumPack200.price, documents: 200 } // For PLATINUM
        }
    };

    // Define individual document SKUs
    const individualDocumentSkus = {
        simplified: {
            BASE: 'BR00015',
            SILVER: 'BR00016',
            GOLD: 'BR00017',
            PLATINUM: 'BR00018'
        },
        full: {
            BASE: 'BR00022',
            SILVER: 'BR00023',
            GOLD: 'BR00024',
            PLATINUM: 'BR00025'
        }
    };

    const accountingType = isFullAccounting ? 'full' : 'simplified';
    const availablePackages = packages[accountingType];
    const additionalPacks = additionalPackages[accountingType];
    const individualSkus = individualDocumentSkus[accountingType];

    // Use dynamic pricing for 95% rule calculations - use actual package prices for the specific accounting type
    const packagePricing = {
        BASE: availablePackages.BASE?.price,
        SILVER: availablePackages.SILVER?.price,
        GOLD: availablePackages.GOLD?.price,
        PLATINUM: availablePackages.PLATINUM?.price
    };

    // Validate that all package prices are available
    for (const [packageName, price] of Object.entries(packagePricing)) {
        if (price === undefined || price === null || isNaN(price) || price <= 0) {
            throw new Error(`Invalid or missing price for ${packageName} package: ${price}`);
        }
    }

    // Calculate optimal package using 95% price threshold logic
    const optimalPackage = calculateOptimalPackageWith95PercentRule(
        documentCount,
        availablePackages,
        additionalPacks,
        individualSkus,
        packagePricing
    );

    console.log(`Selected ${optimalPackage.packageName} package using 95% rule for ${documentCount} documents`);
    console.log('Package result:', optimalPackage);
    return optimalPackage.items;
}

/**
 * Compare current accounting packages with optimal packages
 * @param {Array} currentItems - Current line items in the deal
 * @param {Array} optimalPackages - Optimal package configuration
 * @returns {boolean} True if packages match, false if they need updating
 */
export function compareAccountingPackages(currentItems, optimalPackages) {
    console.log('Comparing current packages with optimal packages...');
    console.log('Current items:', currentItems.map(item => ({
        sku: item.properties.hs_sku,
        quantity: item.properties.quantity || 1
    })));
    console.log('Optimal packages:', optimalPackages);

    // If quantities don't match, packages don't match
    if (currentItems.length !== optimalPackages.length) {
        console.log('Package count mismatch');
        return false;
    }

    // Create maps for easier comparison
    const currentMap = new Map();
    currentItems.forEach(item => {
        const sku = item.properties.hs_sku || item.properties.name || '';
        const quantity = parseInt(item.properties.quantity) || 1;
        currentMap.set(sku, (currentMap.get(sku) || 0) + quantity);
    });

    const optimalMap = new Map();
    optimalPackages.forEach(pkg => {
        optimalMap.set(pkg.sku, (optimalMap.get(pkg.sku) || 0) + pkg.quantity);
    });

    // Compare each SKU and quantity
    for (const [sku, quantity] of optimalMap) {
        if (currentMap.get(sku) !== quantity) {
            console.log(`Mismatch for ${sku}: current=${currentMap.get(sku)}, optimal=${quantity}`);
            return false;
        }
    }

    // Check for extra items in current that shouldn't be there
    for (const [sku, quantity] of currentMap) {
        if (!optimalMap.has(sku)) {
            console.log(`Extra item found: ${sku} (quantity: ${quantity})`);
            return false;
        }
    }

    console.log('Packages match perfectly');
    return true;
}

/**
 * Calculate optimal package using 95% price threshold rule
 * When package + additional documents >= 95% of next package price, jump to next package
 * @param {number} documentCount - Number of documents needed
 * @param {Object} availablePackages - Available package configurations
 * @param {Object} additionalPacks - Additional package configurations
 * @param {Object} individualSkus - Individual document SKUs
 * @param {Object} packagePricing - Base package pricing for 95% rule
 * @returns {Object|null} Optimal package configuration or null
 */
export function calculateOptimalPackageWith95PercentRule(documentCount, availablePackages, additionalPacks, individualSkus, packagePricing) {
    console.log('Calculating optimal package with 95% rule for', documentCount, 'documents');
    console.log('Package pricing:', packagePricing);

    // Package order for upgrade logic
    const packageOrder = ['BASE', 'SILVER', 'GOLD', 'PLATINUM'];

    // Calculate cost for each package and check 95% rule
    for (let i = 0; i < packageOrder.length; i++) {
        const packageName = packageOrder[i];
        const packageData = availablePackages[packageName];

        if (!packageData) continue;

        // Calculate cost for current package
        const result = calculatePackageCost(documentCount, packageData, additionalPacks, individualSkus[packageName], packageName);

        // If this package can't handle the documents (infinite cost), skip to next
        if (result.totalCost === Infinity) {
            continue;
        }

        console.log(`${packageName} package cost: ${result.totalCost}`);

        // Check if there's a next package to compare against
        const nextPackageIndex = i + 1;
        if (nextPackageIndex < packageOrder.length && packagePricing[packageOrder[nextPackageIndex]]) {
            const nextPackagePrice = packagePricing[packageOrder[nextPackageIndex]];
            const threshold95Percent = nextPackagePrice * 0.95;

            console.log(`Checking 95% rule: ${result.totalCost} >= ${threshold95Percent} (95% of ${nextPackagePrice})`);

            // If current package cost >= 95% of next package, try next package
            if (result.totalCost >= threshold95Percent) {
                console.log(`95% threshold reached, checking ${packageOrder[nextPackageIndex]} package`);
                continue;
            }
        }

        // This package is optimal
        console.log(`Selected ${packageName} package as optimal`);
        return {
            packageName,
            items: result.items,
            totalCost: result.totalCost
        };
    }

    // If we get here, there's an error in the package selection logic
    throw new Error(`Failed to select optimal package for ${documentCount} documents. Package selection logic error.`);
}

/**
 * Calculate the total cost and items needed for a specific base package
 * @param {number} documentCount - Number of documents needed
 * @param {Object} basePackage - Base package configuration
 * @param {Object} additionalPacks - Additional package configurations
 * @param {string} individualSku - SKU for individual documents
 * @param {string} packageName - Name of the package (BASE, SILVER, GOLD, PLATINUM)
 * @returns {Object} Object with items array and total cost
 */
export function calculatePackageCost(documentCount, basePackage, additionalPacks, individualSku, packageName) {
    const items = [];
    let remainingDocuments = documentCount;
    let totalCost = 0;

    // Add base package
    items.push({
        sku: basePackage.sku,
        quantity: 1,
        description: `${packageName} package (${basePackage.documents} documents)`,
        price: basePackage.price
    });
    totalCost += basePackage.price;
    remainingDocuments -= basePackage.documents;

    if (remainingDocuments <= 0) {
        return { items, totalCost };
    }

    // Handle additional documents based on package type
    switch (packageName) {
        case 'PLATINUM':
            // Use 200-document packs first (more cost-effective)
            const pack200Count = Math.floor(remainingDocuments / 200);
            if (pack200Count > 0) {
                items.push({
                    sku: additionalPacks.platinumPack200.sku,
                    quantity: pack200Count,
                    description: `200-document additional packs for PLATINUM`,
                    price: additionalPacks.platinumPack200.price
                });
                totalCost += pack200Count * additionalPacks.platinumPack200.price;
                remainingDocuments -= pack200Count * 200;
            }

            // Use 50-document packs for remaining
            const platinumPack50Count = Math.floor(remainingDocuments / 50);
            if (platinumPack50Count > 0) {
                items.push({
                    sku: additionalPacks.platinumPack50.sku,
                    quantity: platinumPack50Count,
                    description: `50-document additional packs for PLATINUM`,
                    price: additionalPacks.platinumPack50.price
                });
                totalCost += platinumPack50Count * additionalPacks.platinumPack50.price;
                remainingDocuments -= platinumPack50Count * 50;
            }
            break;

        case 'GOLD':
            // For GOLD packages, we can use 50-document additional packs
            const goldPack50Count = Math.floor(remainingDocuments / 50);
            if (goldPack50Count > 0) {
                items.push({
                    sku: additionalPacks.goldPack50.sku,
                    quantity: goldPack50Count,
                    description: `50-document additional packs for GOLD`,
                    price: additionalPacks.goldPack50.price
                });
                totalCost += goldPack50Count * additionalPacks.goldPack50.price;
                remainingDocuments -= goldPack50Count * 50;
            }

            // Check if remaining documents are within individual document limit
            const goldMaxExtraAllowed = basePackage.maxExtra;
            if (remainingDocuments <= goldMaxExtraAllowed) {
                // Use individual documents
                if (remainingDocuments > 0) {
                    items.push({
                        sku: individualSku,
                        quantity: remainingDocuments,
                        description: `Individual documents`,
                        price: basePackage.extraPrice
                    });
                    totalCost += remainingDocuments * basePackage.extraPrice;
                    remainingDocuments = 0;
                }
            } else {
                // Exceeds limit, this package is not viable - set high cost
                totalCost = Infinity;
            }
            break;

        default:
            // For non-PLATINUM/GOLD packages, check if we can use individual documents within limit
            const maxExtraAllowed = basePackage.maxExtra;
            if (remainingDocuments <= maxExtraAllowed) {
                // Use individual documents
                items.push({
                    sku: individualSku,
                    quantity: remainingDocuments,
                    description: `Individual documents`,
                    price: basePackage.extraPrice
                });
                totalCost += remainingDocuments * basePackage.extraPrice;
                remainingDocuments = 0;
            } else {
                // Exceeds limit, this package is not viable - set high cost
                totalCost = Infinity;
            }
            break;
    }

    // Add any remaining individual documents for PLATINUM
    if (remainingDocuments > 0 && packageName === 'PLATINUM') {
        items.push({
            sku: individualSku,
            quantity: remainingDocuments,
            description: `Individual documents`,
            price: basePackage.extraPrice
        });
        totalCost += remainingDocuments * basePackage.extraPrice;
    }

    return { items, totalCost };
}

/**
 * Wrapper function for test compatibility - selects optimal accounting package
 * @param {number} documentCount - Number of documents needed
 * @param {boolean} isFullAccounting - Whether it's full accounting or simplified
 * @param {string} accessToken - HubSpot access token for fetching prices
 * @returns {Promise<Object>} Package selection result
 */
export async function selectOptimalAccountingPackage(documentCount, isFullAccounting, accessToken) {
    // Don't catch errors here - let them propagate to match test expectations
    const pricingData = await getAccountingPackagePrices(isFullAccounting, accessToken);

        // Define base package configurations with dynamic pricing
        const simplifiedPackages = {
            BASE: { sku: 'BR00007', price: pricingData.packages.BASE.price, documents: 5, extraPrice: pricingData.individualPrices.BASE },
            SILVER: { sku: 'BR00008', price: pricingData.packages.SILVER.price, documents: 20, extraPrice: pricingData.individualPrices.SILVER },
            GOLD: { sku: 'BR00009', price: pricingData.packages.GOLD.price, documents: 80, extraPrice: pricingData.individualPrices.GOLD },
            PLATINUM: { sku: 'BR00010', price: pricingData.packages.PLATINUM.price, documents: 150, extraPrice: pricingData.individualPrices.PLATINUM }
        };

        const fullPackages = {
            BASE: { sku: 'BR00003', price: pricingData.packages.BASE.price, documents: 5, extraPrice: pricingData.individualPrices.BASE },
            SILVER: { sku: 'BR00004', price: pricingData.packages.SILVER.price, documents: 50, extraPrice: pricingData.individualPrices.SILVER },
            GOLD: { sku: 'BR00005', price: pricingData.packages.GOLD.price, documents: 110, extraPrice: pricingData.individualPrices.GOLD },
            PLATINUM: { sku: 'BR00006', price: pricingData.packages.PLATINUM.price, documents: 200, extraPrice: pricingData.individualPrices.PLATINUM }
        };

        // Calculate maxExtra for each package based on 95% rule
        simplifiedPackages.BASE.maxExtra = calculateMaxExtraDocuments(simplifiedPackages.BASE, simplifiedPackages.SILVER);
        simplifiedPackages.SILVER.maxExtra = calculateMaxExtraDocuments(simplifiedPackages.SILVER, simplifiedPackages.GOLD);
        simplifiedPackages.GOLD.maxExtra = calculateMaxExtraDocuments(simplifiedPackages.GOLD, simplifiedPackages.PLATINUM);
        simplifiedPackages.PLATINUM.maxExtra = Infinity;

        fullPackages.BASE.maxExtra = calculateMaxExtraDocuments(fullPackages.BASE, fullPackages.SILVER);
        fullPackages.SILVER.maxExtra = calculateMaxExtraDocuments(fullPackages.SILVER, fullPackages.GOLD);
        fullPackages.GOLD.maxExtra = calculateMaxExtraDocuments(fullPackages.GOLD, fullPackages.PLATINUM);
        fullPackages.PLATINUM.maxExtra = Infinity;

        // Combine into final packages structure
        const packages = {
            simplified: simplifiedPackages,
            full: fullPackages
        };

        // Define additional packages with dynamic pricing
        const additionalPackages = {
            simplified: {
                goldPack50: { sku: 'BR00019', price: pricingData.additionalPackages.goldPack50.price, documents: 50 },
                platinumPack50: { sku: 'BR00020', price: pricingData.additionalPackages.platinumPack50.price, documents: 50 },
                platinumPack200: { sku: 'BR00021', price: pricingData.additionalPackages.platinumPack200.price, documents: 200 }
            },
            full: {
                goldPack50: { sku: 'BR00027', price: pricingData.additionalPackages.goldPack50.price, documents: 50 },
                platinumPack50: { sku: 'BR00028', price: pricingData.additionalPackages.platinumPack50.price, documents: 50 },
                platinumPack200: { sku: 'BR00029', price: pricingData.additionalPackages.platinumPack200.price, documents: 200 }
            }
        };

        // Define individual document SKUs
        const individualDocumentSkus = {
            simplified: {
                BASE: 'BR00015',
                SILVER: 'BR00016',
                GOLD: 'BR00017',
                PLATINUM: 'BR00018'
            },
            full: {
                BASE: 'BR00022',
                SILVER: 'BR00023',
                GOLD: 'BR00024',
                PLATINUM: 'BR00025'
            }
        };

        const accountingType = isFullAccounting ? 'full' : 'simplified';
        const availablePackages = packages[accountingType];
        const additionalPacks = additionalPackages[accountingType];
        const individualSkus = individualDocumentSkus[accountingType];

        // Use dynamic pricing for 95% rule calculations
        const packagePricing = {
            BASE: availablePackages.BASE?.price,
            SILVER: availablePackages.SILVER?.price,
            GOLD: availablePackages.GOLD?.price,
            PLATINUM: availablePackages.PLATINUM?.price
        };

        // Validate that all package prices are available
        for (const [packageName, price] of Object.entries(packagePricing)) {
            if (price === undefined || price === null || isNaN(price) || price <= 0) {
                throw new Error(`Invalid or missing price for ${packageName} package: ${price}`);
            }
        }

        // Calculate optimal package using 95% price threshold logic
        const optimalPackage = calculateOptimalPackageWith95PercentRule(
            documentCount,
            availablePackages,
            additionalPacks,
            individualSkus,
            packagePricing
        );

        // Calculate extra documents and SKU
        const packageData = availablePackages[optimalPackage.packageName];
        if (!packageData) {
            throw new Error(`Package data not found for ${optimalPackage.packageName}`);
        }

        const extraDocuments = Math.max(0, documentCount - packageData.documents);
        const extraSku = extraDocuments > 0 ? individualSkus[optimalPackage.packageName] : undefined;

        // Return in expected test format
        return {
            selectedPackage: optimalPackage.packageName,
            packageSku: packageData.sku,
            extraDocuments: extraDocuments,
            extraSku: extraSku,
            totalCost: optimalPackage.totalCost,
            items: optimalPackage.items
        };
}

/**
 * Calculate accounting package cost for test compatibility
 * @param {string} packageType - Package type (BASE, SILVER, GOLD, PLATINUM)
 * @param {string} packageSku - Package SKU
 * @param {number} extraDocuments - Number of extra documents
 * @param {string} extraSku - SKU for extra documents
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<number>} Total cost
 */
export async function calculateAccountingPackageCost(packageType, packageSku, extraDocuments, extraSku, accessToken) {
    try {
        const pricingData = await getAccountingPackagePrices(true, accessToken); // Assume full accounting for simplicity

        // Get package price
        const packagePrice = pricingData.prices[packageSku] || 0;

        // Get extra document price
        const extraPrice = extraSku ? (pricingData.prices[extraSku] || 0) : 0;

        // Calculate total cost
        const totalCost = packagePrice + (extraDocuments * extraPrice);

        return totalCost;
    } catch (error) {
        console.error('Error calculating package cost:', error);
        throw error;
    }
}

/**
 * Get accounting package configuration for test compatibility
 * @deprecated This function uses hardcoded maxExtra values. Use selectOptimalAccountingPackage with real pricing data instead.
 * @param {boolean} isFullAccounting - Whether it's full accounting or simplified
 * @returns {Object} Package configuration
 * @throws {Error} Always throws an error to encourage using dynamic pricing
 */
export function getAccountingPackageConfig(isFullAccounting) {
    throw new Error(
        'getAccountingPackageConfig is deprecated. Use selectOptimalAccountingPackage with real pricing data instead. ' +
        'This function used hardcoded maxExtra values which are now calculated dynamically based on the 95% rule.'
    );
}