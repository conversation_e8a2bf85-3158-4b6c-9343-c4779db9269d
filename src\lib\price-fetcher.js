/**
 * Price fetching utilities for HubSpot products
 * Fetches prices dynamically from HubSpot instead of using hardcoded values
 */

import { findProductBySku } from './hubspot-api.js';

// Cache for product prices to avoid repeated API calls
const priceCache = new Map();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

// Rate limiting
let lastApiCall = 0;
const MIN_API_INTERVAL = 1100; // 1.1 seconds between API calls to avoid rate limits

// Test mode flag to disable rate limiting for tests
let isTestMode = false;

/**
 * Enable test mode to disable rate limiting
 */
export function enableTestMode() {
    isTestMode = true;
}

/**
 * Disable test mode to re-enable rate limiting
 */
export function disableTestMode() {
    isTestMode = false;
}

/**
 * Add delay to respect rate limits
 */
async function rateLimitDelay() {
    // Skip delay in test mode
    if (isTestMode) {
        return;
    }

    const now = Date.now();
    const timeSinceLastCall = now - lastApiCall;

    if (timeSinceLastCall < MIN_API_INTERVAL) {
        const delay = MIN_API_INTERVAL - timeSinceLastCall;
        await new Promise(resolve => setTimeout(resolve, delay));
    }

    lastApiCall = Date.now();
}

/**
 * Search for multiple products in smaller batches to avoid rate limits
 * @param {Array<string>} skus - Array of SKUs to search for
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<Object>} Object with SKU as key and product as value
 */
async function searchProductsBatch(skus, accessToken) {
    try {
        const results = {};
        const BATCH_SIZE = 5; // Process 5 SKUs at a time to be conservative

        console.log(`Batch searching for ${skus.length} products`);

        // Process SKUs in smaller batches
        for (let i = 0; i < skus.length; i += BATCH_SIZE) {
            const batch = skus.slice(i, i + BATCH_SIZE);

            await rateLimitDelay();

            // Create filter groups for this batch
            const filterGroups = batch.map(sku => ({
                filters: [{
                    propertyName: 'hs_sku',
                    operator: 'EQ',
                    value: sku
                }]
            }));

            try {
                const response = await fetch('https://api.hubapi.com/crm/v3/objects/products/search', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${accessToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        filterGroups: filterGroups,
                        properties: ['name', 'hs_sku', 'price', 'description'],
                        limit: BATCH_SIZE
                    })
                });

                if (!response.ok) {
                    const errorData = await response.text();
                    console.error(`Batch search failed: ${response.statusText} - ${errorData}`);
                    continue; // Skip this batch and continue with the next
                }

                const data = await response.json();

                // Map results back to SKUs
                if (data.results) {
                    data.results.forEach(product => {
                        const sku = product.properties.hs_sku;
                        if (sku && batch.includes(sku)) {
                            results[sku] = product;
                        }
                    });
                }

            } catch (batchError) {
                console.error(`Error in batch ${Math.floor(i/BATCH_SIZE) + 1}:`, batchError.message);
                continue; // Continue with next batch
            }
        }

        console.log(`Batch search found ${Object.keys(results).length}/${skus.length} products`);
        return results;

    } catch (error) {
        console.error('Error in batch product search:', error);
        return {};
    }
}

/**
 * Get product price from HubSpot by SKU with caching
 * @param {string} sku - Product SKU
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<number>} Product price or 0 if not found
 */
export async function getProductPrice(sku, accessToken) {
    try {
        // Check cache first
        const cacheKey = `${sku}_${accessToken.slice(-10)}`;
        const cached = priceCache.get(cacheKey);

        if (cached && (Date.now() - cached.timestamp) < CACHE_DURATION) {
            return cached.price;
        }

        await rateLimitDelay();
        const product = await findProductBySku(sku, accessToken);

        if (product && product.properties && product.properties.price) {
            const priceValue = parseFloat(product.properties.price);

            // Validate that price is a valid number and greater than 0
            if (isNaN(priceValue) || priceValue <= 0) {
                throw new Error(`Invalid price value for SKU ${sku}: ${product.properties.price}`);
            }

            // Cache the result
            priceCache.set(cacheKey, {
                price: priceValue,
                timestamp: Date.now()
            });

            return priceValue;
        } else {
            throw new Error(`Product not found or missing price for SKU: ${sku}`);
        }
    } catch (error) {
        console.error(`Error fetching price for SKU ${sku}:`, error);
        throw new Error(`Failed to fetch price for SKU ${sku}: ${error.message}`);
    }
}

/**
 * Get multiple product prices with rate limiting and batching
 * @param {Array<string>} skus - Array of SKUs to fetch prices for
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<Object>} Object with SKU as key and price as value
 */
export async function getMultipleProductPrices(skus, accessToken) {
    try {
        console.log(`Fetching prices for ${skus.length} SKUs`);

        // Check cache first for all SKUs
        const priceMap = {};
        const uncachedSkus = [];

        for (const sku of skus) {
            const cacheKey = `${sku}_${accessToken.slice(-10)}`;
            const cached = priceCache.get(cacheKey);

            if (cached && (Date.now() - cached.timestamp) < CACHE_DURATION) {
                priceMap[sku] = cached.price;
            } else {
                uncachedSkus.push(sku);
            }
        }

        if (uncachedSkus.length === 0) {
            console.log('All prices found in cache');
            return priceMap;
        }

        console.log(`Need to fetch ${uncachedSkus.length} prices from HubSpot`);

        // Try batch search first, fallback to individual calls if needed
        let batchResults = {};

        try {
            batchResults = await searchProductsBatch(uncachedSkus, accessToken);
        } catch (error) {
            console.warn('Batch search failed, falling back to individual calls:', error.message);
        }

        // Process batch results and handle missing ones individually
        for (const sku of uncachedSkus) {
            let product = batchResults[sku];
            let price = 0;

            if (product && product.properties && product.properties.price) {
                const priceValue = parseFloat(product.properties.price);

                // Validate that price is a valid number and greater than 0
                if (isNaN(priceValue) || priceValue <= 0) {
                    throw new Error(`Invalid price value for SKU ${sku}: ${product.properties.price}`);
                }

                price = priceValue;
            } else {
                // Try individual search with rate limiting
                try {
                    await rateLimitDelay();
                    const { findProductBySku } = await import('$lib/hubspot-api.js');
                    product = await findProductBySku(sku, accessToken);

                    if (product && product.properties && product.properties.price) {
                        const individualPriceValue = parseFloat(product.properties.price);

                        // Validate that price is a valid number and greater than 0
                        if (isNaN(individualPriceValue) || individualPriceValue <= 0) {
                            throw new Error(`Invalid price value for SKU ${sku}: ${product.properties.price}`);
                        }

                        price = individualPriceValue;
                    } else {
                        throw new Error(`Price not available for SKU ${sku} in HubSpot`);
                    }
                } catch (fallbackError) {
                    console.error(`Failed to fetch price for ${sku}:`, fallbackError.message);
                    throw new Error(`Unable to fetch price for SKU ${sku}: ${fallbackError.message}`);
                }
            }

            // Cache the result
            const cacheKey = `${sku}_${accessToken.slice(-10)}`;
            priceCache.set(cacheKey, {
                price,
                timestamp: Date.now()
            });

            priceMap[sku] = price;
        }

        console.log('Fetched prices:', priceMap);
        return priceMap;
    } catch (error) {
        console.error('Error fetching multiple product prices:', error);
        // Re-throw the error instead of returning empty object
        throw new Error(`Failed to fetch product prices: ${error.message}`);
    }
}

/**
 * Get accounting package prices from HubSpot
 * @param {boolean} isFullAccounting - Whether it's full accounting or simplified
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<Object>} Package pricing configuration
 */
export async function getAccountingPackagePrices(isFullAccounting, accessToken) {
    try {
        const accountingType = isFullAccounting ? 'full' : 'simplified';
        
        // Define SKUs for each package type
        const packageSkus = isFullAccounting ? {
            BASE: 'BR00003',
            SILVER: 'BR00004', 
            GOLD: 'BR00005',
            PLATINUM: 'BR00006'
        } : {
            BASE: 'BR00007',
            SILVER: 'BR00008',
            GOLD: 'BR00009', 
            PLATINUM: 'BR00010'
        };
        
        // Define additional package SKUs
        const additionalSkus = isFullAccounting ? {
            goldPack50: 'BR00027',
            platinumPack50: 'BR00028',
            platinumPack200: 'BR00029'
        } : {
            goldPack50: 'BR00019',
            platinumPack50: 'BR00020',
            platinumPack200: 'BR00021'
        };
        
        // Define individual document SKUs
        const individualSkus = isFullAccounting ? {
            BASE: 'BR00022',
            SILVER: 'BR00023',
            GOLD: 'BR00024',
            PLATINUM: 'BR00025'
        } : {
            BASE: 'BR00015',
            SILVER: 'BR00016',
            GOLD: 'BR00017',
            PLATINUM: 'BR00018'
        };
        
        // Fetch all prices in a single batch to minimize API calls
        const allSkus = [
            ...Object.values(packageSkus),
            ...Object.values(additionalSkus),
            ...Object.values(individualSkus)
        ];

        console.log(`Fetching ${allSkus.length} accounting package prices in batch`);
        const prices = await getMultipleProductPrices(allSkus, accessToken);

        // Validate that all required prices were fetched successfully
        for (const sku of allSkus) {
            if (!prices[sku] || prices[sku] === 0) {
                throw new Error(`Price not available for accounting SKU ${sku}`);
            }
        }

        return {
            packages: {
                BASE: { sku: packageSkus.BASE, price: prices[packageSkus.BASE] },
                SILVER: { sku: packageSkus.SILVER, price: prices[packageSkus.SILVER] },
                GOLD: { sku: packageSkus.GOLD, price: prices[packageSkus.GOLD] },
                PLATINUM: { sku: packageSkus.PLATINUM, price: prices[packageSkus.PLATINUM] }
            },
            additionalPackages: {
                goldPack50: { sku: additionalSkus.goldPack50, price: prices[additionalSkus.goldPack50] },
                platinumPack50: { sku: additionalSkus.platinumPack50, price: prices[additionalSkus.platinumPack50] },
                platinumPack200: { sku: additionalSkus.platinumPack200, price: prices[additionalSkus.platinumPack200] }
            },
            individualPrices: {
                BASE: prices[individualSkus.BASE],
                SILVER: prices[individualSkus.SILVER],
                GOLD: prices[individualSkus.GOLD],
                PLATINUM: prices[individualSkus.PLATINUM]
            }
        };
    } catch (error) {
        console.error('Error fetching accounting package prices:', error);
        // Throw error instead of using fallback pricing
        throw new Error(`Failed to fetch accounting package prices from HubSpot: ${error.message}`);
    }
}

/**
 * Get e-commerce package prices from HubSpot
 * @param {boolean} isFullAccounting - Whether it's full accounting or simplified
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<Object>} E-commerce package pricing configuration
 */
export async function getEcommercePackagePrices(isFullAccounting, accessToken) {
    try {
        // Define SKUs for e-commerce packages
        const packageSkus = isFullAccounting ? {
            BR00058: 'BR00058', // 200 transactions
            BR00059: 'BR00059', // 1000 transactions
            BR00060: 'BR00060', // 5000 transactions
            BR00061: 'BR00061'  // 20000 transactions
        } : {
            BR00090: 'BR00090', // 200 transactions
            BR00091: 'BR00091', // 1000 transactions
            BR00092: 'BR00092', // 5000 transactions
            BR00093: 'BR00093'  // 20000 transactions
        };
        
        // Define additional transaction SKUs
        const additionalSkus = isFullAccounting ? {
            BR00170: 'BR00170', // Additional for 200 package
            BR00171: 'BR00171', // Additional for 1000 package
            BR00172: 'BR00172', // Additional for 5000 package
            BR00173: 'BR00173'  // Additional for 20000 package
        } : {
            BR00166: 'BR00166', // Additional for 200 package
            BR00167: 'BR00167', // Additional for 1000 package
            BR00168: 'BR00168', // Additional for 5000 package
            BR00169: 'BR00169'  // Additional for 20000 package
        };
        
        // Fetch all prices
        const allSkus = [
            ...Object.values(packageSkus),
            ...Object.values(additionalSkus)
        ];
        
        const prices = await getMultipleProductPrices(allSkus, accessToken);

        // Validate that all required prices were fetched successfully
        for (const sku of allSkus) {
            if (!prices[sku] || prices[sku] === 0) {
                throw new Error(`Price not available for e-commerce SKU ${sku}`);
            }
        }

        return {
            packages: packageSkus,
            additionalSkus: additionalSkus,
            prices: prices
        };
    } catch (error) {
        console.error('Error fetching e-commerce package prices:', error);
        // Throw error instead of using fallback pricing
        throw new Error(`Failed to fetch e-commerce package prices from HubSpot: ${error.message}`);
    }
}

/**
 * Get payroll line item prices from HubSpot
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<Object>} Payroll line item prices
 */
export async function getPayrollPrices(accessToken) {
    try {
        const payrollSkus = [
            'BR00070', // Premium employment contracts
            'BR00071', // Premium civil contracts
            'BR00080', // PFRON declaration
            'BR00165', // A1 certificate
            'BR00076'  // Mobility packages
        ];
        
        const prices = await getMultipleProductPrices(payrollSkus, accessToken);
        
        // Validate that all prices were fetched successfully
        const requiredSkus = ['BR00070', 'BR00071', 'BR00080', 'BR00165', 'BR00076'];
        for (const sku of requiredSkus) {
            if (!prices[sku] || prices[sku] === 0) {
                throw new Error(`Price not available for payroll SKU ${sku}`);
            }
        }

        return {
            BR00070: prices['BR00070'],
            BR00071: prices['BR00071'],
            BR00080: prices['BR00080'],
            BR00165: prices['BR00165'],
            BR00076: prices['BR00076']
        };
    } catch (error) {
        console.error('Error fetching payroll prices:', error);
        // Throw error instead of using fallback prices
        throw new Error(`Failed to fetch payroll prices from HubSpot: ${error.message}`);
    }
}

// Fallback prices have been removed - all prices must be fetched from HubSpot
// If prices are not available in HubSpot, the system will throw an error

/**
 * Clear price cache (useful for testing or when prices are updated)
 */
export function clearPriceCache() {
    priceCache.clear();
    console.log('Price cache cleared');
}

/**
 * Fetch prices from HubSpot for multiple SKUs (alias for getMultipleProductPrices)
 * @param {string[]} skus - Array of SKUs to fetch prices for
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<Object>} Object with SKU as key and price as value
 */
export async function fetchPricesFromHubSpot(skus, accessToken) {
    return await getMultipleProductPrices(skus, accessToken);
}
