/**
 * Financial Statement Package Selection Logic
 * Handles selection of appropriate financial statement packages for full accounting
 */

import { SKUS, calculateDocumentSum, parseAndValidateNumericField, processVatStatus, DOCUMENT_FIELDS } from './validation-utils.js';
import { getProductPrice, getMultipleProductPrices } from './price-fetcher.js';
import { calculateOptimalEcommercePackage } from './ecommerce-packages.js';

/**
 * Get financial statement package prices from HubSpot
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<Object>} Financial statement package pricing configuration
 */
export async function getFinancialStatementPackagePrices(accessToken) {
    try {
        const financialStatementSkus = [SKUS.BR00099, SKUS.BR00100, SKUS.BR00101, SKUS.BR00102];

        console.log('Fetching financial statement package prices from HubSpot for SKUs:', financialStatementSkus);

        // Use batch fetching instead of individual calls
        const prices = await getMultipleProductPrices(financialStatementSkus, accessToken);

        // Validate that all prices were fetched successfully
        for (const sku of financialStatementSkus) {
            if (!prices[sku] || prices[sku] === 0) {
                throw new Error(`Price not available for financial statement SKU ${sku}`);
            }
        }

        return {
            packages: {
                BASE: { sku: SKUS.BR00099, price: prices[SKUS.BR00099] },
                SILVER: { sku: SKUS.BR00100, price: prices[SKUS.BR00100] },
                GOLD: { sku: SKUS.BR00101, price: prices[SKUS.BR00101] },
                PLATINUM: { sku: SKUS.BR00102, price: prices[SKUS.BR00102] }
            }
        };
    } catch (error) {
        console.error('Error fetching financial statement package prices:', error);
        throw new Error(`Failed to fetch financial statement package prices: ${error.message}`);
    }
}

/**
 * Calculate the base price for financial statement from actual line item costs
 * This function should be called AFTER all other line items have been created
 * @param {Object} allProperties - Deal properties object
 * @param {Array} createdLineItems - Array of line items that were created for the deal
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<number>} Calculated base price from actual line item costs
 */
export async function calculateFinancialStatementBasePriceFromLineItems(allProperties, createdLineItems, accessToken) {
    console.log('=== CALCULATING FINANCIAL STATEMENT BASE PRICE FROM ACTUAL LINE ITEMS ===');
    console.log('Summing costs from actual line items that were created for financial statement pricing override');

    try {
        let totalCost = 0;
        const costBreakdown = [];

        // Define which SKUs should be included in financial statement cost calculation
        // These correspond to the fields you specified
        const relevantSkus = [
            // Accounting packages (from faktury_rachunki_sprzedazowe___ile_, faktury_rachunki_zakupu___ile_, etc.)
            'BR00003', 'BR00004', 'BR00005', 'BR00006', // Full accounting packages
            'BR00007', 'BR00008', 'BR00009', 'BR00010', // Simplified accounting packages
            'BR00027', 'BR00028', 'BR00029', // Additional full accounting packages
            'BR00019', 'BR00020', 'BR00021', // Additional simplified accounting packages

            // Additional document processing (exceeding package limits)
            'BR00022', 'BR00023', 'BR00024', 'BR00025', // Additional documents for all packages

            // Bank statement processing (from kp_kw___banki_, kp_kw_gotowka, operacje_kp_kw_walutowe)
            'BR00013',

            // Cash registers (from kasy_fiskalne___ile_)
            'BR00031',

            // Fixed assets (from srodki_trwale_i_wartosci_niematerialne_i_prawne___ile_)
            'BR00030',

            // VAT (from vat___status_podatnika, excluding VAT OSS and VAT IOSS)
            'BR00032',

            // Banking operations (from ilosc_kont_bankowych___raporty_dzienne, ilosc_kont_bankowych___raporty_miesieczne)
            'BR00012', 'BR00130',

            // E-commerce packages (from ile_transakcji_sprzedazy_w_miesiacu_)
            'BR00058', 'BR00059', 'BR00060', 'BR00061', // Full accounting e-commerce
            'BR00090', 'BR00091', 'BR00092', 'BR00093', // Simplified accounting e-commerce
            'BR00166', 'BR00167', 'BR00168', 'BR00169', // Additional simplified e-commerce
            'BR00170', 'BR00171', 'BR00172', 'BR00173'  // Additional full e-commerce
        ];

        // Sum up costs from relevant line items
        for (const lineItem of createdLineItems) {
            if (relevantSkus.includes(lineItem.sku)) {
                const itemCost = lineItem.price * lineItem.quantity;
                totalCost += itemCost;
                costBreakdown.push(`${lineItem.sku}: ${lineItem.price} × ${lineItem.quantity} = ${itemCost}`);
                console.log(`Including line item ${lineItem.sku}: ${lineItem.price} × ${lineItem.quantity} = ${itemCost}`);
            }
        }

        // Print detailed cost breakdown
        console.log('=== FINANCIAL STATEMENT COST BREAKDOWN FROM ACTUAL LINE ITEMS ===');
        if (costBreakdown.length > 0) {
            costBreakdown.forEach(item => console.log(`- ${item}`));
        } else {
            console.log('- No relevant line items found for financial statement cost calculation');
        }
        console.log(`TOTAL CALCULATED BASE PRICE FROM LINE ITEMS: ${totalCost}`);
        console.log('=== END OF FINANCIAL STATEMENT COST BREAKDOWN ===');

        return totalCost;

    } catch (error) {
        console.error('Error calculating financial statement base price from line items:', error);
        throw new Error(`Failed to calculate financial statement base price from line items: ${error.message}`);
    }
}

/**
 * Calculate the base price for financial statement from line item costs (DEPRECATED)
 * @param {Object} allProperties - Deal properties object
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<number>} Calculated base price from line items
 */
export async function calculateFinancialStatementBasePrice(allProperties, accessToken) {
    console.log('=== CALCULATING FINANCIAL STATEMENT BASE PRICE ===');
    console.log('Summing costs from all line items for financial statement pricing override');

    try {
        let totalCost = 0;
        const costBreakdown = [];
        
        // 1. Document-based costs (faktury_rachunki_sprzedazowe___ile_, faktury_rachunki_zakupu___ile_, 
        //    faktury_walutowe___ile_miesiecznie_, dokumenty_wewnetrzne_wdt__wnt_itp)
        const bookingOperationsSum = calculateDocumentSum(allProperties, DOCUMENT_FIELDS.BOOKING_OPERATIONS);
        console.log('Booking operations sum for financial statement:', bookingOperationsSum);
        
        // Get individual document prices for full accounting
        if (bookingOperationsSum > 0) {
            // Use BR00022 (BASE individual document price for full accounting) as base rate
            const documentPrice = await getProductPrice(SKUS.BR00022, accessToken);
            const documentCost = bookingOperationsSum * documentPrice;
            totalCost += documentCost;
            costBreakdown.push(`Documents (${SKUS.BR00022}): ${bookingOperationsSum} × ${documentPrice} = ${documentCost}`);
            console.log(`Document cost: ${bookingOperationsSum} documents × ${documentPrice} = ${documentCost}`);
        }
        
        // 2. Cash registers (kasy_fiskalne___ile_)
        const cashRegisters = parseAndValidateNumericField(allProperties, 'kasy_fiskalne___ile_');
        if (cashRegisters > 0) {
            const cashRegisterPrice = await getProductPrice(SKUS.BR00031, accessToken);
            const cashRegisterCost = cashRegisters * cashRegisterPrice;
            totalCost += cashRegisterCost;
            costBreakdown.push(`Cash registers (${SKUS.BR00031}): ${cashRegisters} × ${cashRegisterPrice} = ${cashRegisterCost}`);
            console.log(`Cash register cost: ${cashRegisters} × ${cashRegisterPrice} = ${cashRegisterCost}`);
        }
        
        // 3. Fixed assets (srodki_trwale_i_wartosci_niematerialne_i_prawne___ile_)
        const fixedAssets = parseAndValidateNumericField(allProperties, 'srodki_trwale_i_wartosci_niematerialne_i_prawne___ile_');
        if (fixedAssets > 0) {
            const fixedAssetPrice = await getProductPrice(SKUS.BR00030, accessToken);
            const fixedAssetCost = fixedAssets * fixedAssetPrice;
            totalCost += fixedAssetCost;
            costBreakdown.push(`Fixed assets (${SKUS.BR00030}): ${fixedAssets} × ${fixedAssetPrice} = ${fixedAssetCost}`);
            console.log(`Fixed asset cost: ${fixedAssets} × ${fixedAssetPrice} = ${fixedAssetCost}`);
        }
        
        // 4. VAT status (vat___status_podatnika) - excluding VAT OSS and VAT IOSS
        const vatStatus = allProperties['vat___status_podatnika'] || '';
        const vatResult = processVatStatus(vatStatus);
        if (vatResult.shouldHaveBR00032) {
            const vatPrice = await getProductPrice(SKUS.BR00032, accessToken);
            totalCost += vatPrice;
            costBreakdown.push(`VAT EU/8/9M (${SKUS.BR00032}): ${vatPrice}`);
            console.log(`VAT EU/8/9M cost: ${vatPrice}`);
        }
        
        // 5. Banking operations (ilosc_kont_bankowych___raporty_dzienne, ilosc_kont_bankowych___raporty_miesieczne)
        const dailyReports = parseAndValidateNumericField(allProperties, 'ilosc_kont_bankowych___raporty_dzienne');
        if (dailyReports > 0) {
            const dailyReportPrice = await getProductPrice(SKUS.BR00130, accessToken);
            const dailyReportCost = dailyReports * dailyReportPrice;
            totalCost += dailyReportCost;
            costBreakdown.push(`Daily bank reports (${SKUS.BR00130}): ${dailyReports} × ${dailyReportPrice} = ${dailyReportCost}`);
            console.log(`Daily bank report cost: ${dailyReports} × ${dailyReportPrice} = ${dailyReportCost}`);
        }

        const monthlyReports = parseAndValidateNumericField(allProperties, 'ilosc_kont_bankowych___raporty_miesieczne');
        if (monthlyReports > 0) {
            const monthlyReportPrice = await getProductPrice(SKUS.BR00012, accessToken);
            const monthlyReportCost = monthlyReports * monthlyReportPrice;
            totalCost += monthlyReportCost;
            costBreakdown.push(`Monthly bank reports (${SKUS.BR00012}): ${monthlyReports} × ${monthlyReportPrice} = ${monthlyReportCost}`);
            console.log(`Monthly bank report cost: ${monthlyReports} × ${monthlyReportPrice} = ${monthlyReportCost}`);
        }
        
        // 6. Cash-bank operations (kp_kw___banki_, kp_kw_gotowka, operacje_kp_kw_walutowe)
        const cashBankSum = calculateDocumentSum(allProperties, DOCUMENT_FIELDS.CASH_BANK_OPERATIONS);
        if (cashBankSum > 0) {
            // Use BR00013 price for cash-bank operations
            const cashBankPrice = await getProductPrice(SKUS.BR00013, accessToken);
            const cashBankCost = cashBankSum * cashBankPrice;
            totalCost += cashBankCost;
            costBreakdown.push(`Cash-bank operations (${SKUS.BR00013}): ${cashBankSum} × ${cashBankPrice} = ${cashBankCost}`);
            console.log(`Cash-bank operations cost: ${cashBankSum} × ${cashBankPrice} = ${cashBankCost}`);
        }
        
        // 7. E-commerce transactions (ile_transakcji_sprzedazy_w_miesiacu_)
        const transactionCount = parseAndValidateNumericField(allProperties, 'ile_transakcji_sprzedazy_w_miesiacu_');
        if (transactionCount > 0) {
            const ecommercePackage = await calculateOptimalEcommercePackage(transactionCount, true, accessToken);
            if (ecommercePackage && ecommercePackage.totalCost > 0) {
                totalCost += ecommercePackage.totalCost;
                costBreakdown.push(`E-commerce transactions: ${transactionCount} transactions = ${ecommercePackage.totalCost}`);
                console.log(`E-commerce cost: ${ecommercePackage.totalCost}`);
            }
        }

        // Print detailed cost breakdown
        console.log('=== FINANCIAL STATEMENT COST BREAKDOWN ===');
        if (costBreakdown.length > 0) {
            costBreakdown.forEach(item => console.log(`- ${item}`));
        } else {
            console.log('- No costs calculated (all values are zero or empty)');
        }
        console.log(`TOTAL CALCULATED BASE PRICE: ${totalCost}`);
        console.log('=== END OF FINANCIAL STATEMENT COST BREAKDOWN ===');
        return totalCost;
        
    } catch (error) {
        console.error('Error calculating financial statement base price:', error);
        throw new Error(`Failed to calculate financial statement base price: ${error.message}`);
    }
}

/**
 * Select appropriate financial statement package based on accounting package level
 * @param {string} selectedPackageName - The selected accounting package name (BASE, SILVER, GOLD, PLATINUM)
 * @param {Object} allProperties - Deal properties object for base price calculation
 * @param {string} accessToken - HubSpot access token for fetching prices
 * @param {Array} [createdLineItems] - Optional array of line items that were created for the deal
 * @returns {Promise<Array>} Array of financial statement package line items
 */
export async function selectFinancialStatementPackageForFullAccounting(selectedPackageName, allProperties, accessToken, createdLineItems = null) {
    console.log('Selecting financial statement package for full accounting with package:', selectedPackageName);
    console.log('Created line items provided:', createdLineItems ? 'Yes' : 'No');
    
    // Only add financial statement packages for full accounting
    if (!selectedPackageName) {
        console.log('No accounting package selected - no financial statement package needed');
        return [];
    }

    try {
        // Fetch financial statement package prices from HubSpot
        const pricingData = await getFinancialStatementPackagePrices(accessToken);
        console.log('Financial statement package pricing data:', pricingData);

        // Calculate the base price from line items
        let calculatedBasePrice;
        if (createdLineItems && createdLineItems.length > 0) {
            // Use actual line item costs if available (preferred method)
            calculatedBasePrice = await calculateFinancialStatementBasePriceFromLineItems(allProperties, createdLineItems, accessToken);
        } else {
            // Fallback to old method if no line items provided
            console.log('No created line items provided, using fallback calculation method');
            calculatedBasePrice = await calculateFinancialStatementBasePrice(allProperties, accessToken);
        }

        // Map accounting package to corresponding financial statement package
        const financialStatementPackageMapping = {
            'BASE': pricingData.packages.BASE,
            'SILVER': pricingData.packages.SILVER,
            'GOLD': pricingData.packages.GOLD,
            'PLATINUM': pricingData.packages.PLATINUM
        };

        const selectedFinancialStatementPackage = financialStatementPackageMapping[selectedPackageName];
        
        if (!selectedFinancialStatementPackage) {
            console.warn(`No financial statement package mapping found for accounting package: ${selectedPackageName}`);
            return [];
        }

        // Use the higher of the two values: calculated base price or package price
        const finalPrice = Math.max(calculatedBasePrice, selectedFinancialStatementPackage.price);

        console.log(`Selected financial statement package ${selectedFinancialStatementPackage.sku} for accounting package ${selectedPackageName}`);
        console.log(`Package price: ${selectedFinancialStatementPackage.price}, Calculated base price: ${calculatedBasePrice}, Final price: ${finalPrice}`);

        // Enhanced logging for price overwriting
        if (calculatedBasePrice > selectedFinancialStatementPackage.price) {
            console.log(`PRICE OVERRIDE: Financial statement price is being overridden with calculated base price ${calculatedBasePrice} (higher than package price ${selectedFinancialStatementPackage.price})`);
        } else if (calculatedBasePrice < selectedFinancialStatementPackage.price) {
            console.log(`PRICE MAINTAINED: Using package price ${selectedFinancialStatementPackage.price} (higher than calculated base price ${calculatedBasePrice})`);
        } else {
            console.log(`PRICE EQUAL: Package price equals calculated base price (${finalPrice})`);
        }
        
        const result = [{
            sku: selectedFinancialStatementPackage.sku,
            quantity: 1,
            description: `Financial statement ${selectedPackageName} package for full accounting`,
            price: finalPrice,
            customPrice: finalPrice !== selectedFinancialStatementPackage.price
        }];

        console.log('=== FINANCIAL STATEMENT PACKAGE SELECTION RESULT ===');
        console.log(`Selected SKU: ${selectedFinancialStatementPackage.sku}`);
        console.log(`Final price: ${finalPrice}`);
        console.log(`Custom price applied: ${finalPrice !== selectedFinancialStatementPackage.price ? 'YES' : 'NO'}`);
        console.log('=== END OF FINANCIAL STATEMENT PACKAGE SELECTION ===');

        return result;

    } catch (error) {
        console.error('Error selecting financial statement package:', error);
        throw new Error(`Failed to select financial statement package: ${error.message}`);
    }
}
