import { describe, test, expect, beforeEach, vi } from 'vitest';

// Mock dependencies
vi.mock('../../src/lib/hubspot-api.js', () => ({
  findProductBySku: vi.fn()
}));

// Mock global fetch
global.fetch = vi.fn();

import {
  getProductPrice,
  getMultipleProductPrices,
  fetchPricesFromHubSpot,
  getAccountingPackagePrices,
  getPayrollPrices,
  getEcommercePackagePrices,
  clearPriceCache,
  enableTestMode,
  disableTestMode
} from '../../src/lib/price-fetcher.js';

import { findProductBySku } from '../../src/lib/hubspot-api.js';

describe('Price Fetcher', () => {
  const mockAccessToken = 'test-token-**********';
  const mockSku = 'BR00001';

  beforeEach(() => {
    vi.clearAllMocks();
    // Clear the price cache
    clearPriceCache();
    // Enable test mode to disable rate limiting
    enableTestMode();
    // Mock console methods to avoid noise in tests
    vi.spyOn(console, 'log').mockImplementation(() => {});
    vi.spyOn(console, 'error').mockImplementation(() => {});
    vi.spyOn(console, 'warn').mockImplementation(() => {});
  });

  afterEach(() => {
    // Disable test mode
    disableTestMode();
    vi.restoreAllMocks();
  });

  describe('getProductPrice', () => {
    test('should fetch and return product price successfully', async () => {
      const mockProduct = {
        properties: {
          price: '100.50',
          hs_sku: mockSku
        }
      };

      findProductBySku.mockResolvedValue(mockProduct);

      const price = await getProductPrice(mockSku, mockAccessToken);

      expect(findProductBySku).toHaveBeenCalledWith(mockSku, mockAccessToken);
      expect(price).toBe(100.50);
    });

    test('should cache product prices and return from cache on subsequent calls', async () => {
      // Clear cache and reset mock
      clearPriceCache();
      findProductBySku.mockReset();

      const mockProduct = {
        properties: {
          price: '75.25',
          hs_sku: mockSku
        }
      };

      findProductBySku.mockResolvedValue(mockProduct);

      // First call - should fetch from API
      const price1 = await getProductPrice(mockSku, mockAccessToken);
      expect(price1).toBe(75.25);
      expect(findProductBySku).toHaveBeenCalledTimes(1);

      // Second call - should return from cache
      const price2 = await getProductPrice(mockSku, mockAccessToken);
      expect(price2).toBe(75.25);
      expect(findProductBySku).toHaveBeenCalledTimes(1); // No additional API call
    });

    test('should handle cache expiration and refetch price', async () => {
      // Enable fake timers for this test
      vi.useFakeTimers();

      const mockProduct = {
        properties: {
          price: '50.00',
          hs_sku: mockSku
        }
      };

      findProductBySku.mockResolvedValue(mockProduct);

      // First call
      await getProductPrice(mockSku, mockAccessToken);
      expect(findProductBySku).toHaveBeenCalledTimes(1);

      // Advance time beyond cache duration (5 minutes + 1 second)
      vi.advanceTimersByTime(5 * 60 * 1000 + 1000);

      // Second call - should refetch due to cache expiration
      await getProductPrice(mockSku, mockAccessToken);
      expect(findProductBySku).toHaveBeenCalledTimes(2);

      // Restore real timers
      vi.useRealTimers();
    });

    test('should throw error when product not found', async () => {
      // Clear cache and reset mock
      clearPriceCache();
      findProductBySku.mockReset();
      findProductBySku.mockResolvedValue(null);

      await expect(getProductPrice(mockSku, mockAccessToken))
        .rejects.toThrow(`Failed to fetch price for SKU ${mockSku}: Product not found or missing price for SKU: ${mockSku}`);
    });

    test('should throw error when product has no price property', async () => {
      // Clear cache and reset mock
      clearPriceCache();
      findProductBySku.mockReset();

      const mockProduct = {
        properties: {
          hs_sku: mockSku
          // Missing price property
        }
      };

      findProductBySku.mockResolvedValue(mockProduct);

      await expect(getProductPrice(mockSku, mockAccessToken))
        .rejects.toThrow(`Failed to fetch price for SKU ${mockSku}: Product not found or missing price for SKU: ${mockSku}`);
    });

    test('should throw error when price is invalid', async () => {
      // Clear cache and reset mock
      clearPriceCache();
      findProductBySku.mockReset();

      const mockProduct = {
        properties: {
          price: 'invalid-price',
          hs_sku: mockSku
        }
      };

      findProductBySku.mockResolvedValue(mockProduct);

      await expect(getProductPrice(mockSku, mockAccessToken))
        .rejects.toThrow(`Failed to fetch price for SKU ${mockSku}: Invalid price value for SKU ${mockSku}: invalid-price`);
    });

    test('should throw error when price is zero or negative', async () => {
      // Clear cache and reset mock
      clearPriceCache();
      findProductBySku.mockReset();

      const mockProduct = {
        properties: {
          price: '0',
          hs_sku: mockSku
        }
      };

      findProductBySku.mockResolvedValue(mockProduct);

      await expect(getProductPrice(mockSku, mockAccessToken))
        .rejects.toThrow(`Failed to fetch price for SKU ${mockSku}: Invalid price value for SKU ${mockSku}: 0`);
    });

    test('should handle API errors gracefully', async () => {
      // Clear cache and reset mock
      clearPriceCache();
      findProductBySku.mockReset();

      const apiError = new Error('HubSpot API error');
      findProductBySku.mockRejectedValue(apiError);

      await expect(getProductPrice(mockSku, mockAccessToken))
        .rejects.toThrow(`Failed to fetch price for SKU ${mockSku}: HubSpot API error`);
    });
  });

  describe('getMultipleProductPrices', () => {
    test('should fetch multiple product prices successfully', async () => {
      const skus = ['BR00001', 'BR00002', 'BR00003'];
      
      // Mock batch search response
      fetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({
          results: [
            { properties: { hs_sku: 'BR00001', price: '100.00' } },
            { properties: { hs_sku: 'BR00002', price: '200.00' } },
            { properties: { hs_sku: 'BR00003', price: '300.00' } }
          ]
        })
      });

      const prices = await getMultipleProductPrices(skus, mockAccessToken);

      expect(prices).toEqual({
        'BR00001': 100.00,
        'BR00002': 200.00,
        'BR00003': 300.00
      });
    });

    test('should return cached prices when available', async () => {
      const skus = ['BR00001', 'BR00002'];
      
      // First call to populate cache
      fetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({
          results: [
            { properties: { hs_sku: 'BR00001', price: '100.00' } },
            { properties: { hs_sku: 'BR00002', price: '200.00' } }
          ]
        })
      });

      await getMultipleProductPrices(skus, mockAccessToken);
      
      // Clear fetch mock to ensure cache is used
      fetch.mockClear();

      // Second call should use cache
      const prices = await getMultipleProductPrices(skus, mockAccessToken);

      expect(fetch).not.toHaveBeenCalled();
      expect(prices).toEqual({
        'BR00001': 100.00,
        'BR00002': 200.00
      });
    });

    test('should handle batch search failures and fallback to individual calls', async () => {
      const skus = ['BR00001'];
      
      // Mock batch search failure
      fetch.mockRejectedValue(new Error('Batch search failed'));
      
      // Mock individual product lookup
      findProductBySku.mockResolvedValue({
        properties: { hs_sku: 'BR00001', price: '100.00' }
      });

      const prices = await getMultipleProductPrices(skus, mockAccessToken);

      expect(prices).toEqual({
        'BR00001': 100.00
      });
      expect(findProductBySku).toHaveBeenCalledWith('BR00001', mockAccessToken);
    });

    test('should handle invalid prices in batch results', async () => {
      const skus = ['BR00001'];
      
      fetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({
          results: [
            { properties: { hs_sku: 'BR00001', price: 'invalid' } }
          ]
        })
      });

      await expect(getMultipleProductPrices(skus, mockAccessToken))
        .rejects.toThrow('Invalid price value for SKU BR00001: invalid');
    });

    test('should handle empty SKU array', async () => {
      const prices = await getMultipleProductPrices([], mockAccessToken);
      expect(prices).toEqual({});
    });

    test('should handle partial batch results', async () => {
      const skus = ['BR00001', 'BR00002'];
      
      // Mock batch search returning only one result
      fetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({
          results: [
            { properties: { hs_sku: 'BR00001', price: '100.00' } }
            // BR00002 missing from batch results
          ]
        })
      });

      // Mock individual lookup for missing SKU
      findProductBySku.mockResolvedValue({
        properties: { hs_sku: 'BR00002', price: '200.00' }
      });

      const prices = await getMultipleProductPrices(skus, mockAccessToken);

      expect(prices).toEqual({
        'BR00001': 100.00,
        'BR00002': 200.00
      });
    });
  });

  describe('Rate Limiting', () => {
    test('should implement rate limiting delay between API calls', async () => {
      // Enable fake timers for this test
      vi.useFakeTimers();

      const mockProduct = {
        properties: {
          price: '100.00',
          hs_sku: 'BR00001'
        }
      };

      findProductBySku.mockResolvedValue(mockProduct);

      const startTime = Date.now();

      // Make two consecutive calls
      await getProductPrice('BR00001', mockAccessToken);
      await getProductPrice('BR00002', mockAccessToken);

      // Advance timers to simulate the delay
      vi.advanceTimersByTime(1100); // 1.1 seconds

      const endTime = Date.now();

      // The second call should have been delayed
      expect(findProductBySku).toHaveBeenCalledTimes(2);

      // Restore real timers
      vi.useRealTimers();
    });
  });

  describe('Error Handling', () => {
    test('should handle network errors gracefully', async () => {
      // Clear cache and reset mock
      clearPriceCache();
      findProductBySku.mockReset();

      findProductBySku.mockRejectedValue(new Error('Network error'));

      await expect(getProductPrice(mockSku, mockAccessToken))
        .rejects.toThrow('Failed to fetch price for SKU BR00001: Network error');
    });

    test('should handle malformed API responses', async () => {
      // Clear cache and reset mock
      clearPriceCache();
      findProductBySku.mockReset();

      findProductBySku.mockResolvedValue({
        // Missing properties object
      });

      await expect(getProductPrice(mockSku, mockAccessToken))
        .rejects.toThrow(`Failed to fetch price for SKU ${mockSku}: Product not found or missing price for SKU: ${mockSku}`);
    });
  });

  describe('fetchPricesFromHubSpot', () => {
    test('should fetch prices for provided SKUs', async () => {
      const skus = ['BR00001', 'BR00002'];

      fetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({
          results: [
            { properties: { hs_sku: 'BR00001', price: '100.00' } },
            { properties: { hs_sku: 'BR00002', price: '200.00' } }
          ]
        })
      });

      const prices = await fetchPricesFromHubSpot(skus, mockAccessToken);

      expect(prices).toEqual({
        'BR00001': 100.00,
        'BR00002': 200.00
      });
    });

    test('should handle empty SKU array', async () => {
      const prices = await fetchPricesFromHubSpot([], mockAccessToken);
      expect(prices).toEqual({});
    });
  });

  describe('getAccountingPackagePrices', () => {
    test('should fetch accounting package prices for simplified accounting', async () => {
      // Mock findProductBySku to return products with prices for all required SKUs
      const mockProducts = {
        'BR00007': { properties: { hs_sku: 'BR00007', price: '234' } },
        'BR00008': { properties: { hs_sku: 'BR00008', price: '294' } },
        'BR00009': { properties: { hs_sku: 'BR00009', price: '524' } },
        'BR00010': { properties: { hs_sku: 'BR00010', price: '999' } },
        'BR00015': { properties: { hs_sku: 'BR00015', price: '10' } },
        'BR00016': { properties: { hs_sku: 'BR00016', price: '20' } },
        'BR00017': { properties: { hs_sku: 'BR00017', price: '30' } },
        'BR00018': { properties: { hs_sku: 'BR00018', price: '40' } },
        'BR00019': { properties: { hs_sku: 'BR00019', price: '50' } },
        'BR00020': { properties: { hs_sku: 'BR00020', price: '60' } },
        'BR00021': { properties: { hs_sku: 'BR00021', price: '70' } }
      };

      findProductBySku.mockImplementation((sku) => {
        return Promise.resolve(mockProducts[sku] || null);
      });

      const prices = await getAccountingPackagePrices(false, mockAccessToken);

      expect(prices).toHaveProperty('packages');
      expect(prices).toHaveProperty('individualPrices');
      expect(prices).toHaveProperty('additionalPackages');
      expect(prices.packages.BASE.price).toBe(234);
      expect(prices.packages.SILVER.price).toBe(294);
      expect(prices.packages.GOLD.price).toBe(524);
      expect(prices.packages.PLATINUM.price).toBe(999);
    });
  });

  describe('getPayrollPrices', () => {
    test('should fetch payroll-related prices', async () => {
      const mockPrices = {
        'BR00070': 80, 'BR00071': 70, 'BR00080': 50, 'BR00165': 120, 'BR00076': 50
      };

      fetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({
          results: Object.entries(mockPrices).map(([sku, price]) => ({
            properties: { hs_sku: sku, price: price.toString() }
          }))
        })
      });

      const prices = await getPayrollPrices(mockAccessToken);

      expect(prices).toEqual(mockPrices);
    });
  });

  describe('getEcommercePackagePrices', () => {
    test('should fetch e-commerce package prices', async () => {
      // Mock all required e-commerce SKUs for full accounting (default)
      const mockProducts = {
        'BR00058': { properties: { hs_sku: 'BR00058', price: '100' } }, // 200 transactions
        'BR00059': { properties: { hs_sku: 'BR00059', price: '200' } }, // 1000 transactions
        'BR00060': { properties: { hs_sku: 'BR00060', price: '300' } }, // 5000 transactions
        'BR00061': { properties: { hs_sku: 'BR00061', price: '400' } }, // 20000 transactions
        'BR00170': { properties: { hs_sku: 'BR00170', price: '10' } },  // Additional for 200
        'BR00171': { properties: { hs_sku: 'BR00171', price: '20' } },  // Additional for 1000
        'BR00172': { properties: { hs_sku: 'BR00172', price: '30' } },  // Additional for 5000
        'BR00173': { properties: { hs_sku: 'BR00173', price: '40' } }   // Additional for 20000
      };

      findProductBySku.mockImplementation((sku) => {
        return Promise.resolve(mockProducts[sku] || null);
      });

      const result = await getEcommercePackagePrices(true, mockAccessToken); // true for full accounting

      expect(result).toHaveProperty('packages');
      expect(result).toHaveProperty('additionalSkus');
      expect(result).toHaveProperty('prices');
      expect(result.prices['BR00058']).toBe(100);
      expect(result.prices['BR00059']).toBe(200);
    });
  });
});
