/**
 * Business logic handlers for comprehensive data updates
 */

import { getDealProperties, getDealLineItemsWithDetails, updateDealProperties } from './hubspot-api.js';
import { deleteLineItem } from './line-item-manager.js';
import {
    processVatStatus,
    processLanguageProperty,
    DOCUMENT_FIELDS,
    calculateDocumentSum,
    isFullAccountingType,
    calculateBaseDocumentQuantity,
    calculateBR00013DocumentQuantity,
    calculateBR00013QuantityForPackage,
    PAYROLL_CONFIG,
    processPayrollPackage,
    parseAndValidateNumericField
} from './validation-utils.js';
import {
    createAccountingResult,
    mergeVatResult,
    mergeLanguageResult
} from './result-structure-utils.js';
import { selectOptimalAccountingPackage } from './accounting-packages.js';
import { calculateOptimalEcommercePackage } from './ecommerce-packages.js';
import { selectPitPackageForSimplifiedAccounting } from './pit-packages.js';
import { selectFinancialStatementPackageForFullAccounting } from './financial-statement-packages.js';

/**
 * Handle comprehensive data update - recalculates all business logic from scratch
 * @param {string} propertyName - The property name (should be 'aktualizuj_dane')
 * @param {string} propertyValue - The property value
 * @param {string} dealId - Deal ID
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<Object>} Complete business logic processing result
 */
export async function handleComprehensiveUpdate(propertyName, propertyValue, dealId, accessToken) {
    console.log('Processing comprehensive data update - recalculating all business logic from scratch');

    // Get all relevant deal properties for complete recalculation
    const allRelevantFields = [
        // Document fields for accounting calculations
        ...DOCUMENT_FIELDS.ALL_DOCUMENT_FIELDS,
        // E-commerce fields
        'ile_transakcji_sprzedazy_w_miesiacu_',
        // VAT fields
        'vat___status_podatnika',
        // Language fields
        'jezyk_obslugi',
        // Payroll fields
        'pakiet_kadrowo_placowy',
        'umowa_o_prace___liczba_osob',
        'umowy_cywilnoprawne___liczba_pracownikow',
        'ppk___ile_osob_',
        'pfron___ile_osob_',
        'a1___czy_wystepuja_',
        'zatrudnienie_cudzoziemcow___czy_wystepuje_',
        'dodatkowe_skladniki_wynagrodzenia',
        'kto_robi_import_do_moje_ppk',
        // MSP fields
        'pytania_do_msp',
        // Quantity-based fields
        'srodki_trwale_i_wartosci_niematerialne_i_prawne___ile_',
        'kasy_fiskalne___ile_',
        // Mobility package fields
        'ile_jest_pakietow_mobilnosci_',
        'ucp___ile_jest_pakietow_mobilnosci_',
        // Banking fields
        'ilosc_kont_bankowych___raporty_dzienne',
        'ilosc_kont_bankowych___raporty_miesieczne',
        'ile_kanalow_platniczych_',
        // Service and industry fields for field clearing logic
        'uslugi_do_wyceny',
        'branza'
    ];

    const allProperties = await getDealProperties(dealId, accessToken, allRelevantFields);
    console.log('Retrieved all properties for comprehensive recalculation');

    // Handle line item removal when services are turned off (but preserve field values)
    await handleLineItemClearing(dealId, accessToken, allProperties);

    // Handle field clearing when services are removed or conditions are not met
    await handleFieldClearing(dealId, accessToken, allProperties);

    // Create accounting result structure (this will hold all our results)
    const result = createAccountingResult();

    // Calculate mobility settings FIRST so adjusted employee counts are available for all other calculations
    await recalculateMobilitySettings(result, allProperties);

    // Calculate accounting-related values (as they affect other calculations)
    // Calculate adjusted kp_kw___banki_ value (field value + employee counts) for cash-bank sum
    const originalKpKwBanki = parseAndValidateNumericField(allProperties, 'kp_kw___banki_');
    const umowaOPraceOsob = parseAndValidateNumericField(allProperties, 'umowa_o_prace___liczba_osob');
    const umowyCywilnoprawneOsob = parseAndValidateNumericField(allProperties, 'umowy_cywilnoprawne___liczba_pracownikow');

    // Only add employee counts to BR00013 when both conditions are met:
    // 1. 'rodzaj_ksiegowosci' is a full accounting type (Pełna księgowość, Fundacje rodzinne, or Fundacje non profit i NGO)
    // 2. 'uslugi_do_wyceny' contains 'Księgowość'
    const rodzajKsiegowosci = allProperties['rodzaj_ksiegowosci'] || '';
    const uslugiDoWyceny = allProperties['uslugi_do_wyceny'] || '';
    const shouldAddEmployeesToBR00013 = isFullAccountingType(rodzajKsiegowosci) && uslugiDoWyceny.includes('Księgowość');

    const employeeCountsForBR00013 = shouldAddEmployeesToBR00013 ? (umowaOPraceOsob + umowyCywilnoprawneOsob) : 0;
    const adjustedKpKwBanki = originalKpKwBanki + employeeCountsForBR00013;

    console.log(`Employee counts for BR00013: ${employeeCountsForBR00013} (shouldAdd: ${shouldAddEmployeesToBR00013}, rodzaj: '${rodzajKsiegowosci}', uslugi: '${uslugiDoWyceny}')`);
    console.log(`Adjusted kp_kw___banki_: ${originalKpKwBanki} + ${employeeCountsForBR00013} = ${adjustedKpKwBanki}`);

    // Calculate cash-bank sum using adjusted kp_kw___banki_ value
    const originalCashBankSum = calculateDocumentSum(allProperties, DOCUMENT_FIELDS.CASH_BANK_OPERATIONS);
    const cashBankSum = originalCashBankSum - originalKpKwBanki + adjustedKpKwBanki;
    console.log(`Adjusted cash-bank sum: original ${originalCashBankSum} -> adjusted ${cashBankSum} (kp_kw___banki_: ${originalKpKwBanki} -> ${adjustedKpKwBanki})`);

    // Store the adjusted value for later use in banking settings
    result.kpKwBankiLineItemQuantity = adjustedKpKwBanki;
    const bookingsSum = calculateDocumentSum(allProperties, DOCUMENT_FIELDS.BOOKING_OPERATIONS);

    const accountingType = allProperties['rodzaj_ksiegowosci'] || '';
    const isFullAccounting = isFullAccountingType(accountingType);

    console.log('Cash-bank operations sum:', cashBankSum);
    console.log('Bookings sum:', bookingsSum);
    console.log('Accounting type:', accountingType);
    console.log('Is full accounting:', isFullAccounting);

    // Package selection uses only bookings sum
    const baseDocumentQuantity = calculateBaseDocumentQuantity(cashBankSum, bookingsSum, isFullAccounting);
    console.log('Base document quantity for package selection (bookings only):', baseDocumentQuantity);

    // BR00013 calculation uses the new logic: max of (invoices+employees) OR (cash-bank operations)
    const br00013Calculation = calculateBR00013DocumentQuantity(allProperties);
    const br00013DocumentQuantity = br00013Calculation.quantity;
    console.log('BR00013 document quantity (new logic):', br00013DocumentQuantity, 'chosen from sum', br00013Calculation.chosenSum);

    // Handle zero document case or when "Księgowość" is not present - no packages needed
    let selectedPackageName = null;
    let accountingPackages = [];
    let br00013Quantity = 0;
    let br00013BankStatementQuantity = 0;

    // Check if accounting services are active - both full and simplified accounting should get packages
    const hasAccountingServices = uslugiDoWyceny.includes('Księgowość');
    const hasValidAccountingType = rodzajKsiegowosci && rodzajKsiegowosci.trim() !== '';
    const accountingServicesActive = hasAccountingServices && hasValidAccountingType;

    if (!accountingServicesActive) {
        console.log('Accounting services not active - either Księgowość not in uslugi_do_wyceny or rodzaj_ksiegowosci is empty');
        selectedPackageName = null;
        accountingPackages = [];
        br00013Quantity = 0;
        br00013BankStatementQuantity = 0;
    } else if (baseDocumentQuantity === 0) {
        console.log('Zero documents detected - no accounting packages needed');
        selectedPackageName = null;
        accountingPackages = [];
        // BR00013 quantity should only be added for full accounting types, even with zero packages
        if (isFullAccounting) {
            br00013Quantity = br00013DocumentQuantity;
            br00013BankStatementQuantity = bookingsSum > cashBankSum ? br00013DocumentQuantity : 0;
            console.log('BR00013 added for full accounting type with zero packages:', br00013Quantity);
        } else {
            br00013Quantity = 0;
            br00013BankStatementQuantity = 0;
            console.log('BR00013 not added - simplified accounting or empty accounting type');
        }
    } else {
        // Get the optimal package selection result (not just the items)
        const optimalPackageResult = await selectOptimalAccountingPackage(baseDocumentQuantity, isFullAccounting, accessToken);

        // Extract the package name and items
        if (!optimalPackageResult.selectedPackage) {
            throw new Error('Failed to select optimal accounting package');
        }
        if (!optimalPackageResult.items) {
            throw new Error('No accounting package items returned from optimization');
        }

        selectedPackageName = optimalPackageResult.selectedPackage;
        accountingPackages = optimalPackageResult.items;
        console.log('Optimal accounting packages:', accountingPackages);

        // Only calculate BR00013 if we have documents, a selected package, AND full accounting type
        if (br00013DocumentQuantity > 0 && selectedPackageName && isFullAccounting) {
            br00013Quantity = calculateBR00013QuantityForPackage(br00013DocumentQuantity, selectedPackageName, 'for comprehensive update', isFullAccounting);
            br00013BankStatementQuantity = bookingsSum > cashBankSum ? br00013Quantity : 0;
            console.log('BR00013 added for full accounting type with packages:', br00013Quantity);
        } else {
            br00013Quantity = 0;
            br00013BankStatementQuantity = 0;
            if (!isFullAccounting) {
                console.log('BR00013 not added - simplified accounting or empty accounting type');
            } else {
                console.log('BR00013 not added - no documents or no selected package');
            }
        }
    }

    console.log('BR00013 quantity needed:', br00013Quantity);
    console.log('BR00013 bank statement quantity needed:', br00013BankStatementQuantity);

    // Employee counts are already included in the cash-bank sum calculation above
    // No need to add them again to avoid duplication

    // Set accounting results
    result.br00013Quantity = br00013Quantity;
    result.br00013BankStatementQuantity = br00013BankStatementQuantity;
    result.accountingPackages = accountingPackages;
    result.isFullAccounting = isFullAccounting;
    result.baseDocumentQuantity = baseDocumentQuantity; // For package selection (bookings only)
    result.br00013DocumentQuantity = br00013DocumentQuantity; // For BR00013 calculation (max of bookings or cash-bank)
    result.selectedPackageName = selectedPackageName;
    result.cashBankSum = cashBankSum;
    result.bookingsSum = bookingsSum;

    // Recalculate all other business logic areas
    await recalculateEcommercePackages(result, allProperties, isFullAccounting, accessToken);
    await recalculatePitPackages(result, allProperties, isFullAccounting, accessToken);
    await recalculateVatSettings(result, allProperties);
    await recalculateLanguageSettings(result, allProperties);
    await recalculatePayrollSettings(result, allProperties, dealId, accessToken);
    await recalculateMspSettings(result, allProperties, accessToken);
    await recalculateQuantityBasedSettings(result, allProperties);
    await recalculateBankingSettings(result, allProperties, isFullAccounting);

    // NOTE: Financial statement packages are calculated AFTER all other business logic
    // This ensures the calculation is based on actual line item costs rather than estimated costs
    await recalculateFinancialStatementPackages(result, allProperties, isFullAccounting, accessToken);

    result.completeRecalculationPerformed = true;
    console.log('Comprehensive recalculation completed');

    return result;
}

/**
 * Helper functions for complete recalculation
 */

/**
 * Calculate accounting package selection based on document count
 * @param {Object} result - Current result object
 * @param {Object} allProperties - All deal properties
 * @param {string} accessToken - HubSpot access token
 * @returns {Promise<Object>} Updated result object with accounting package selection
 */
export async function calculateAccountingPackageSelection(result, allProperties, accessToken) {
    console.log('Calculating accounting package selection');

    // Ensure result object exists
    if (!result) {
        result = {};
    }

    // Calculate document quantities
    const baseDocumentQuantity = calculateBaseDocumentQuantity(allProperties);
    const br00013DocumentQuantity = calculateBR00013DocumentQuantity(allProperties);
    const isFullAccounting = isFullAccountingType(allProperties);

    // If no documents, return early with zero quantities
    if (baseDocumentQuantity === 0) {
        result.br00013Quantity = 0;
        result.br00013BankStatementQuantity = 0;
        return result;
    }

    // Get the optimal package selection result
    const optimalPackageResult = await selectOptimalAccountingPackage(baseDocumentQuantity, isFullAccounting, accessToken);

    // Validate the result
    if (!optimalPackageResult) {
        throw new Error('Failed to select optimal accounting package');
    }
    if (!optimalPackageResult.items) {
        throw new Error('No accounting package items returned from optimization');
    }

    // Update result with package selection
    result.selectedPackageName = optimalPackageResult.selectedPackage;
    result.accountingPackageItems = optimalPackageResult.items;

    // Calculate BR00013 quantities
    const br00013Quantity = calculateBR00013QuantityForPackage(allProperties, optimalPackageResult.selectedPackage);
    result.br00013Quantity = br00013Quantity;

    // Calculate bank statement quantity for full accounting
    if (isFullAccounting) {
        const bookingsSum = calculateDocumentSum(allProperties, DOCUMENT_FIELDS.BOOKING_OPERATIONS);
        const cashBankSum = calculateDocumentSum(allProperties, DOCUMENT_FIELDS.CASH_BANK_OPERATIONS);
        result.br00013BankStatementQuantity = bookingsSum > cashBankSum ? br00013Quantity : 0;
    } else {
        result.br00013BankStatementQuantity = 0;
    }

    return result;
}

/**
 * Recalculate e-commerce packages with new accounting type
 */
export async function recalculateEcommercePackages(result, allProperties, isFullAccounting, accessToken) {
    // Ensure result object exists
    if (!result) {
        result = {};
    }

    const transactionCount = parseAndValidateNumericField(allProperties, 'ile_transakcji_sprzedazy_w_miesiacu_');

    if (transactionCount > 0) {
        console.log('Recalculating e-commerce packages for', transactionCount, 'transactions with accounting type:', isFullAccounting ? 'full' : 'simplified');

        try {
            const optimalPackage = await calculateOptimalEcommercePackage(transactionCount, isFullAccounting, accessToken);

            // Add e-commerce package information to result
            result.ecommercePackage = optimalPackage;
            result.ecommerceRefreshed = true;
            result.ecommerceTransactionCount = transactionCount;

            console.log('E-commerce packages recalculated:', optimalPackage);
        } catch (error) {
            console.error('Error recalculating e-commerce packages:', error);
            throw new Error(`Failed to recalculate e-commerce packages: ${error.message}`);
        }
    } else {
        console.log('No e-commerce transactions - clearing e-commerce packages');
        result.ecommerceRefreshed = false;
        result.ecommerceTransactionCount = 0;
    }
}

/**
 * Recalculate PIT packages for simplified accounting
 */
async function recalculatePitPackages(result, allProperties, isFullAccounting, accessToken) {
    console.log('Recalculating PIT packages for accounting type:', isFullAccounting ? 'full' : 'simplified');

    // Only add PIT packages for simplified accounting
    if (isFullAccounting) {
        console.log('Full accounting detected - no PIT packages needed');
        result.pitPackages = [];
        result.pitRefreshed = false;
        return;
    }

    // Get the selected accounting package name from the result
    const selectedPackageName = result.selectedPackageName;

    if (!selectedPackageName) {
        console.log('No accounting package selected - no PIT package needed');
        result.pitPackages = [];
        result.pitRefreshed = false;
        return;
    }

    try {
        console.log('Selecting PIT package for accounting package:', selectedPackageName);
        const pitPackages = await selectPitPackageForSimplifiedAccounting(selectedPackageName, accessToken);

        result.pitPackages = pitPackages;
        result.pitRefreshed = true;
        result.selectedPitPackage = selectedPackageName;

        console.log('PIT packages recalculated:', pitPackages);
    } catch (error) {
        console.error('Error recalculating PIT packages:', error);
        result.pitRefreshError = error.message;
        result.pitPackages = [];
        result.pitRefreshed = false;
    }
}

/**
 * Recalculate financial statement packages for full accounting
 */
async function recalculateFinancialStatementPackages(result, allProperties, isFullAccounting, accessToken) {
    console.log('Recalculating financial statement packages for accounting type:', isFullAccounting ? 'full' : 'simplified');

    // Check if accounting services are active
    const uslugiDoWyceny = allProperties['uslugi_do_wyceny'] || '';
    const rodzajKsiegowosci = allProperties['rodzaj_ksiegowosci'] || '';
    const accountingServicesActive = uslugiDoWyceny.includes('Księgowość') && rodzajKsiegowosci.trim() !== '';

    if (!accountingServicesActive) {
        console.log('Accounting services not active - no financial statement packages needed (Księgowość not in uslugi_do_wyceny or rodzaj_ksiegowosci is empty)');
        result.financialStatementPackages = [];
        result.financialStatementRefreshed = false;
        return;
    }

    // Only add financial statement packages for full accounting
    if (!isFullAccounting) {
        console.log('Simplified accounting detected - no financial statement packages needed');
        result.financialStatementPackages = [];
        result.financialStatementRefreshed = false;
        return;
    }

    // Get the selected accounting package name from the result
    const selectedPackageName = result.selectedPackageName;

    if (!selectedPackageName) {
        console.log('No accounting package selected - no financial statement package needed');
        result.financialStatementPackages = [];
        result.financialStatementRefreshed = false;
        return;
    }

    try {
        console.log('Selecting financial statement package for accounting package:', selectedPackageName);
        const financialStatementPackages = await selectFinancialStatementPackageForFullAccounting(selectedPackageName, allProperties, accessToken);

        result.financialStatementPackages = financialStatementPackages;
        result.financialStatementRefreshed = true;
        result.selectedFinancialStatementPackage = selectedPackageName;

        console.log('Financial statement packages recalculated:', financialStatementPackages);
    } catch (error) {
        console.error('Error recalculating financial statement packages:', error);
        result.financialStatementRefreshError = error.message;
        result.financialStatementPackages = [];
        result.financialStatementRefreshed = false;
    }
}

/**
 * Recalculate VAT settings
 */
async function recalculateVatSettings(result, allProperties) {
    const vatStatus = allProperties['vat___status_podatnika'];

    // Validate that VAT status field exists
    if (vatStatus === null || vatStatus === undefined) {
        throw new Error('Missing required field: vat___status_podatnika');
    }

    console.log('Recalculating VAT settings for status:', vatStatus);

    // Check if accounting services are active
    const uslugiDoWyceny = allProperties['uslugi_do_wyceny'] || '';
    const rodzajKsiegowosci = allProperties['rodzaj_ksiegowosci'] || '';
    const accountingServicesActive = uslugiDoWyceny.includes('Księgowość') && rodzajKsiegowosci.trim() !== '';

    if (!accountingServicesActive) {
        console.log('Accounting services not active - no VAT line items needed (Księgowość not in uslugi_do_wyceny or rodzaj_ksiegowosci is empty)');
        result.shouldHaveBR00032 = false;
        result.shouldHaveBR00033 = false;
        result.br00032Quantity = 0;
        result.br00033Quantity = 0;
    } else {
        const vatResult = processVatStatus(vatStatus);
        mergeVatResult(result, vatResult);
    }

    console.log('VAT settings recalculated:', {
        shouldHaveBR00032: result.shouldHaveBR00032,
        shouldHaveBR00033: result.shouldHaveBR00033,
        br00032Quantity: result.br00032Quantity,
        br00033Quantity: result.br00033Quantity
    });
}

/**
 * Recalculate language settings
 */
async function recalculateLanguageSettings(result, allProperties) {
    const language = allProperties['jezyk_obslugi'];

    // Validate that language field exists
    if (language === null || language === undefined) {
        throw new Error('Missing required field: jezyk_obslugi');
    }

    console.log('Recalculating language settings for language:', language);

    const languageResult = processLanguageProperty(language);
    mergeLanguageResult(result, languageResult);

    console.log('Language settings recalculated:', {
        shouldHaveBR00129: result.shouldHaveBR00129
    });
}

/**
 * Recalculate payroll settings
 */
async function recalculatePayrollSettings(result, allProperties, dealId, accessToken) {
    const payrollPackage = allProperties['pakiet_kadrowo_placowy'];

    // Validate that payroll package field exists
    if (payrollPackage === null || payrollPackage === undefined) {
        throw new Error('Missing required field: pakiet_kadrowo_placowy');
    }

    console.log('Recalculating payroll settings for package:', payrollPackage);

    if (payrollPackage && payrollPackage.trim() !== '') {
        // Always fetch payroll prices from HubSpot - no fallbacks
        const { getPayrollPrices } = await import('./price-fetcher.js');
        if (!accessToken) {
            throw new Error('HubSpot access token not available - cannot fetch payroll prices');
        }
        const payrollPrices = await getPayrollPrices(accessToken);
        console.log('Fetched payroll prices for business logic:', payrollPrices);

        // Use the processPayrollPackage utility function with original properties
        // Pass dealId for detailed Ryczałt logging and payroll prices
        await processPayrollPackage(result, allProperties, dealId, payrollPrices);

        // Apply mobility package adjustments to line item quantities (not field values)
        if (result.adjustedUmowaOPrace !== undefined) {
            // Adjust BR00070 and BR00072 quantities (employment contracts)
            if (result.br00070Quantity > 0) {
                result.br00070Quantity = Math.max(0, result.br00070Quantity - parseAndValidateNumericField(allProperties, 'ile_jest_pakietow_mobilnosci_'));
            }
            if (result.br00072Quantity > 0) {
                result.br00072Quantity = Math.max(0, result.br00072Quantity - parseAndValidateNumericField(allProperties, 'ile_jest_pakietow_mobilnosci_'));
            }
            console.log(`Adjusted employment contract line items by mobility packages: BR00070=${result.br00070Quantity}, BR00072=${result.br00072Quantity}`);
        }

        if (result.adjustedUmowyCywilnoprawne !== undefined) {
            // Adjust BR00071 and BR00073 quantities (civil contracts)
            if (result.br00071Quantity > 0) {
                result.br00071Quantity = Math.max(0, result.br00071Quantity - parseAndValidateNumericField(allProperties, 'ucp___ile_jest_pakietow_mobilnosci_'));
            }
            if (result.br00073Quantity > 0) {
                result.br00073Quantity = Math.max(0, result.br00073Quantity - parseAndValidateNumericField(allProperties, 'ucp___ile_jest_pakietow_mobilnosci_'));
            }
            console.log(`Adjusted civil contract line items by mobility packages: BR00071=${result.br00071Quantity}, BR00073=${result.br00073Quantity}`);
        }

        console.log('Payroll settings recalculated with mobility package adjustments applied to line item quantities');
    } else {
        console.log('Empty payroll package - skipping payroll recalculation');
    }
}

/**
 * Recalculate MSP settings
 */
async function recalculateMspSettings(result, allProperties, accessToken) {
    const mspValue = allProperties['pytania_do_msp'];
    const accountingType = allProperties['rodzaj_ksiegowosci'];
    const isFullAccounting = result.isFullAccounting;

    // Validate required fields
    if (mspValue === null || mspValue === undefined) {
        throw new Error('Missing required field: pytania_do_msp');
    }
    if (accountingType === null || accountingType === undefined) {
        throw new Error('Missing required field: rodzaj_ksiegowosci');
    }

    console.log('Recalculating MSP settings for value:', mspValue);
    console.log('Accounting type:', accountingType);
    console.log('Is full accounting:', isFullAccounting);

    // Check if value contains "Biegły rewident?" - only apply for full accounting AND when accounting services are active
    const safePropertyValue = mspValue || '';

    // Check if accounting services are active (same logic used below for "Konta zespołu 5?")
    const uslugiDoWyceny = allProperties['uslugi_do_wyceny'] || '';
    const rodzajKsiegowosci = allProperties['rodzaj_ksiegowosci'] || '';
    const accountingServicesActive = uslugiDoWyceny.includes('Księgowość') && rodzajKsiegowosci.trim() !== '';

    if (isFullAccounting && accountingServicesActive) {
        result.shouldHaveBR00111 = safePropertyValue.includes('Biegły rewident?');
        console.log('BR00111 (MSP audit) needed for full accounting with active services:', result.shouldHaveBR00111);
    } else if (isFullAccounting && !accountingServicesActive) {
        result.shouldHaveBR00111 = false;
        console.log('BR00111 (MSP audit) disabled - accounting services not active (Księgowość not in uslugi_do_wyceny or rodzaj_ksiegowosci is empty)');
    } else {
        result.shouldHaveBR00111 = false;
        console.log('BR00111 (MSP audit) disabled for simplified accounting');
    }

    // Check if value contains "Konta zespołu 5?" for BR00013 calculation from base value - only apply for full accounting
    // Note: uslugiDoWyceny, rodzajKsiegowosci, and accountingServicesActive are already declared above

    if (isFullAccounting && safePropertyValue.includes('Konta zespołu 5?') && accountingServicesActive) {
        console.log('Konta zespołu 5? detected with active accounting services - calculating BR00013 from base value (not 50% increased rate)');

        // Calculate bookings sum using utility function
        const bookingsSum = calculateDocumentSum(allProperties, DOCUMENT_FIELDS.BOOKING_OPERATIONS);
        const cashBankSum = calculateDocumentSum(allProperties, DOCUMENT_FIELDS.CASH_BANK_OPERATIONS);

        // Calculate 50% markup for package selection only
        const br00013WithMarkup = Math.ceil(bookingsSum * 1.5);
        console.log(`MSP markup calculation for package selection: ${bookingsSum} documents * 1.5 = ${br00013WithMarkup}`);

        // IMPORTANT: Recalculate accounting packages based on the markup quantity for package selection
        console.log('Recalculating accounting packages due to MSP markup');
        const optimalPackageResult = await selectOptimalAccountingPackage(br00013WithMarkup, result.isFullAccounting, accessToken);
        console.log('Recalculated accounting packages for MSP markup:', optimalPackageResult);

        // Validate the recalculated package result
        if (!optimalPackageResult.items) {
            throw new Error('No accounting package items returned from MSP recalculation');
        }
        if (!optimalPackageResult.selectedPackage) {
            throw new Error('No selected package returned from MSP recalculation');
        }

        // Update the accounting packages and selected package name
        result.accountingPackages = optimalPackageResult.items;
        result.selectedPackageName = optimalPackageResult.selectedPackage;
        result.baseDocumentQuantity = bookingsSum; // Keep base document quantity as original value, not markup

        // Calculate BR00013 quantities using the new logic that includes employee counts
        const br00013Calculation = calculateBR00013DocumentQuantity(allProperties);
        const br00013DocumentQuantity = br00013Calculation.quantity;
        const newSelectedPackageName = result.selectedPackageName;
        result.br00013Quantity = calculateBR00013QuantityForPackage(br00013DocumentQuantity, newSelectedPackageName, 'with MSP base value for comprehensive update', result.isFullAccounting);

        console.log(`MSP: BR00013 calculation uses new logic with employee counts included: ${br00013DocumentQuantity} (chosen from sum ${br00013Calculation.chosenSum})`);

        // Recalculate BR00013 bank statement quantity if applicable (using base value)
        if (result.isFullAccounting && bookingsSum > cashBankSum) {
            result.br00013BankStatementQuantity = result.br00013Quantity;
        }

        console.log(`BR00013 quantities calculated from max(bookings, cash-bank) with MSP package selection:`);
        console.log(`- BR00013 document quantity: ${br00013DocumentQuantity} (bookings: ${bookingsSum}, cash-bank: ${cashBankSum})`);
        console.log(`- BR00013 quantity: ${result.br00013Quantity} (package selection based on markup: ${br00013WithMarkup})`);
        console.log(`- BR00013 bank statement quantity: ${result.br00013BankStatementQuantity || 0}`);
        console.log(`- Selected package: ${result.selectedPackageName}`);
        console.log(`- Accounting packages updated: ${result.accountingPackages.length} items`);
    } else if (isFullAccounting && safePropertyValue.includes('Konta zespołu 5?') && !accountingServicesActive) {
        console.log('Konta zespołu 5? detected but accounting services not active - no accounting packages will be added (Księgowość not in uslugi_do_wyceny or rodzaj_ksiegowosci is empty)');
        // Don't recalculate packages when accounting services are not active
    } else if (!isFullAccounting && safePropertyValue.includes('Konta zespołu 5?')) {
        console.log('Konta zespołu 5? detected but ignored for simplified accounting');
    }

    console.log('MSP settings recalculated');
}

/**
 * Recalculate quantity-based settings
 */
async function recalculateQuantityBasedSettings(result, allProperties) {
    console.log('Recalculating quantity-based settings');

    // Check if accounting services are active
    const uslugiDoWyceny = allProperties['uslugi_do_wyceny'] || '';
    const rodzajKsiegowosci = allProperties['rodzaj_ksiegowosci'] || '';
    const accountingServicesActive = uslugiDoWyceny.includes('Księgowość') && rodzajKsiegowosci.trim() !== '';
    const fullAccountingServicesActive = uslugiDoWyceny.includes('Księgowość') && isFullAccountingType(rodzajKsiegowosci);

    // Handle fixed assets (BR00030) - only for full accounting types
    if (!fullAccountingServicesActive) {
        console.log('BR00030 (fixed assets) not added - requires full accounting type (Pełna księgowość, Fundacje rodzinne, or Fundacje non profit i NGO)');
        result.br00030Quantity = 0;
    } else {
        const fixedAssets = parseAndValidateNumericField(allProperties, 'srodki_trwale_i_wartosci_niematerialne_i_prawne___ile_');
        result.br00030Quantity = fixedAssets;
    }

    // Handle cash registers (BR00031) - available for any accounting services
    if (!accountingServicesActive) {
        console.log('Accounting services not active - no cash registers line item needed (Księgowość not in uslugi_do_wyceny or rodzaj_ksiegowosci is empty)');
        result.br00031Quantity = 0;
    } else {
        const cashRegisters = parseAndValidateNumericField(allProperties, 'kasy_fiskalne___ile_');
        result.br00031Quantity = cashRegisters;
    }

    // Handle A1 certificates (boolean-based but numeric quantity) - this is not accounting-specific
    // BUT exclude for Ryczałt packages and when no payroll package is selected
    const a1Certificates = parseAndValidateNumericField(allProperties, 'a1___czy_wystepuja_');
    const payrollPackage = allProperties['pakiet_kadrowo_placowy'];
    if (payrollPackage && payrollPackage !== 'Ryczałt') {
        result.br00165Quantity = a1Certificates;
    } else {
        result.br00165Quantity = 0; // Don't add BR00165 when no payroll package or for Ryczałt
        if (payrollPackage === 'Ryczałt') {
            console.log('BR00165 (A1) not added for Ryczałt package - included in BR00069');
        } else {
            console.log('BR00165 (A1) not added - no payroll package selected');
        }
    }

    // Handle mobility packages (will be set by mobility settings calculation)
    // For Ryczałt package, mobility packages are included in BR00069, so don't add them as separate line items
    // Also exclude when no payroll package is selected
    // Reuse the payrollPackage variable declared above
    if (payrollPackage === 'Ryczałt') {
        result.mobilityPackageQuantity = 0; // Don't add BR00076 as separate line item for Ryczałt
        console.log('Mobility packages set to 0 for Ryczałt package (included in BR00069)');
    } else if (!payrollPackage) {
        result.mobilityPackageQuantity = 0; // Don't add BR00076 when no payroll package selected
        console.log('Mobility packages set to 0 - no payroll package selected');
    } else {
        result.mobilityPackageQuantity = result.totalMobilityPackages || 0;
    }

    console.log('Quantity-based settings recalculated:', {
        br00030Quantity: result.br00030Quantity,
        br00031Quantity: result.br00031Quantity,
        br00165Quantity: result.br00165Quantity,
        mobilityPackageQuantity: result.mobilityPackageQuantity,
        payrollPackage: payrollPackage
    });
}

/**
 * Recalculate mobility package settings and adjust employee counts
 */
async function recalculateMobilitySettings(result, allProperties) {
    console.log('Recalculating mobility settings');

    const mobilityPackages = parseAndValidateNumericField(allProperties, 'ile_jest_pakietow_mobilnosci_');
    const ucpMobilityPackages = parseAndValidateNumericField(allProperties, 'ucp___ile_jest_pakietow_mobilnosci_');
    const currentUmowaOPrace = parseAndValidateNumericField(allProperties, 'umowa_o_prace___liczba_osob');
    const currentUmowyCywilnoprawne = parseAndValidateNumericField(allProperties, 'umowy_cywilnoprawne___liczba_pracownikow');

    // Calculate total mobility packages (sum of both fields)
    const totalMobilityPackages = mobilityPackages + ucpMobilityPackages;

    // Calculate adjusted employee counts (subtract specific mobility packages)
    // umowa_o_prace___liczba_osob minus ile_jest_pakietow_mobilnosci_
    const adjustedUmowaOPrace = Math.max(0, currentUmowaOPrace - mobilityPackages);
    // umowy_cywilnoprawne___liczba_pracownikow minus ucp___ile_jest_pakietow_mobilnosci_
    const adjustedUmowyCywilnoprawne = Math.max(0, currentUmowyCywilnoprawne - ucpMobilityPackages);

    // Store the adjusted values and total mobility packages for later use
    result.adjustedUmowaOPrace = adjustedUmowaOPrace;
    result.adjustedUmowyCywilnoprawne = adjustedUmowyCywilnoprawne;
    result.totalMobilityPackages = totalMobilityPackages;

    console.log('Mobility settings calculated:', {
        mobilityPackages,
        ucpMobilityPackages,
        totalMobilityPackages,
        originalUmowaOPrace: currentUmowaOPrace,
        originalUmowyCywilnoprawne: currentUmowyCywilnoprawne,
        adjustedUmowaOPrace,
        adjustedUmowyCywilnoprawne
    });
}

/**
 * Recalculate banking settings
 */
async function recalculateBankingSettings(result, allProperties, isFullAccounting) {
    console.log('Recalculating banking settings');

    // Check if accounting services are active
    const uslugiDoWyceny = allProperties['uslugi_do_wyceny'] || '';
    const rodzajKsiegowosci = allProperties['rodzaj_ksiegowosci'] || '';
    const accountingServicesActive = uslugiDoWyceny.includes('Księgowość') && rodzajKsiegowosci.trim() !== '';

    // Handle daily bank reports (BR00130) - only for full accounting types
    const dailyReports = parseAndValidateNumericField(allProperties, 'ilosc_kont_bankowych___raporty_dzienne');
    const fullAccountingServicesActive = uslugiDoWyceny.includes('Księgowość') && isFullAccountingType(rodzajKsiegowosci);
    if (fullAccountingServicesActive) {
        result.br00130Quantity = dailyReports;
    } else {
        result.br00130Quantity = 0;
        console.log('BR00130 (daily bank reports) not added - requires full accounting type (Pełna księgowość, Fundacje rodzinne, or Fundacje non profit i NGO)');
    }

    // Handle monthly bank reports and payment channels (both use BR00012)
    let monthlyReports = parseAndValidateNumericField(allProperties, 'ilosc_kont_bankowych___raporty_miesieczne');
    const paymentChannels = parseAndValidateNumericField(allProperties, 'ile_kanalow_platniczych_');

    // kpKwBankiLineItemQuantity is already calculated earlier in the process
    const kpKwBankiLineItemQuantity = result.kpKwBankiLineItemQuantity;
    const currentKpKwBanki = parseAndValidateNumericField(allProperties, 'kp_kw___banki_');
    const umowaOPraceOsob = parseAndValidateNumericField(allProperties, 'umowa_o_prace___liczba_osob');
    const umowyCywilnoprawneOsob = parseAndValidateNumericField(allProperties, 'umowy_cywilnoprawne___liczba_pracownikow');

    if (isFullAccounting && accountingServicesActive) {
        // Ensure monthly reports is at least 1 when full accounting is present AND accounting services are active
        monthlyReports = Math.max(monthlyReports, 1);
        // Sum monthly reports with payment channels when full accounting is present
        result.br00012Quantity = monthlyReports + paymentChannels;
        console.log(`Full accounting with active services: BR00012 = ${monthlyReports} (monthly reports, min 1) + ${paymentChannels} (payment channels) = ${result.br00012Quantity}`);
    } else {
        // BR00012 should only be added for full accounting types
        result.br00012Quantity = 0;
        if (!isFullAccounting && accountingServicesActive) {
            console.log(`Simplified accounting or empty accounting type: BR00012 = 0 (requires full accounting type: Pełna księgowość, Fundacje rodzinne, or Fundacje non profit i NGO)`);
        } else if (!accountingServicesActive) {
            console.log(`Accounting services not active: BR00012 = 0 (no banking line items when accounting is removed)`);
        }
    }

    // Check if employee counts should be added to banking calculations (same logic as BR00013)
    // Note: rodzajKsiegowosci and uslugiDoWyceny are already declared above
    const shouldAddEmployeesToBanking = isFullAccountingType(rodzajKsiegowosci) && uslugiDoWyceny.includes('Księgowość');
    const employeeCountsForBanking = shouldAddEmployeesToBanking ? (umowaOPraceOsob + umowyCywilnoprawneOsob) : 0;

    console.log('Banking settings recalculated:', {
        br00130Quantity: result.br00130Quantity,
        br00012Quantity: result.br00012Quantity,
        kpKwBankiFieldValue: currentKpKwBanki,
        kpKwBankiLineItemQuantity: kpKwBankiLineItemQuantity,
        employeeCountsAdded: employeeCountsForBanking,
        shouldAddEmployees: shouldAddEmployeesToBanking
    });
}

/**
 * Handle line item removal when services or industries are removed (but preserve field values)
 * @param {string} dealId - Deal ID
 * @param {string} accessToken - HubSpot access token
 * @param {Object} allProperties - All deal properties
 */
async function handleLineItemClearing(dealId, accessToken, allProperties) {
    console.log('Checking for line item clearing requirements (field values will be preserved)');

    const { getDealLineItemsWithDetails } = await import('./hubspot-api.js');
    const { deleteLineItem } = await import('./line-item-manager.js');

    // Check if "Kadry" was removed from "uslugi_do_wyceny"
    const uslugiDoWyceny = allProperties['uslugi_do_wyceny'] || '';
    if (!uslugiDoWyceny.includes('Kadry')) {
        console.log('Kadry not found in uslugi_do_wyceny - removing payroll-related line items (field values preserved)');

        // Note: Field values are preserved - only line items are removed

        // Remove ALL existing payroll line items
        console.log('Removing all existing payroll line items due to Kadry service removal');
        try {
            const currentLineItems = await getDealLineItemsWithDetails(dealId, accessToken);
            const payrollSkus = [
                'BR00070', 'BR00071', 'BR00072', 'BR00073', 'BR00074', 'BR00075',
                'BR00069', 'BR00077', 'BR00078', 'BR00079', 'BR00165',
                'BR00114', 'BR00115', 'BR00117', 'BR00118', 'BR00119', 'BR00081',
                'BR00076' // Mobility packages (tied to payroll services)
            ];

            const payrollLineItems = currentLineItems.filter(item => {
                const sku = item.properties.hs_sku || item.properties.name || '';
                return payrollSkus.includes(sku);
            });

            console.log(`Found ${payrollLineItems.length} payroll line items to remove`);
            for (const item of payrollLineItems) {
                const sku = item.properties.hs_sku || item.properties.name || '';
                console.log(`Removing payroll line item: ${sku} (ID: ${item.id})`);
                await deleteLineItem(item.id, accessToken);
            }
            console.log('All payroll line items removed successfully');
        } catch (error) {
            console.error('Error removing payroll line items:', error);
            // Don't fail the entire process if line item removal fails
        }
    }

    // Check if "Księgowość" was removed from "uslugi_do_wyceny" or "rodzaj_ksiegowosci" is empty
    const rodzajKsiegowosci = allProperties['rodzaj_ksiegowosci'] || '';
    if (!uslugiDoWyceny.includes('Księgowość') || rodzajKsiegowosci.trim() === '') {
        console.log('Księgowość not found in uslugi_do_wyceny or rodzaj_ksiegowosci is empty - removing accounting-related line items (field values preserved)');

        // Note: Field values are preserved - only line items are removed

        // Remove ALL existing accounting-related line items
        console.log('Removing all existing accounting-related line items due to Księgowość service removal or empty rodzaj_ksiegowosci');
        try {
            const currentLineItems = await getDealLineItemsWithDetails(dealId, accessToken);
            const accountingSkus = [
                // Document-based line items
                'BR00013', // Bank statement processing
                'BR00030', // Fixed assets
                'BR00031', // Cash registers
                'BR00032', // VAT EU/8/9M
                'BR00033', // VAT OSS
                'BR00012', // Monthly bank reports and payment channels
                'BR00130', // Daily bank reports
                'BR00076', // Mobility packages
                'BR00111', // MSP audit
                // Accounting packages
                'BR00003', 'BR00004', 'BR00005', 'BR00006', // Full accounting packages
                'BR00007', 'BR00008', 'BR00009', 'BR00010', // Simplified accounting packages
                'BR00015', 'BR00016', 'BR00017', 'BR00018', // Simplified individual documents
                'BR00019', 'BR00020', 'BR00021', // Simplified additional packs
                'BR00022', 'BR00023', 'BR00024', 'BR00025', // Full individual documents
                'BR00027', 'BR00028', 'BR00029', // Full additional packs
                // Financial statement packages
                'BR00099', 'BR00100', 'BR00101', 'BR00102',
            ];

            const accountingLineItems = currentLineItems.filter(item => {
                const sku = item.properties.hs_sku || item.properties.name || '';
                return accountingSkus.includes(sku);
            });

            console.log(`Found ${accountingLineItems.length} accounting line items to remove`);
            for (const item of accountingLineItems) {
                const sku = item.properties.hs_sku || item.properties.name || '';
                console.log(`Removing accounting line item: ${sku} (ID: ${item.id})`);
                await deleteLineItem(item.id, accessToken);
            }
            console.log('All accounting line items removed successfully');
        } catch (error) {
            console.error('Error removing accounting line items:', error);
            // Don't fail the entire process if line item removal fails
        }
    }

    // Check if "E-commerce" was removed from "branza"
    const branza = allProperties['branza'] || '';
    if (!branza.includes('E-commerce')) {
        console.log('E-commerce not found in branza - removing e-commerce-related line items (field values preserved)');

        // Note: Field values are preserved - only line items are removed

        // Remove existing e-commerce line items
        const existingLineItems = await getDealLineItemsWithDetails(dealId, accessToken);
        const ecommerceSkus = [
            'BR00160', 'BR00161', 'BR00162', 'BR00163', 'BR00164', // E-commerce packages
            'BR00166', 'BR00167', 'BR00168', 'BR00169', 'BR00170', 'BR00171', 'BR00172', 'BR00173' // Additional transactions
        ];

        // Safety check: ensure existingLineItems is an array
        if (!Array.isArray(existingLineItems)) {
            console.log('Warning: existingLineItems is not an array, skipping e-commerce line item clearing');
            return;
        }

        for (const sku of ecommerceSkus) {
            const existingItem = existingLineItems.find(item => {
                const itemSku = item.properties.hs_sku || item.properties.name || '';
                return itemSku === sku;
            });
            if (existingItem) {
                console.log(`Removing e-commerce line item: ${sku} (${existingItem.id})`);
                await deleteLineItem(existingItem.id, accessToken);
            }
        }
    }

    // Check if accounting type changed from "Pełna księgowość" to simplified accounting
    const accountingType = allProperties['rodzaj_ksiegowosci'] || '';
    const isCurrentlyFullAccounting = isFullAccountingType(accountingType);

    if (!isCurrentlyFullAccounting && accountingType.includes('uproszczona')) {
        console.log('Accounting type changed to simplified - removing full accounting specific line items (field values preserved)');

        // Note: Field values are preserved - only line items are removed


    }

    // Note: Field values are preserved when services are disabled - only line items are removed
    console.log('Line item clearing completed - field values preserved');
}

/**
 * Handle field clearing when services are removed or conditions are not met
 * @param {string} dealId - Deal ID
 * @param {string} accessToken - HubSpot access token
 * @param {Object} allProperties - All deal properties
 */
async function handleFieldClearing(dealId, accessToken, allProperties) {
    console.log('Checking for field clearing requirements');

    const uslugiDoWyceny = allProperties['uslugi_do_wyceny'] || '';
    const rodzajKsiegowosci = allProperties['rodzaj_ksiegowosci'] || '';
    const branza = allProperties['branza'] || '';

    const fieldsToUpdate = {};

    // Check if accounting services should be cleared
    const hasAccountingServices = uslugiDoWyceny.includes('Księgowość');
    const hasAccountingType = rodzajKsiegowosci && rodzajKsiegowosci.trim() !== '';

    // Only clear accounting fields if accounting services are completely removed (not just simplified)
    if (!hasAccountingServices || !hasAccountingType) {
        console.log('Accounting services completely removed - clearing accounting fields');

        // Clear accounting-related fields
        fieldsToUpdate['faktury_rachunki_sprzedazowe___ile_'] = '';
        fieldsToUpdate['faktury_rachunki_zakupu___ile_'] = '';
        fieldsToUpdate['faktury_walutowe___ile_miesiecznie_'] = '';
        fieldsToUpdate['dokumenty_wewnetrzne_wdt__wnt_itp'] = '';
        fieldsToUpdate['operacje_kp_kw_walutowe'] = '';
        fieldsToUpdate['kp_kw___banki_'] = '';
        fieldsToUpdate['kp_kw_gotowka'] = '';
        fieldsToUpdate['kasy_fiskalne___ile_'] = '';
        fieldsToUpdate['vat___status_podatnika'] = '';
        fieldsToUpdate['srodki_trwale_i_wartosci_niematerialne_i_prawne___ile_'] = '';
        fieldsToUpdate['pytania_do_msp'] = '';
        fieldsToUpdate['ilosc_kont_bankowych___raporty_dzienne'] = '';
        fieldsToUpdate['ilosc_kont_bankowych___raporty_miesieczne'] = '';
        fieldsToUpdate['wyciagi_bankowe___liczba_'] = '';
        fieldsToUpdate['ile_kanalow_platniczych_'] = '';
    }

    // Check if payroll services should be cleared
    if (!uslugiDoWyceny.includes('Kadry')) {
        console.log('Payroll services not active - clearing payroll fields');

        // Clear payroll-related fields
        fieldsToUpdate['pakiet_kadrowo_placowy'] = '';
        fieldsToUpdate['umowa_o_prace___liczba_osob'] = '';
        fieldsToUpdate['umowy_cywilnoprawne___liczba_pracownikow'] = '';
        fieldsToUpdate['ppk___ile_osob_'] = '';
        fieldsToUpdate['pfron___ile_osob_'] = '';
        fieldsToUpdate['a1___czy_wystepuja_'] = '';
        fieldsToUpdate['kto_robi_import_do_moje_ppk'] = '';
        fieldsToUpdate['dodatkowe_skladniki_wynagrodzenia'] = '';
        fieldsToUpdate['ile_jest_pakietow_mobilnosci_'] = '';
        fieldsToUpdate['ucp___ile_jest_pakietow_mobilnosci_'] = '';
    }

    // Check if e-commerce services should be cleared
    if (!branza.includes('E-commerce')) {
        console.log('E-commerce services not active - clearing e-commerce fields');

        // Clear e-commerce related fields
        fieldsToUpdate['ile_transakcji_sprzedazy_w_miesiacu_'] = '';
        fieldsToUpdate['ile_kanalow_platniczych_'] = '';
    }

    // Update fields if any need to be cleared
    if (Object.keys(fieldsToUpdate).length > 0) {
        console.log('Clearing fields:', Object.keys(fieldsToUpdate));
        await updateDealProperties(dealId, accessToken, fieldsToUpdate);

        // Also update the allProperties object so business logic uses cleared values
        for (const [fieldName, value] of Object.entries(fieldsToUpdate)) {
            allProperties[fieldName] = value;
        }

        console.log('Field clearing completed');
    } else {
        console.log('No fields need to be cleared');
    }
}
