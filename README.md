# sv

Everything you need to build a Svelte project, powered by [`sv`](https://github.com/sveltejs/cli).

## Creating a project

If you're seeing this, you've probably already done this step. Congrats!

```bash
# create a new project in the current directory
npx sv create

# create a new project in my-app
npx sv create my-app
```

## Developing

Once you've created a project and installed dependencies with `npm install` (or `pnpm install` or `yarn`), start a development server:

```bash
npm run dev

# or start the server and open the app in a new browser tab
npm run dev -- --open
```

## Building

To create a production version of your app:

```bash
npm run build
```

You can preview the production build with `npm run preview`.

## Deployment

This application uses Playwright for web scraping functionality. The deployment is configured to handle browser installation at runtime rather than during the build process to avoid GLIBC compatibility issues.

### Build Scripts

- `npm run build` - Standard build with tests (no browser installation)
- `npm run build:no-tests` - Build without running tests
- `npm run build:with-browsers` - Build with browser installation (for local development)

### Production Startup

The application includes a startup script that handles browser initialization:

```bash
npm start
```

This will:
1. Run the startup script to initialize browsers
2. Start the production server

### Health Check

Check application health and browser availability:

```
GET /api/health
```

Returns:
- `200` - All services including browsers are available
- `503` - API is available but browsers are not ready
- `500` - Error occurred during health check

### Environment Variables

The application will work without additional environment variables, but you may want to set:

- `PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=0` - Ensure browsers are downloaded when needed
- `NODE_ENV=production` - Set production mode

> To deploy your app, you may need to install an [adapter](https://svelte.dev/docs/kit/adapters) for your target environment.
