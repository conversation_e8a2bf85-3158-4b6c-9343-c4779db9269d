import { describe, test, expect, beforeEach, vi } from 'vitest';

/**
 * Unit Tests for HubSpot API Module
 * 
 * Tests only the hubspot-api.js module functions in isolation.
 * All external dependencies are mocked.
 */

// Mock global fetch
global.fetch = vi.fn();

import {
  getDealProperties,
  updateDealProperties,
  findProductBySku,
  getDealLineItemsWithDetails,
  updateDealAmount
} from '../../src/lib/hubspot-api.js';

describe('HubSpot API', () => {
  const mockAccessToken = 'test-token-1234567890';
  const mockDealId = '123456789';

  beforeEach(() => {
    vi.clearAllMocks();
    fetch.mockClear();
    // Mock console methods to avoid noise in tests
    vi.spyOn(console, 'log').mockImplementation(() => {});
    vi.spyOn(console, 'error').mockImplementation(() => {});
  });

  describe('getDealProperties', () => {
    test('should fetch deal properties successfully', async () => {
      const mockResponse = {
        properties: {
          'test_property': 'test_value'
        }
      };

      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse
      });

      const properties = ['test_property', 'another_property'];
      const result = await getDealProperties(mockDealId, mockAccessToken, properties);

      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining(`/deals/${mockDealId}?properties=test_property,another_property`),
        expect.objectContaining({
          headers: expect.objectContaining({
            'Authorization': `Bearer ${mockAccessToken}`
          })
        })
      );
      expect(result).toEqual(mockResponse.properties);
    });

    test('should handle API errors gracefully', async () => {
      fetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        statusText: 'Not Found'
      });

      const properties = ['test_property'];

      await expect(getDealProperties(mockDealId, mockAccessToken, properties))
        .rejects.toThrow('Failed to fetch deal properties: Not Found');
    });

    test('should handle network errors gracefully', async () => {
      fetch.mockRejectedValueOnce(new Error('Network error'));

      const properties = ['test_property'];

      await expect(getDealProperties(mockDealId, mockAccessToken, properties))
        .rejects.toThrow('Network error');
    });
  });

  describe('updateDealProperties', () => {
    test('should update deal properties successfully', async () => {
      const mockProperties = {
        'test_property': 'updated_value'
      };

      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ id: mockDealId })
      });

      const result = await updateDealProperties(mockDealId, mockAccessToken, mockProperties);

      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining(`/deals/${mockDealId}`),
        expect.objectContaining({
          method: 'PATCH',
          headers: expect.objectContaining({
            'Authorization': `Bearer ${mockAccessToken}`,
            'Content-Type': 'application/json'
          }),
          body: JSON.stringify({ properties: mockProperties })
        })
      );
      expect(result).toEqual({ id: mockDealId });
    });

    test('should handle update errors gracefully', async () => {
      fetch.mockResolvedValueOnce({
        ok: false,
        status: 400,
        statusText: 'Bad Request',
        text: async () => 'Invalid data'
      });

      await expect(updateDealProperties(mockDealId, mockAccessToken, {}))
        .rejects.toThrow('Failed to update deal properties: Bad Request - Invalid data');
    });
  });

  describe('updateDealAmount', () => {
    test('should update deal amount successfully', async () => {
      // Mock the associations call
      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ results: [{ toObjectId: 'line-item-1' }] })
      });

      // Mock the batch read call
      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          results: [{
            id: 'line-item-1',
            properties: {
              amount: '100',
              quantity: '1',
              price: '100',
              hs_sku: 'BR00001'
            }
          }]
        })
      });

      // Mock the deal update call
      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ id: mockDealId, properties: { amount: '100' } })
      });

      const result = await updateDealAmount(mockDealId, mockAccessToken);

      expect(result).toBeDefined();
    });
  });

  describe('findProductBySku', () => {
    test('should find product by SKU successfully', async () => {
      const mockProduct = {
        id: 'product-123',
        properties: {
          sku: 'BR00001',
          name: 'Test Product',
          price: '100'
        }
      };

      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ results: [mockProduct] })
      });

      const result = await findProductBySku('BR00001', mockAccessToken);

      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('/products/search'),
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Authorization': `Bearer ${mockAccessToken}`,
            'Content-Type': 'application/json'
          })
        })
      );
      expect(result).toEqual(mockProduct);
    });

    test('should throw error when product not found', async () => {
      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ results: [] })
      });

      await expect(findProductBySku('NONEXISTENT', mockAccessToken))
        .rejects.toThrow('Failed to search for product with SKU NONEXISTENT: No product found with SKU: NONEXISTENT');
    });
  });



  describe('getDealLineItemsWithDetails', () => {
    test('should fetch deal line items with details successfully', async () => {
      // Clear any previous mocks
      fetch.mockClear();

      // Mock the associations call
      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          results: [
            { toObjectId: 'line-item-1' },
            { toObjectId: 'line-item-2' }
          ]
        })
      });

      // Mock the batch read call
      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          results: [
            {
              id: 'line-item-1',
              properties: { sku: 'BR00001', quantity: '1', price: '100' }
            },
            {
              id: 'line-item-2',
              properties: { sku: 'BR00002', quantity: '2', price: '200' }
            }
          ]
        })
      });

      const result = await getDealLineItemsWithDetails(mockDealId, mockAccessToken);

      expect(result).toHaveLength(2);
      expect(result[0]).toEqual({
        id: 'line-item-1',
        properties: { sku: 'BR00001', quantity: '1', price: '100' }
      });
    });

    test('should return empty array when no line items exist', async () => {
      // Clear any previous mocks
      fetch.mockClear();

      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ results: [] })
      });

      const result = await getDealLineItemsWithDetails(mockDealId, mockAccessToken);

      expect(result).toEqual([]);
    });
  });
});
