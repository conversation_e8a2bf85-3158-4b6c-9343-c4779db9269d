# Asana Complete Subtasks API

This endpoint allows you to complete all subtasks of a main Asana task. It's designed to work with Make platform webhooks.

## Endpoint

```
POST /api/asana/complete-subtasks
```

## Authentication

The endpoint requires an API key passed in the `x-api-key` header:

```bash
curl -X POST http://localhost:5173/api/asana/complete-subtasks \
  -H "Content-Type: application/json" \
  -H "x-api-key: your-api-key" \
  -d '...'
```

## Configuration

Add your Asana Personal Access Token to the `.env` file:

```env
ASANA_ACCESS_TOKEN=your-asana-personal-access-token-here
```

You can get your Asana Personal Access Token from:
1. Go to Asana → Profile Settings → Apps → Manage Developer Apps
2. Create a new Personal Access Token
3. Copy the token and add it to your `.env` file

## Request Format

The endpoint accepts the exact payload format sent by Make platform. It can handle both:

### Array Format (from Make platform)
```json
[
    {
        "gid": "1210845766069034",
        "name": "Main Task Name",
        "workspace": {
            "gid": "1201224404925575",
            "name": "taxcoach.pl"
        },
        "completed": true,
        "projects": [
            {
                "gid": "1210845762582760",
                "name": "test"
            }
        ]
    }
]
```

### Single Object Format
```json
{
    "gid": "1210845766069034",
    "name": "Main Task Name",
    "workspace": {
        "gid": "1201224404925575",
        "name": "taxcoach.pl"
    }
}
```

## Required Fields

- **`gid`**: The main task ID (required)
- **`workspace.gid`**: The workspace ID (optional but recommended)

## Response Format

### Success Response
```json
{
    "success": true,
    "message": "Successfully completed all 3 subtasks",
    "data": {
        "mainTask": {
            "gid": "1210845766069034",
            "name": "Main Task Name",
            "completed": false,
            "permalink_url": "https://app.asana.com/..."
        },
        "subtasks": {
            "total": 3,
            "completed": 3,
            "failed": 0
        },
        "completedSubtasks": [
            {
                "gid": "1210845766069035",
                "name": "Subtask 1",
                "status": "completed"
            },
            {
                "gid": "1210845766069036",
                "name": "Subtask 2",
                "status": "already_completed"
            }
        ]
    },
    "consoleLogs": [
        "[LOG] Starting to complete all subtasks for main task: 1210845766069034",
        "[LOG] Found 3 subtasks",
        "[LOG] Successfully completed subtask: 1210845766069035"
    ]
}
```

### Error Response
```json
{
    "success": false,
    "error": "Task not found in Asana",
    "consoleLogs": [
        "[ERROR] Failed to retrieve main task details: Not Found"
    ]
}
```

## How It Works

1. **Validates** the API key and request format
2. **Extracts** the main task GID from the payload
3. **Fetches** task details to verify the task exists
4. **Retrieves** all subtasks of the main task
5. **Completes** each subtask that isn't already completed
6. **Returns** a detailed response with completion status

## Error Handling

The endpoint handles various error scenarios:

- **401 Unauthorized**: Invalid or missing API key
- **400 Bad Request**: Invalid JSON or missing required fields
- **404 Not Found**: Task not found in Asana
- **503 Service Unavailable**: Failed to connect to Asana API
- **500 Internal Server Error**: Other unexpected errors

## Testing

Run the integration tests:

```bash
npm run test:integration tests/integration/asana-complete-subtasks.test.js
```

Note: Some tests require a valid Asana access token to run fully.

## Make Platform Integration

To use with Make platform:

1. Set up a webhook trigger in Make
2. Configure the webhook to send task data when a task is completed
3. Add an HTTP module that calls this endpoint
4. Use the exact payload format that Make sends

The endpoint is designed to handle the exact format that Make platform sends, including the array wrapper around the task data.

## Logging

The endpoint captures all console logs and includes them in the response for debugging purposes. This helps track the completion process and identify any issues.
