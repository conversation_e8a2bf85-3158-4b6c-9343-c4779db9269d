/**
 * Result structure utilities for standardizing handler return objects
 */

/**
 * Create a base result object with default values
 * @returns {Object} Base result object
 */
export function createBaseResult() {
    return {
        shouldHaveBR00032: false,
        shouldHaveBR00033: false,
        br00032Quantity: 0,
        br00033Quantity: 0,
        shouldHaveBR00129: false
    };
}

/**
 * Create an extended result object for quantity-based properties
 * @returns {Object} Extended result object for quantity-based properties
 */
export function createQuantityBasedResult() {
    return {
        ...createBaseResult(),
        br00030Quantity: 0,
        br00031Quantity: 0,
        br00077Quantity: 0,
        br00080Quantity: 0,
        shouldHaveBR00078: false
    };
}

/**
 * Create an extended result object for numeric-based properties
 * @returns {Object} Extended result object for numeric-based properties
 */
export function createNumericBasedResult() {
    return {
        ...createBaseResult(),
        br00030Quantity: 0,
        br00031Quantity: 0,
        br00077Quantity: 0,
        br00080Quantity: 0,
        shouldHaveBR00078: false,
        numericValue: 0
    };
}

/**
 * Create an extended result object for boolean-based properties
 * @returns {Object} Extended result object for boolean-based properties
 */
export function createBooleanBasedResult() {
    return {
        ...createBaseResult(),
        br00165Quantity: 0
    };
}

/**
 * Create an extended result object for accounting properties
 * @returns {Object} Extended result object for accounting properties
 */
export function createAccountingResult() {
    return {
        ...createBaseResult(),
        // Accounting-specific properties
        br00013Quantity: 0,
        br00013BankStatementQuantity: 0,
        accountingPackages: null,
        isFullAccounting: false,
        selectedPackageName: 'BASE', // Track which package was selected
        // E-commerce properties (for complete recalculation)
        selectedEcommercePackage: null,
        ecommercePackageQuantity: 1,
        additionalTransactions: 0,
        totalEcommerceCost: 0,
        additionalTransactionSku: null,
        additionalTransactionQuantity: 0,
        ecommerceRefreshed: false,
        ecommerceTransactionCount: 0,
        // Payroll properties (for complete recalculation)
        br00069Quantity: 0,
        br00070Quantity: 0,
        br00071Quantity: 0,
        br00072Quantity: 0,
        br00073Quantity: 0,
        br00074Quantity: 0,
        br00075Quantity: 0,
        br00077Quantity: 0,
        br00080Quantity: 0,
        br00165Quantity: 0,
        shouldHaveBR00078: false,
        shouldHaveBR00079: false,
        // New KADRY-related properties
        br00114Quantity: 0,
        shouldHaveBR00115: false,
        shouldHaveBR00117: false,
        shouldHaveBR00118: false,
        shouldHaveBR00119: false,
        shouldHaveBR00081: false,
        // MSP properties (for complete recalculation)
        shouldHaveBR00111: false,
        // Quantity-based properties (for complete recalculation)
        br00030Quantity: 0,
        br00031Quantity: 0,
        // Banking properties (for complete recalculation)
        br00012Quantity: 0,
        br00130Quantity: 0,
        // PIT packages (for simplified accounting)
        pitPackages: [],
        pitRefreshed: false,
        selectedPitPackage: null,
        pitRefreshError: null,
        // Financial statement packages (for full accounting)
        financialStatementPackages: [],
        financialStatementRefreshed: false,
        selectedFinancialStatementPackage: null,
        financialStatementRefreshError: null,
        // Recalculation flags
        completeRecalculationPerformed: false
    };
}

/**
 * Create an extended result object for MSP properties
 * @returns {Object} Extended result object for MSP properties
 */
export function createMspResult() {
    return {
        ...createBaseResult(),
        shouldHaveBR00111: false,
        br00013Quantity: 0
    };
}

/**
 * Create an extended result object for payroll properties
 * @returns {Object} Extended result object for payroll properties
 */
export function createPayrollResult() {
    return {
        ...createBaseResult(),
        br00069Quantity: 0,
        br00070Quantity: 0,
        br00071Quantity: 0,
        br00072Quantity: 0,
        br00073Quantity: 0,
        br00074Quantity: 0,
        br00075Quantity: 0,
        br00077Quantity: 0,
        br00080Quantity: 0,
        br00165Quantity: 0,
        shouldHaveBR00078: false,
        shouldHaveBR00079: false,
        // New KADRY-related properties
        br00114Quantity: 0,
        shouldHaveBR00115: false,
        shouldHaveBR00117: false,
        shouldHaveBR00118: false,
        shouldHaveBR00119: false,
        shouldHaveBR00081: false
    };
}

/**
 * Create an extended result object for banking properties
 * @returns {Object} Extended result object for banking properties
 */
export function createBankingResult() {
    return {
        ...createBaseResult(),
        br00012Quantity: 0,
        br00130Quantity: 0
    };
}

/**
 * Create an extended result object for e-commerce properties
 * @returns {Object} Extended result object for e-commerce properties
 */
export function createEcommerceResult() {
    return {
        ...createBaseResult(),
        selectedEcommercePackage: null,
        ecommercePackageQuantity: 1,
        additionalTransactions: 0,
        totalEcommerceCost: 0,
        additionalTransactionSku: null,
        additionalTransactionQuantity: 0
    };
}

/**
 * Merge preserved settings into a result object (modifies the result object in place)
 * @param {Object} result - The result object to merge into
 * @param {Object} preservedSettings - The preserved settings to merge
 * @returns {Object} The merged result object
 */
export function mergePreservedSettings(result, preservedSettings) {
    Object.assign(result, preservedSettings);
    return result;
}

/**
 * Merge VAT processing results into a result object (modifies the result object in place)
 * @param {Object} result - The result object to merge into
 * @param {Object} vatResult - The VAT processing result to merge
 * @returns {Object} The merged result object
 */
export function mergeVatResult(result, vatResult) {
    result.shouldHaveBR00032 = vatResult.shouldHaveBR00032;
    result.shouldHaveBR00033 = vatResult.shouldHaveBR00033;
    result.br00032Quantity = vatResult.br00032Quantity;
    result.br00033Quantity = vatResult.br00033Quantity;
    return result;
}

/**
 * Merge language processing results into a result object (modifies the result object in place)
 * @param {Object} result - The result object to merge into
 * @param {Object} languageResult - The language processing result to merge
 * @returns {Object} The merged result object
 */
export function mergeLanguageResult(result, languageResult) {
    result.shouldHaveBR00129 = languageResult.shouldHaveBR00129;
    return result;
}

/**
 * Create an extended result object for financial statement properties
 * @returns {Object} Extended result object for financial statement properties
 */
export function createFinancialStatementResult() {
    return {
        ...createBaseResult(),
        selectedFinancialStatementPackage: null,
        financialStatementPackageItems: [],
        financialStatementBasePrice: 0,
        shouldHaveFinancialStatementPackage: false
    };
}

/**
 * Create a result object with preserved settings merged
 * @param {Object} preservedSettings - Settings to preserve
 * @returns {Object} Result object with preserved settings
 */
export function createResultWithPreservedSettings(preservedSettings = {}) {
    return {
        ...createBaseResult(),
        ...preservedSettings
    };
}
