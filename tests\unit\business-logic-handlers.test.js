import { describe, test, expect, beforeEach, vi } from 'vitest';

// Mock all dependencies
vi.mock('../../src/lib/hubspot-api.js', () => ({
  getDealProperties: vi.fn(),
  getDealLineItemsWithDetails: vi.fn(),
  updateDealProperties: vi.fn()
}));

vi.mock('../../src/lib/line-item-manager.js', () => ({
  deleteLineItem: vi.fn()
}));

vi.mock('../../src/lib/validation-utils.js', () => ({
  processVatStatus: vi.fn(),
  processLanguageProperty: vi.fn(),
  DOCUMENT_FIELDS: {
    BOOKING_OPERATIONS: ['faktury_rachunki_sprzedazowe___ile_', 'faktury_rachunki_zakupu___ile_'],
    CASH_BANK_OPERATIONS: ['kp_kw___banki_', 'kp_kw_gotowka'],
    ALL_DOCUMENT_FIELDS: [
      'operacje_kp_kw_walutowe',
      'kp_kw___banki_',
      'kp_kw_gotowka',
      'faktury_rachunki_sprzedazowe___ile_',
      'faktury_rachunki_zakupu___ile_',
      'faktury_walutowe___ile_miesiecznie_',
      'dokumenty_wewnetrzne_wdt__wnt_itp',
      'rodzaj_ksiegowosci'
    ]
  },
  calculateDocumentSum: vi.fn(),
  isFullAccountingType: vi.fn(),
  calculateBaseDocumentQuantity: vi.fn(),
  calculateBR00013DocumentQuantity: vi.fn(),
  calculateBR00013QuantityForPackage: vi.fn(),
  PAYROLL_CONFIG: {},
  processPayrollPackage: vi.fn(),
  parseAndValidateNumericField: vi.fn()
}));

vi.mock('../../src/lib/result-structure-utils.js', () => ({
  createAccountingResult: vi.fn(),
  mergeVatResult: vi.fn(),
  mergeLanguageResult: vi.fn()
}));

vi.mock('../../src/lib/accounting-packages.js', () => ({
  selectOptimalAccountingPackage: vi.fn()
}));

vi.mock('../../src/lib/ecommerce-packages.js', () => ({
  calculateOptimalEcommercePackage: vi.fn()
}));

import {
  handleComprehensiveUpdate,
  calculateAccountingPackageSelection,
  recalculateEcommercePackages
} from '../../src/lib/business-logic-handlers.js';

import { getDealProperties, getDealLineItemsWithDetails, updateDealProperties } from '../../src/lib/hubspot-api.js';
import { deleteLineItem } from '../../src/lib/line-item-manager.js';
import {
  processVatStatus,
  processLanguageProperty,
  calculateDocumentSum,
  isFullAccountingType,
  calculateBaseDocumentQuantity,
  calculateBR00013DocumentQuantity,
  calculateBR00013QuantityForPackage,
  processPayrollPackage,
  parseAndValidateNumericField
} from '../../src/lib/validation-utils.js';
import {
  createAccountingResult,
  mergeVatResult,
  mergeLanguageResult
} from '../../src/lib/result-structure-utils.js';
import { selectOptimalAccountingPackage } from '../../src/lib/accounting-packages.js';
import { calculateOptimalEcommercePackage } from '../../src/lib/ecommerce-packages.js';

describe('Business Logic Handlers', () => {
  const mockDealId = 'test-deal-123';
  const mockAccessToken = 'test-token';

  beforeEach(() => {
    vi.clearAllMocks();
    // Mock console methods to avoid noise in tests
    vi.spyOn(console, 'log').mockImplementation(() => {});
    vi.spyOn(console, 'error').mockImplementation(() => {});

    // Setup createAccountingResult mock to return a proper object
    createAccountingResult.mockReturnValue({
      br00013Quantity: 0,
      br00013BankStatementQuantity: 0,
      accountingPackages: null,
      isFullAccounting: false,
      selectedPackageName: 'BASE'
      // Don't set ecommerceRefreshed initially - let the function set it
    });
  });

  describe('calculateAccountingPackageSelection - Error Handling', () => {
    test('should throw error when selectOptimalAccountingPackage returns null', async () => {
      const mockResult = createAccountingResult();
      const mockAllProperties = {
        'faktury_rachunki_sprzedazowe___ile_': '10',
        'rodzaj_ksiegowosci': 'Pełna księgowość'
      };

      // Mock dependencies
      calculateDocumentSum.mockReturnValue(15);
      isFullAccountingType.mockReturnValue(true);
      selectOptimalAccountingPackage.mockResolvedValue(null); // This should trigger the error

      await expect(calculateAccountingPackageSelection(mockResult, mockAllProperties, mockAccessToken))
        .rejects.toThrow('Failed to select optimal accounting package');
    });

    test('should throw error when selectOptimalAccountingPackage returns result without items', async () => {
      const mockResult = createAccountingResult();
      const mockAllProperties = {
        'faktury_rachunki_sprzedazowe___ile_': '10',
        'rodzaj_ksiegowosci': 'Pełna księgowość'
      };

      // Mock dependencies
      calculateDocumentSum.mockReturnValue(15);
      isFullAccountingType.mockReturnValue(true);
      selectOptimalAccountingPackage.mockResolvedValue({ 
        selectedPackage: 'GOLD',
        items: null // This should trigger the error
      });

      await expect(calculateAccountingPackageSelection(mockResult, mockAllProperties, mockAccessToken))
        .rejects.toThrow('No accounting package items returned from optimization');
    });

    test('should handle zero document count correctly', async () => {
      const mockResult = createAccountingResult();
      const mockAllProperties = {
        'faktury_rachunki_sprzedazowe___ile_': '0',
        'rodzaj_ksiegowosci': 'Pełna księgowość'
      };

      // Mock dependencies
      calculateDocumentSum.mockReturnValue(0); // Zero documents
      isFullAccountingType.mockReturnValue(true);
      calculateBaseDocumentQuantity.mockReturnValue(0);
      calculateBR00013DocumentQuantity.mockReturnValue(0);
      calculateBR00013QuantityForPackage.mockReturnValue(0);

      const result = await calculateAccountingPackageSelection(mockResult, mockAllProperties, mockAccessToken);

      // Should not call selectOptimalAccountingPackage for zero documents
      expect(selectOptimalAccountingPackage).not.toHaveBeenCalled();
      expect(result.br00013Quantity).toBe(0);
      expect(result.br00013BankStatementQuantity).toBe(0);
    });

    test('should handle selectOptimalAccountingPackage API errors', async () => {
      const mockResult = createAccountingResult();
      const mockAllProperties = {
        'faktury_rachunki_sprzedazowe___ile_': '10',
        'rodzaj_ksiegowosci': 'Pełna księgowość'
      };

      // Mock dependencies - need to ensure non-zero document count to trigger the API call
      calculateDocumentSum.mockReturnValue(15);
      calculateBaseDocumentQuantity.mockReturnValue(15); // Non-zero to trigger API call
      calculateBR00013DocumentQuantity.mockReturnValue(15);
      isFullAccountingType.mockReturnValue(true);
      selectOptimalAccountingPackage.mockRejectedValue(new Error('HubSpot API error'));

      await expect(calculateAccountingPackageSelection(mockResult, mockAllProperties, mockAccessToken))
        .rejects.toThrow('HubSpot API error');
    });
  });

  describe('recalculateEcommercePackages - Error Handling', () => {
    test('should handle calculateOptimalEcommercePackage errors', async () => {
      const mockResult = createAccountingResult();
      const mockAllProperties = {
        'ile_transakcji_sprzedazy_w_miesiacu_': '100',
        'rodzaj_ksiegowosci': 'Pełna księgowość'
      };

      // Mock dependencies
      parseAndValidateNumericField.mockReturnValue(100);
      isFullAccountingType.mockReturnValue(true);
      calculateOptimalEcommercePackage.mockRejectedValue(new Error('E-commerce calculation failed'));

      // Function expects 4 parameters: result, allProperties, isFullAccounting, accessToken
      await expect(recalculateEcommercePackages(mockResult, mockAllProperties, true, mockAccessToken))
        .rejects.toThrow('Failed to recalculate e-commerce packages: E-commerce calculation failed');

      expect(console.error).toHaveBeenCalledWith(
        'Error recalculating e-commerce packages:',
        expect.any(Error)
      );
    });

    test('should handle zero transaction count correctly', async () => {
      // Create a fresh object without ecommerceRefreshed property
      const mockResult = {
        br00013Quantity: 0,
        br00013BankStatementQuantity: 0
      };
      const mockAllProperties = {
        'ile_transakcji_sprzedazy_w_miesiacu_': '0',
        'rodzaj_ksiegowosci': 'Pełna księgowość'
      };

      // Mock dependencies
      parseAndValidateNumericField.mockReturnValue(0);
      isFullAccountingType.mockReturnValue(true);

      // Function expects 4 parameters: result, allProperties, isFullAccounting, accessToken
      await recalculateEcommercePackages(mockResult, mockAllProperties, true, mockAccessToken);

      // Should not call calculateOptimalEcommercePackage for zero transactions
      expect(calculateOptimalEcommercePackage).not.toHaveBeenCalled();
      // The function doesn't set ecommerceRefreshed for zero transactions, but it might set a default
      expect(mockResult.ecommerceRefreshed).toBeFalsy();
    });

    test('should handle negative transaction count correctly', async () => {
      // Create a fresh object without ecommerceRefreshed property
      const mockResult = {
        br00013Quantity: 0,
        br00013BankStatementQuantity: 0
      };
      const mockAllProperties = {
        'ile_transakcji_sprzedazy_w_miesiacu_': '-5',
        'rodzaj_ksiegowosci': 'Pełna księgowość'
      };

      // Mock dependencies
      parseAndValidateNumericField.mockReturnValue(-5);
      isFullAccountingType.mockReturnValue(true);

      // Function expects 4 parameters: result, allProperties, isFullAccounting, accessToken
      await recalculateEcommercePackages(mockResult, mockAllProperties, true, mockAccessToken);

      // Should not call calculateOptimalEcommercePackage for negative transactions
      expect(calculateOptimalEcommercePackage).not.toHaveBeenCalled();
      // The function doesn't set ecommerceRefreshed for negative transactions, but it might set a default
      expect(mockResult.ecommerceRefreshed).toBeFalsy();
    });

    test('should handle parseAndValidateNumericField errors', async () => {
      const mockResult = createAccountingResult();
      const mockAllProperties = {
        'ile_transakcji_sprzedazy_w_miesiacu_': 'invalid',
        'rodzaj_ksiegowosci': 'Pełna księgowość'
      };

      // Mock dependencies
      parseAndValidateNumericField.mockImplementation(() => {
        throw new Error('Invalid numeric value');
      });

      // Function expects 4 parameters: result, allProperties, isFullAccounting, accessToken
      await expect(recalculateEcommercePackages(mockResult, mockAllProperties, true, mockAccessToken))
        .rejects.toThrow('Invalid numeric value');
    });

    test('should successfully recalculate e-commerce packages', async () => {
      const mockResult = createAccountingResult();
      const mockAllProperties = {
        'ile_transakcji_sprzedazy_w_miesiacu_': '150',
        'rodzaj_ksiegowosci': 'Pełna księgowość'
      };

      const mockOptimalPackage = {
        selectedPackage: 'BR00058',
        totalCost: 250,
        additionalTransactions: 0
      };

      // Mock dependencies
      parseAndValidateNumericField.mockReturnValue(150);
      isFullAccountingType.mockReturnValue(true);
      calculateOptimalEcommercePackage.mockResolvedValue(mockOptimalPackage);

      // Function expects 4 parameters: result, allProperties, isFullAccounting, accessToken
      await recalculateEcommercePackages(mockResult, mockAllProperties, true, mockAccessToken);

      expect(calculateOptimalEcommercePackage).toHaveBeenCalledWith(150, true, mockAccessToken);
      expect(mockResult.ecommercePackage).toEqual(mockOptimalPackage);
      expect(mockResult.ecommerceRefreshed).toBe(true);
      expect(mockResult.ecommerceTransactionCount).toBe(150);
    });
  });

  describe('handleComprehensiveUpdate - Error Handling', () => {
    test('should handle getDealProperties errors', async () => {
      getDealProperties.mockRejectedValue(new Error('Deal not found'));

      await expect(handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      )).rejects.toThrow('Deal not found');
    });

    test('should handle updateDealProperties errors', async () => {
      const mockProperties = {
        'faktury_rachunki_sprzedazowe___ile_': '10',
        'rodzaj_ksiegowosci': 'Pełna księgowość',
        'uslugi_do_wyceny': 'Księgowość'
      };

      // Mock successful property fetch
      getDealProperties.mockResolvedValue(mockProperties);
      
      // Mock other dependencies
      createAccountingResult.mockReturnValue({
        shouldHaveBR00032: false,
        shouldHaveBR00033: false,
        shouldHaveBR00129: false,
        br00032Quantity: 0,
        br00033Quantity: 0,
        br00013Quantity: 0,
        br00013BankStatementQuantity: 0,
        accountingPackages: null,
        isFullAccounting: false,
        selectedPackageName: 'BASE'
      });

      calculateDocumentSum.mockReturnValue(10);
      isFullAccountingType.mockReturnValue(true);
      calculateBaseDocumentQuantity.mockReturnValue(10);
      calculateBR00013DocumentQuantity.mockReturnValue(5);
      calculateBR00013QuantityForPackage.mockReturnValue(5);
      
      selectOptimalAccountingPackage.mockResolvedValue({
        selectedPackage: 'BASE',
        items: [{ sku: 'BR00003', quantity: 1 }]
      });

      parseAndValidateNumericField.mockReturnValue(0);
      processVatStatus.mockReturnValue({
        shouldHaveBR00032: false,
        shouldHaveBR00033: false,
        br00032Quantity: 0,
        br00033Quantity: 0
      });
      processLanguageProperty.mockReturnValue({
        shouldHaveBR00129: false
      });

      // Mock updateDealProperties to fail
      updateDealProperties.mockRejectedValue(new Error('Update failed'));

      await expect(handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      )).rejects.toThrow('Update failed');
    });
  });
});
