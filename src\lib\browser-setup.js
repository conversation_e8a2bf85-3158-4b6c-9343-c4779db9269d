import { exec } from 'child_process';
import { promisify } from 'util';
import { existsSync } from 'fs';
import { join } from 'path';

const execAsync = promisify(exec);

/**
 * Check if Playwright browsers are installed
 * @returns {Promise<boolean>} True if browsers are available
 */
export async function areBrowsersInstalled() {
  try {
    // Try to get browser path - if this succeeds, browsers are installed
    const { chromium } = await import('playwright-core');
    const browser = await chromium.launch({ headless: true });
    await browser.close();
    return true;
  } catch (error) {
    console.log('Browsers not installed or not working:', error.message);
    return false;
  }
}

/**
 * Install Playwright browsers if they're not already installed
 * @returns {Promise<boolean>} True if installation was successful
 */
export async function ensureBrowsersInstalled() {
  console.log('Checking if Playwright browsers are installed...');
  
  if (await areBrowsersInstalled()) {
    console.log('✅ Playwright browsers are already installed and working');
    return true;
  }

  console.log('📦 Installing Playwright browsers...');
  
  try {
    // Install only Chromium browser (lighter than installing all browsers)
    console.log('Installing Chromium browser...');
    await execAsync('npx playwright install chromium', { 
      timeout: 300000, // 5 minutes timeout
      env: { ...process.env, PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: '0' }
    });
    
    // Try to install system dependencies, but don't fail if it doesn't work
    try {
      console.log('Installing system dependencies...');
      await execAsync('npx playwright install-deps chromium', { 
        timeout: 300000,
        env: { ...process.env }
      });
      console.log('✅ System dependencies installed successfully');
    } catch (depsError) {
      console.warn('⚠️  System dependencies installation failed, but continuing...');
      console.warn('This might work anyway if the container has the required libraries');
      console.warn('Error:', depsError.message);
    }
    
    // Verify installation worked
    if (await areBrowsersInstalled()) {
      console.log('✅ Playwright browsers installed and verified successfully');
      return true;
    } else {
      console.error('❌ Browser installation completed but verification failed');
      return false;
    }
    
  } catch (error) {
    console.error('❌ Failed to install Playwright browsers:', error.message);
    console.error('The application may not work properly without browsers');
    return false;
  }
}

/**
 * Initialize browsers with retry logic
 * @param {number} maxRetries - Maximum number of retry attempts
 * @returns {Promise<boolean>} True if browsers are ready
 */
export async function initializeBrowsers(maxRetries = 3) {
  console.log('🚀 Initializing Playwright browsers...');
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    console.log(`Attempt ${attempt}/${maxRetries}`);
    
    if (await ensureBrowsersInstalled()) {
      console.log('🎉 Browsers are ready!');
      return true;
    }
    
    if (attempt < maxRetries) {
      console.log(`Retrying in 5 seconds...`);
      await new Promise(resolve => setTimeout(resolve, 5000));
    }
  }
  
  console.error('❌ Failed to initialize browsers after all attempts');
  console.error('The application will start but browser-dependent features may not work');
  return false;
}

/**
 * Get browser launch options optimized for containerized environments
 * @returns {Object} Browser launch options
 */
export function getBrowserLaunchOptions() {
  return {
    headless: true,
    timeout: 60000,
    args: [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-dev-shm-usage',
      '--disable-accelerated-2d-canvas',
      '--no-first-run',
      '--no-zygote',
      '--single-process', // This can help with memory issues
      '--disable-gpu'
    ]
  };
}
