# Test Organization Structure

This document describes the reorganized test structure to ensure proper separation between unit tests and integration tests.

## Principles

1. **One-to-One Mapping**: Each source file has exactly one corresponding unit test file
2. **Single Responsibility**: Each unit test file tests only its corresponding source file
3. **Isolation**: Unit tests mock all external dependencies
4. **Integration Separation**: Comprehensive flows that test multiple modules are in integration tests

## Unit Tests Structure

### File Naming Convention
- Source file: `src/lib/[module-name].js`
- Unit test file: `tests/unit/[module-name].test.js`

### Current Unit Test Coverage

| Source File | Unit Test File | Description |
|-------------|----------------|-------------|
| `accounting-packages.js` | `accounting-packages.test.js` | Tests accounting package selection and cost calculation |
| `asana-api.js` | `asana-api.test.js` | Tests Asana API integration functions |
| `browser-setup.js` | `browser-setup.test.js` | Tests browser configuration and setup |
| `business-logic-handlers.js` | `business-logic-handlers.test.js` | Tests business logic handler functions |
| `ecommerce-packages.js` | `ecommerce-packages.test.js` | Tests e-commerce package selection logic |
| `financial-statement-packages.js` | `financial-statement-packages.test.js` | Tests financial statement package logic |
| `hubspot-api.js` | `hubspot-api.test.js` | Tests HubSpot API integration functions |
| `krsPlaywrightService.js` | `krsPlaywrightService.test.js` | Tests KRS data extraction service |
| `line-item-manager.js` | `line-item-manager.test.js` | Tests line item management functions |
| `pit-packages.js` | `pit-packages.test.js` | Tests PIT package selection logic |
| `price-fetcher.js` | `price-fetcher.test.js` | Tests price fetching and caching |
| `property-preservation-utils.js` | `property-preservation-utils.test.js` | Tests property preservation utilities |
| `result-structure-utils.js` | `result-structure-utils.test.js` | Tests result structure utilities |
| `ryczalt-logger.js` | `ryczalt-logger.test.js` | Tests Ryczałt calculation logging |
| `validation-utils.js` | `validation-utils.test.js` | Tests validation and utility functions |

## Integration Tests Structure

Integration tests are located in `tests/integration/` and test complete business flows across multiple modules.

### Integration Test Files

| Test File | Description |
|-----------|-------------|
| `comprehensive-business-flows.test.js` | Placeholder for future comprehensive flow tests |
| `comprehensive-business-scenarios.test.js` | Real-world business scenarios testing multiple modules |
| `consolidated-integration-tests.test.js` | Large collection of integration tests across modules |
| `edge-cases-and-complex-scenarios.test.js` | Edge cases and complex business scenarios |
| `financial-statement-integration.test.js` | Financial statement integration flows |
| `real-world-scenarios.test.js` | Placeholder for future real-world scenario tests |

## Benefits of This Organization

1. **Clear Responsibility**: Deleting a unit test file removes all tests for that specific source file
2. **Easy Maintenance**: Each unit test file focuses on a single module
3. **Better Test Discovery**: Developers can easily find tests for a specific module
4. **Separation of Concerns**: Unit tests focus on individual functions, integration tests focus on workflows
5. **Faster Unit Test Execution**: Unit tests run faster due to mocking of dependencies

## Test Execution

### Running Unit Tests Only
```bash
npm test tests/unit/
```

### Running Integration Tests Only
```bash
npm test tests/integration/
```

### Running Tests for a Specific Module
```bash
npm test tests/unit/[module-name].test.js
```

## Guidelines for Adding New Tests

### For New Source Files
1. Create a corresponding unit test file: `tests/unit/[module-name].test.js`
2. Test only the functions exported from that module
3. Mock all external dependencies

### For New Features
1. Add unit tests to the appropriate module's test file
2. If the feature involves multiple modules working together, add integration tests

### For Bug Fixes
1. Add unit tests to verify the fix in the appropriate module's test file
2. If the bug involves interaction between modules, add integration tests

## Migration Notes

The following changes were made during reorganization:

1. **Renamed Files**: Removed "-additional" suffix from test files
2. **Moved Comprehensive Tests**: Moved multi-module tests to integration folder
3. **Merged Related Tests**: Merged kadry-logic tests into validation-utils tests
4. **Created Missing Tests**: Added placeholder unit tests for modules without tests
5. **Established Naming Convention**: Ensured consistent naming between source and test files

This organization ensures that the test suite is maintainable, discoverable, and follows best practices for unit and integration testing.
