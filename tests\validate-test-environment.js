#!/usr/bin/env node

/**
 * Test Environment Validation Script
 *
 * This script validates that the test environment is properly configured
 * before running the comprehensive test suite.
 */

// Load environment variables
import { config } from 'dotenv';
config();

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { 
  TEST_CONFIG,
  getTestAccessToken,
  validateTestDeal,
  canRunIntegrationTests
} from './test-config.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// ANSI color codes
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

async function validateEnvironment() {
  log('\n🔍 Validating Test Environment', 'cyan');
  log('=' .repeat(50), 'cyan');
  
  let allValid = true;
  const issues = [];
  
  // 1. Check Node.js version
  log('\n📋 Checking Node.js version...', 'yellow');
  const nodeVersion = process.version;
  const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
  
  if (majorVersion >= 16) {
    log(`✅ Node.js ${nodeVersion} (supported)`, 'green');
  } else {
    log(`❌ Node.js ${nodeVersion} (requires >= 16.0.0)`, 'red');
    issues.push('Upgrade Node.js to version 16 or higher');
    allValid = false;
  }
  
  // 2. Check HubSpot access token
  log('\n🔑 Checking HubSpot access token...', 'yellow');
  if (canRunIntegrationTests()) {
    log('✅ HUBSPOT_ACCESS_TOKEN found', 'green');
    
    try {
      const accessToken = getTestAccessToken();
      
      // Test basic API connectivity
      const { getDealProperties } = await import('../src/lib/hubspot-api.js');
      const testDealId = TEST_CONFIG.TEST_DEALS.SAMPLE_DEAL;
      
      log(`🔗 Testing API connectivity with deal ${testDealId}...`, 'blue');
      
      const validation = await validateTestDeal(testDealId, accessToken);
      if (validation.isValid) {
        log('✅ HubSpot API connectivity verified', 'green');
        log(`📊 Test deal properties: ${Object.keys(validation.properties).length} fields`, 'blue');
      } else {
        log(`⚠️  Test deal validation failed: ${validation.error}`, 'yellow');
        log('   Integration tests will use fallback scenarios', 'yellow');
      }
      
    } catch (error) {
      log(`❌ HubSpot API test failed: ${error.message}`, 'red');
      issues.push('Verify HubSpot access token has correct permissions');
      allValid = false;
    }
    
  } else {
    log('⚠️  HUBSPOT_ACCESS_TOKEN not found', 'yellow');
    log('   Integration tests will be skipped', 'yellow');
    log('   Set HUBSPOT_ACCESS_TOKEN environment variable for full testing', 'blue');
  }
  
  // 3. Check price fetching
  if (canRunIntegrationTests()) {
    log('\n💰 Testing price fetching...', 'yellow');
    try {
      const { getMultipleProductPrices } = await import('../src/lib/price-fetcher.js');
      const accessToken = getTestAccessToken();
      
      const requiredSkus = ['BR00003', 'BR00004', 'BR00013', 'BR00022'];
      const prices = await getMultipleProductPrices(requiredSkus, accessToken);
      const missingSkus = requiredSkus.filter(sku => !prices[sku] || prices[sku] <= 0);
      
      if (missingSkus.length === 0) {
        log('✅ Price fetching verified', 'green');
        log(`📈 Fetched ${Object.keys(prices).length} product prices`, 'blue');
      } else {
        log(`⚠️  Missing or invalid prices for: ${missingSkus.join(', ')}`, 'yellow');
        log('   Tests will continue but may have limited coverage', 'yellow');
      }
      
    } catch (error) {
      log(`❌ Price fetching failed: ${error.message}`, 'red');
      issues.push('Verify HubSpot product catalog is properly configured');
    }
  }
  
  // 4. Check test configuration
  log('\n⚙️  Validating test configuration...', 'yellow');
  
  const configChecks = [
    { name: 'Test timeouts', value: TEST_CONFIG.TIMEOUTS.API_CALL, min: 10000 },
    { name: 'Sample deal ID', value: TEST_CONFIG.TEST_DEALS.SAMPLE_DEAL, required: true },
    { name: 'Mock writes flag', value: TEST_CONFIG.FLAGS.MOCK_ALL_WRITES, expected: true }
  ];
  
  configChecks.forEach(check => {
    if (check.required && !check.value) {
      log(`❌ ${check.name}: missing`, 'red');
      issues.push(`Configure ${check.name}`);
      allValid = false;
    } else if (check.min && check.value < check.min) {
      log(`⚠️  ${check.name}: ${check.value} (may be too low)`, 'yellow');
    } else if (check.expected !== undefined && check.value !== check.expected) {
      log(`⚠️  ${check.name}: ${check.value} (expected: ${check.expected})`, 'yellow');
    } else {
      log(`✅ ${check.name}: ${check.value}`, 'green');
    }
  });
  
  // 5. Check dependencies
  log('\n📦 Checking dependencies...', 'yellow');
  try {
    // Check if Vitest is available (but don't import it outside test environment)
    const fs = await import('fs');
    const path = await import('path');
    const packageJsonPath = path.join(process.cwd(), 'package.json');
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));

    if (packageJson.devDependencies && packageJson.devDependencies['vitest']) {
      log('✅ Vitest testing framework configured', 'green');
    } else {
      throw new Error('Vitest not found in package.json');
    }

    await import('../src/lib/validation-utils.js');
    log('✅ Core business logic modules available', 'green');

    await import('../src/lib/hubspot-api.js');
    log('✅ HubSpot API modules available', 'green');
    
  } catch (error) {
    log(`❌ Dependency check failed: ${error.message}`, 'red');
    issues.push('Run npm install to install missing dependencies');
    allValid = false;
  }
  
  // 6. Summary
  log('\n📋 Validation Summary', 'bright');
  log('=' .repeat(30), 'cyan');
  
  if (allValid) {
    log('✅ Environment validation passed!', 'green');
    log('🚀 Ready to run comprehensive test suite', 'green');
    
    if (canRunIntegrationTests()) {
      log('🔗 Integration tests will use real HubSpot data', 'blue');
      log('🔒 All write operations will be safely mocked', 'blue');
    } else {
      log('⚠️  Integration tests will be limited (no HubSpot token)', 'yellow');
    }
    
  } else {
    log('❌ Environment validation failed!', 'red');
    log('\n🔧 Issues to resolve:', 'yellow');
    issues.forEach(issue => log(`   • ${issue}`, 'yellow'));
    
    log('\n💡 Quick setup guide:', 'blue');
    log('   1. Set HUBSPOT_ACCESS_TOKEN environment variable', 'blue');
    log('   2. Ensure the token has read access to deals and products', 'blue');
    log('   3. Verify the test deal ID exists in your HubSpot account', 'blue');
    log('   4. Run: npm install (if dependencies are missing)', 'blue');
  }
  
  // 7. Test recommendations
  log('\n💡 Test Execution Recommendations:', 'cyan');
  
  if (canRunIntegrationTests()) {
    log('   npm run test:all        # Full test suite with real data', 'blue');
    log('   npm run test:integration # Integration tests only', 'blue');
  } else {
    log('   npm run test:unit       # Unit tests (no HubSpot required)', 'blue');
    log('   npm run test:coverage   # Coverage analysis', 'blue');
  }
  
  log('   npm run test:watch      # Development mode', 'blue');
  log('   npm run test:help       # Show all options', 'blue');
  
  return allValid;
}

// This module is designed to be imported, not run directly
// Use tests/validate-environment.js for standalone execution

export { validateEnvironment };
