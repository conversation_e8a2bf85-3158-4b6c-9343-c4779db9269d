[variables]
NODE_ENV = "production"
NPM_CONFIG_PRODUCTION = "false"

[phases.setup]
nixPkgs = ["nodejs_20"]
nixLibs = ["libuuid", "libGL"]
aptPkgs = [
    "curl",
    "wget",
    "ca-certificates",
    "fonts-liberation",
    "libappindicator3-1",
    "libasound2",
    "libatk-bridge2.0-0",
    "libdrm2",
    "libgtk-3-0",
    "libnspr4",
    "libnss3",
    "libxcomposite1",
    "libxdamage1",
    "libxrandr2",
    "xdg-utils",
    "libxss1",
    "libgconf-2-4"
]

[phases.install]
dependsOn = ["setup"]
cmds = ["npm ci"]

[phases.build]
dependsOn = ["install"]
cmds = ["npm run build"]

[start]
cmd = "npm run start"
