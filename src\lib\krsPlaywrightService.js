import waitOn from 'wait-on';
import { exec } from 'child_process';
import { chromium } from 'playwright-core';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import { initializeBrowsers, getBrowserLaunchOptions } from './browser-setup.js';

// ES Module equivalent for __dirname
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

/**
 * Fetches KRS data for a given NIP number
 * @param {Object} page - Playwright page object
 * @param {string} nip - NIP number to search for (default: '7792455049')
 * @returns {Promise<Array>} - JSON array with the table data
 */
async function fetchKrsData(page, nip = '7792455049') {
  await page.goto('https://wyszukiwarka-krs.ms.gov.pl/');
  await page.getByRole('textbox', { name: 'NIP' }).click();
  await page.getByRole('textbox', { name: 'NIP' }).fill(nip);
  await page.getByRole('button', { name: 'Wyszukaj' }).click();
  await page.locator('p-checkbox').filter({ hasText: 'Przedsiębiorcy' }).locator('div').nth(2).click();
  await page.getByRole('button', { name: 'Wyszukaj' }).click();

  // Wait for search results to load
  const searchResultsTable = page.getByLabel('Wyniki wyszukiwania -').locator('div').filter({ hasText: 'Numer KRS Nazwa Miejscowość' }).nth(1);
  await searchResultsTable.waitFor({ state: 'visible', timeout: 10000 });

  console.log('Search results table found, extracting data...');

  // First, extract the search results table to find the first link
  const searchResults = await searchResultsTable.evaluate(table => {
    const result = {
      rows: []
    };

    // Find the table element
    const tableElement = table.querySelector('table[role="grid"]');
    if (!tableElement) return result;

    // Get all rows
    const rows = tableElement.querySelectorAll('tbody tr');

    // Process each row
    Array.from(rows).forEach(row => {
      const rowData = {};
      const cells = row.querySelectorAll('td');

      // Process each cell
      Array.from(cells).forEach((cell, index) => {
        // Get column header from the table header
        const headerRow = tableElement.querySelector('thead tr');
        const headerCell = headerRow ? headerRow.querySelectorAll('th')[index] : null;
        const columnName = headerCell ? headerCell.textContent.trim() : `Column${index}`;

        // Get cell value
        const cellValue = cell.textContent.trim();
        rowData[columnName] = cellValue;

        // Check for links
        const links = cell.querySelectorAll('a');
        if (links.length > 0) {
          rowData[`${columnName}_links`] = Array.from(links).map(link => {
            return {
              text: link.textContent.trim(),
              href: link.getAttribute('href'),
              onclick: link.getAttribute('onclick')
            };
          });
        }
      });

      result.rows.push(rowData);
    });

    return result;
  });

  // Log the search results
  console.log(`Found ${searchResults.rows.length} search result rows`);

  // Find the first link in the search results
  let firstLinkText = null;
  let firstLinkColumn = null;

  for (const row of searchResults.rows) {
    let foundLink = false;

    for (const key of Object.keys(row)) {
      if (key.endsWith('_links') && row[key].length > 0) {
        firstLinkText = row[key][0].text;
        firstLinkColumn = key.replace('_links', '');
        console.log(`Found first link: ${firstLinkText} in column ${firstLinkColumn}`);
        foundLink = true;
        break;
      }
    }

    if (foundLink) break;
  }

  if (!firstLinkText) {
    console.error('No links found in search results');
    return searchResults.rows;
  }

  // Click on the first link found
  console.log(`Clicking on link with text: ${firstLinkText}`);
  await page.getByRole('gridcell', { name: firstLinkText }).click();

  // Now follow the original script logic to extract "Członkowie reprezentacji" table
  console.log('Waiting for "Członkowie reprezentacji" panel to load...');
  const czlonkowieHeaderLocator = page.locator('ds-panel-header:has-text("Członkowie reprezentacji")');
  await czlonkowieHeaderLocator.waitFor({ state: 'visible', timeout: 15000 });
  await czlonkowieHeaderLocator.click();

  const czlonkowieContentPanel = czlonkowieHeaderLocator
    .locator('xpath=ancestor::div[contains(@class, "p-panel-header")]')
    .locator('xpath=following-sibling::div[contains(@class, "p-toggleable-content")]');

  await czlonkowieContentPanel.waitFor({ state: 'visible', timeout: 10000 });

  const tableContainerLocator = page.getByLabel('Członkowie reprezentacji')
    .locator('div')
    .filter({ hasText: 'Nazwisko lub Nazwa Nazwisko' })
    .nth(1);

  await tableContainerLocator.waitFor();

  const tableLocator = tableContainerLocator.locator('table[role="grid"]');
  await tableLocator.waitFor({ state: 'visible' });

  console.log('Extracting "Członkowie reprezentacji" table data...');
  const jsonData = await tableLocator.locator('tbody tr.table-row').evaluateAll(rows => {
    return rows.map(row => {
      const rowData = {};
      const cells = Array.from(row.querySelectorAll(':scope > td.ng-star-inserted'));
      cells.forEach(cell => {
        const titleElement = cell.querySelector('div.ds-column-title');
        const valueElement = cell.querySelector('div.ds-column-value');
        if (titleElement && valueElement) {
          const key = titleElement.textContent?.trim();
          const value = valueElement.textContent?.trim();
          if (key) {
            rowData[key] = value;
          }
        }
      });
      return rowData;
    }).filter(obj => Object.keys(obj).length > 0);
  });

  return jsonData;
}

/**
 * Runs the KRS data service with a standalone Playwright instance
 * @param {Object} options - Configuration options
 * @param {string} options.nip - NIP number to search for
 * @param {number} options.port - Port to run the server on
 * @param {number} options.timeout - Timeout in milliseconds
 * @returns {Promise<Array>} - JSON array with the "Członkowie reprezentacji" table data
 */
export async function runKrsService(options = {}) {
  const {
    nip = '7792455049',
    port = 5001,
    timeout = 300000
  } = options;

  console.log(`Starting KRS service for NIP: ${nip}`);

  // Ensure browsers are installed before launching
  console.log('Ensuring browsers are available...');
  const browsersReady = await initializeBrowsers();

  if (!browsersReady) {
    throw new Error('Failed to initialize browsers. Cannot proceed with KRS data extraction.');
  }

  // Launch browser with optimized options
  const browser = await chromium.launch(getBrowserLaunchOptions());

  try {
    const context = await browser.newContext();
    const page = await context.newPage();

    // Add page error handler
    page.on('console', msg => console.log(`Browser console: ${msg.text()}`));
    page.on('pageerror', err => console.error(`Browser page error: ${err.message}`));

    // Execute the function with the provided NIP
    const data = await fetchKrsData(page, nip);
    console.log('Członkowie reprezentacji data fetched successfully!');
    console.log(`Found ${data.length} members in the table`);

    // Close browser resources
    await context.close();
    await browser.close();

    return data;
  } catch (error) {
    console.error('Error occurred:', error.message);

    try {
      // Try to take a screenshot for debugging
      const page = await browser.newPage();
      const screenshotPath = './error-screenshot.png';
      await page.screenshot({ path: screenshotPath });
      console.log(`Screenshot saved to ${screenshotPath}`);
      await page.close();
    } catch (screenshotError) {
      console.error('Failed to take screenshot:', screenshotError.message);
    }

    await browser.close();
    console.error('Error fetching KRS data:', error);
    throw error;
  }
}

// If this file is run directly, execute the service
if (import.meta.url === `file://${process.argv[1]}`) {
  const nip = process.argv[2] || '7792455049';
  console.log(`Starting KRS data extraction for NIP: ${nip}`);

  runKrsService({ nip })
    .then(data => {
      console.log('Extracted "Członkowie reprezentacji" data:');
      console.log(JSON.stringify(data, null, 2));
    })
    .catch(error => {
      console.error('Failed to get KRS data:', error);
      process.exit(1);
    });
}
