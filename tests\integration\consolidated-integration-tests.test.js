import { describe, test, expect, beforeEach, vi } from 'vitest';

// ============================================================================
// IMPORTS - All business logic modules and utilities
// ============================================================================
import {
  selectOptimalAccountingPackage,
  calculateAccountingPackageCost,
  getAccountingPackageConfig
} from '../../src/lib/accounting-packages.js';

import {
  calculateBR00013QuantityForPackage,
  calculateDocumentSum,
  isFullAccountingType,
  calculateBaseDocumentQuantity,
  calculateBR00013DocumentQuantity,
  DOCUMENT_FIELDS,
  PACKAGE_MINIMUMS,
  processPayrollPackage,
  clearAllPayrollQuantities,
  PAYROLL_CONFIG,
  processVatStatus,
  processLanguageProperty
} from '../../src/lib/validation-utils.js';

import {
  handleComprehensiveUpdate
} from '../../src/lib/business-logic-handlers.js';

// Mock prices for testing payroll functions
const mockPayrollPrices = {
  BR00070: 80,  // Employment contract price
  BR00071: 70,  // Civil contract price
  BR00080: 50,  // PFRON price
  BR00165: 120, // A1 price
  BR00076: 50   // Mobility package price
};

import {
  testEcommercePricing,
  calculateOptimalEcommercePackage
} from '../../src/lib/ecommerce-packages.js';

import {
  getPitPackagePrices,
  selectPitPackageForSimplifiedAccounting
} from '../../src/lib/pit-packages.js';

import {
  findLineItemBySku,
  findLineItemBySkuSafe,
  validateSkuInProductLibrary,
  createLineItemFromProduct,
  createLineItem,
  createLineItemWithQuantity,
  createLineItemFromProductWithQuantity,
  updateLineItemQuantity,
  deleteLineItem,
  associateLineItemWithDeal,
  createAndAssociateLineItem,
  manageLineItemQuantity,
  manageBooleanLineItem,
  createLineItemWithCustomPrice,
  updateLineItemPrice,
  manageLineItemWithCustomPrice,
  updateAccountingPackagesToOptimal
} from '../../src/lib/line-item-manager.js';

// ============================================================================
// MOCKS - Centralized mock setup for all dependencies
// ============================================================================
vi.mock('../../src/lib/line-item-manager.js', () => ({
  findLineItemBySku: vi.fn(),
  findLineItemBySkuSafe: vi.fn(),
  validateSkuInProductLibrary: vi.fn(),
  createLineItemFromProduct: vi.fn(),
  createLineItem: vi.fn(),
  createLineItemWithQuantity: vi.fn(),
  createLineItemFromProductWithQuantity: vi.fn(),
  updateLineItemQuantity: vi.fn(),
  deleteLineItem: vi.fn(),
  associateLineItemWithDeal: vi.fn(),
  createAndAssociateLineItem: vi.fn(),
  manageLineItemQuantity: vi.fn(),
  manageBooleanLineItem: vi.fn(),
  createLineItemWithCustomPrice: vi.fn(),
  updateLineItemPrice: vi.fn(),
  manageLineItemWithCustomPrice: vi.fn(),
  updateAccountingPackagesToOptimal: vi.fn(),
  calculateBR00069CustomPrice: vi.fn()
}));

vi.mock('../../src/lib/price-fetcher.js', () => ({
  fetchPricesFromHubSpot: vi.fn(),
  getAccountingPackagePrices: vi.fn(),
  getPayrollPrices: vi.fn(),
  getEcommercePackagePrices: vi.fn(),
  getProductPrice: vi.fn()
}));

// Don't mock PIT packages for unit tests - test the real implementation
// vi.mock('../../src/lib/pit-packages.js', () => ({
//   getPitPackagePrices: vi.fn(),
//   selectPitPackageForSimplifiedAccounting: vi.fn()
// }));

vi.mock('../../src/lib/hubspot-api.js', () => ({
  getDealProperties: vi.fn(),
  updateLineItemsForDeal: vi.fn(),
  getLineItemsForDeal: vi.fn(),
  findProductBySku: vi.fn(),
  updateDealProperties: vi.fn(),
  getDealLineItemsWithDetails: vi.fn()
}));

import { fetchPricesFromHubSpot, getAccountingPackagePrices, getPayrollPrices, getEcommercePackagePrices, getProductPrice } from '../../src/lib/price-fetcher.js';
import { getDealProperties, updateLineItemsForDeal, getLineItemsForDeal, findProductBySku, updateDealProperties, getDealLineItemsWithDetails } from '../../src/lib/hubspot-api.js';
import { deleteLineItem } from '../../src/lib/line-item-manager.js';
import { calculatePayrollPackageQuantities, calculateAccountingPackageSelection, parseIntSafe, convertToNumber } from '../../src/lib/business-logic-handlers.js';
// Import real PIT package functions for unit testing
import { getPitPackagePrices, selectPitPackageForSimplifiedAccounting } from '../../src/lib/pit-packages.js';

// ============================================================================
// SHARED TEST UTILITIES - Common helpers and mock data
// ============================================================================

/**
 * Creates mock deal properties with default values and optional overrides
 * Used across all test scenarios to ensure consistency
 */
function createMockDealProperties(overrides = {}) {
  return {
    'faktury_rachunki_sprzedazowe___ile_': '0',
    'faktury_rachunki_zakupu___ile_': '0',
    'faktury_walutowe___ile_miesiecznie_': '0',
    'dokumenty_wewnetrzne_wdt__wnt_itp': '0',
    'operacje_kp_kw_walutowe': '0',
    'kp_kw___banki_': '0',
    'kp_kw_gotowka': '0',
    'rodzaj_ksiegowosci': 'Pełna księgowość',
    'jezyk_obslugi': 'Polski',
    'vat___status_podatnika': '',
    'pakiet_kadrowo_placowy': '',
    'umowa_o_prace___liczba_osob': '0',
    'umowy_cywilnoprawne___liczba_pracownikow': '0',
    'ppk___ile_osob_': '0',
    'pfron___ile_osob_': '0',
    'a1___czy_wystepuja_': '0',
    'kto_robi_import_do_moje_ppk': 'Klient',
    'srodki_trwale_i_wartosci_niematerialne_i_prawne___ile_': '0',
    'kasy_fiskalne___ile_': '0',
    'pytania_do_msp': '',
    'ilosc_kont_bankowych___raporty_dzienne': '0',
    'ilosc_kont_bankowych___raporty_miesieczne': '0',
    'dodatkowe_skladniki_wynagrodzenia': '',
    'wyciagi_bankowe___liczba_': '0',
    'ile_kanalow_platniczych_': '0',
    'branza': 'E-commerce', // Default to E-commerce to prevent field clearing
    'uslugi_do_wyceny': 'Księgowość', // Default to include accounting services
    ...overrides
  };
}

/**
 * Creates standardized mock prices for accounting packages
 * Ensures consistent pricing across all tests
 */
function createMockPrices() {
  return {
    'BR00003': 499,    // BASE full accounting
    'BR00004': 764,    // SILVER full accounting
    'BR00005': 1374,   // GOLD full accounting
    'BR00006': 2499,   // PLATINUM full accounting
    'BR00007': 234,    // BASE simplified accounting
    'BR00008': 294,    // SILVER simplified accounting
    'BR00009': 524,    // GOLD simplified accounting
    'BR00010': 999,    // PLATINUM simplified accounting
    'BR00022': 12.9,   // Extra documents (50 pack)
    'BR00023': 49.9    // Extra documents (200 pack)
  };
}

/**
 * Creates mock payroll result structure
 * Used for payroll package testing
 */
function createMockPayrollResult() {
  return {
    br00069Quantity: 0,
    br00069OverridePrice: 0,
    br00070Quantity: 0,
    br00071Quantity: 0,
    br00072Quantity: 0,
    br00073Quantity: 0,
    br00074Quantity: 0,
    br00075Quantity: 0,
    br00077Quantity: 0,
    br00080Quantity: 0,
    br00165Quantity: 0,
    shouldHaveBR00078: false,
    shouldHaveBR00079: false,
    // New KADRY-related properties
    br00114Quantity: 0,
    shouldHaveBR00115: false,
    shouldHaveBR00117: false,
    shouldHaveBR00118: false,
    shouldHaveBR00119: false,
    shouldHaveBR00081: false
  };
}

/**
 * Creates mock e-commerce pricing data for full accounting
 * Used for e-commerce package testing
 */
function createMockFullAccountingEcommercePrices() {
  return {
    packages: {
      BR00058: 'BR00058', // 200 transactions
      BR00059: 'BR00059', // 1000 transactions
      BR00060: 'BR00060', // 5000 transactions
      BR00061: 'BR00061'  // 20000 transactions
    },
    additionalSkus: {
      BR00170: 'BR00170', // Additional 200 transactions
      BR00171: 'BR00171', // Additional 1000 transactions
      BR00172: 'BR00172', // Additional 5000 transactions
      BR00173: 'BR00173'  // Additional 20000 transactions
    },
    prices: {
      'BR00058': 250,   // 200 transactions package
      'BR00059': 450,   // 1000 transactions package
      'BR00060': 850,   // 5000 transactions package
      'BR00061': 1500,  // 20000 transactions package
      'BR00170': 1.25,  // Additional cost per transaction (200 level)
      'BR00171': 1.15,  // Additional cost per transaction (1000 level)
      'BR00172': 1.05,  // Additional cost per transaction (5000 level)
      'BR00173': 0.95   // Additional cost per transaction (20000 level)
    }
  };
}

/**
 * Creates mock e-commerce pricing data for simplified accounting
 * Used for e-commerce package testing
 */
function createMockSimplifiedAccountingEcommercePrices() {
  return {
    packages: {
      BR00090: 'BR00090', // 200 transactions
      BR00091: 'BR00091', // 1000 transactions
      BR00092: 'BR00092', // 5000 transactions
      BR00093: 'BR00093'  // 20000 transactions
    },
    additionalSkus: {
      BR00166: 'BR00166', // Additional 200 transactions
      BR00167: 'BR00167', // Additional 1000 transactions
      BR00168: 'BR00168', // Additional 5000 transactions
      BR00169: 'BR00169'  // Additional 20000 transactions
    },
    prices: {
      'BR00090': 180,   // 200 transactions package
      'BR00091': 320,   // 1000 transactions package
      'BR00092': 600,   // 5000 transactions package
      'BR00093': 1100,  // 20000 transactions package
      'BR00166': 0.90,  // Additional cost per transaction (200 level)
      'BR00167': 0.80,  // Additional cost per transaction (1000 level)
      'BR00168': 0.70,  // Additional cost per transaction (5000 level)
      'BR00169': 0.60   // Additional cost per transaction (20000 level)
    }
  };
}

// ============================================================================
// COMPREHENSIVE BUSINESS SCENARIO MOCK DATA - Simulating real-world conditions
// ============================================================================

/**
 * Creates mock data for small business scenario
 * Simulates a small business with basic accounting needs
 */
function createSmallBusinessScenario() {
  return createMockDealProperties({
    'faktury_rachunki_sprzedazowe___ile_': '7',
    'faktury_rachunki_zakupu___ile_': '3',
    'faktury_walutowe___ile_miesiecznie_': '1',
    'dokumenty_wewnetrzne_wdt__wnt_itp': '1',
    'operacje_kp_kw_walutowe': '2',
    'kp_kw___banki_': '5',
    'kp_kw_gotowka': '3',
    'rodzaj_ksiegowosci': 'Pełna księgowość',
    'jezyk_obslugi': 'Polski',
    'vat___status_podatnika': '',
    'pakiet_kadrowo_placowy': '',
    'uslugi_do_wyceny': 'Księgowość'
  });
}

/**
 * Creates mock data for medium business scenario
 * Simulates a medium business with payroll and VAT needs
 */
function createMediumBusinessScenario() {
  return createMockDealProperties({
    'faktury_rachunki_sprzedazowe___ile_': '23',
    'faktury_rachunki_zakupu___ile_': '15',
    'faktury_walutowe___ile_miesiecznie_': '3',
    'dokumenty_wewnetrzne_wdt__wnt_itp': '5',
    'operacje_kp_kw_walutowe': '8',
    'kp_kw___banki_': '12',
    'kp_kw_gotowka': '6',
    'rodzaj_ksiegowosci': 'Pełna księgowość',
    'jezyk_obslugi': 'Angielski',
    'vat___status_podatnika': 'VAT EU',
    'pakiet_kadrowo_placowy': 'Płace',
    'umowa_o_prace___liczba_osob': '5',
    'umowy_cywilnoprawne___liczba_pracownikow': '2',
    'ppk___ile_osob_': '3',
    'pfron___ile_osob_': '1',
    'a1___czy_wystepuja_': '1',
    'kto_robi_import_do_moje_ppk': 'Biuro',
    'uslugi_do_wyceny': 'Księgowość;Kadry'
  });
}

/**
 * Creates mock data for large business scenario
 * Simulates a large business with complex VAT and premium payroll
 */
function createLargeBusinessScenario() {
  return createMockDealProperties({
    'faktury_rachunki_sprzedazowe___ile_': '105',
    'faktury_rachunki_zakupu___ile_': '67',
    'faktury_walutowe___ile_miesiecznie_': '12',
    'dokumenty_wewnetrzne_wdt__wnt_itp': '8',
    'operacje_kp_kw_walutowe': '25',
    'kp_kw___banki_': '45',
    'kp_kw_gotowka': '15',
    'rodzaj_ksiegowosci': 'Pełna księgowość',
    'jezyk_obslugi': 'Polski',
    'vat___status_podatnika': 'VAT EU;VAT OSS',
    'pakiet_kadrowo_placowy': 'Kadry i płace PREMIUM',
    'umowa_o_prace___liczba_osob': '15',
    'umowy_cywilnoprawne___liczba_pracownikow': '8',
    'ppk___ile_osob_': '12',
    'pfron___ile_osob_': '3',
    'a1___czy_wystepuja_': '2',
    'kto_robi_import_do_moje_ppk': 'Klient',
    'srodki_trwale_i_wartosci_niematerialne_i_prawne___ile_': '2',
    'kasy_fiskalne___ile_': '1',
    'uslugi_do_wyceny': 'Księgowość;Kadry'
  });
}

/**
 * Creates mock data for enterprise scenario
 * Simulates an enterprise with maximum complexity
 */
function createEnterpriseScenario() {
  return createMockDealProperties({
    'faktury_rachunki_sprzedazowe___ile_': '250',
    'faktury_rachunki_zakupu___ile_': '180',
    'faktury_walutowe___ile_miesiecznie_': '25',
    'dokumenty_wewnetrzne_wdt__wnt_itp': '15',
    'operacje_kp_kw_walutowe': '50',
    'kp_kw___banki_': '80',
    'kp_kw_gotowka': '25',
    'rodzaj_ksiegowosci': 'Pełna księgowość',
    'jezyk_obslugi': 'Niemiecki',
    'vat___status_podatnika': 'VAT EU;VAT OSS;VAT 8;VAT 9M',
    'pakiet_kadrowo_placowy': 'Ryczałt',
    'umowa_o_prace___liczba_osob': '25',
    'umowy_cywilnoprawne___liczba_pracownikow': '15',
    'ppk___ile_osob_': '20',
    'pfron___ile_osob_': '5',
    'a1___czy_wystepuja_': '3',
    'kto_robi_import_do_moje_ppk': 'Biuro',
    'srodki_trwale_i_wartosci_niematerialne_i_prawne___ile_': '5',
    'kasy_fiskalne___ile_': '3',
    'pytania_do_msp': 'Biegły rewident?;Konta zespołu 5?',
    'ilosc_kont_bankowych___raporty_dzienne': '10',
    'wyciagi_bankowe___liczba_': '15',
    'liczba_kanalow_platnosci': '8',
    'ile_transakcji_sprzedazy_w_miesiacu_': '1500',
    'branza': 'E-commerce',
    'uslugi_do_wyceny': 'Księgowość;Kadry;E-commerce'
  });
}

/**
 * Creates mock data for simplified accounting scenario
 * Tests simplified accounting with different package minimums
 */
function createSimplifiedAccountingScenario() {
  return createMockDealProperties({
    'faktury_rachunki_sprzedazowe___ile_': '25',
    'faktury_rachunki_zakupu___ile_': '15',
    'faktury_walutowe___ile_miesiecznie_': '2',
    'dokumenty_wewnetrzne_wdt__wnt_itp': '3',
    'rodzaj_ksiegowosci': 'Księgowość uproszczona',
    'jezyk_obslugi': 'Polski',
    'vat___status_podatnika': '',
    'pakiet_kadrowo_placowy': 'Płace',
    'umowa_o_prace___liczba_osob': '3',
    'umowy_cywilnoprawne___liczba_pracownikow': '2',
    'uslugi_do_wyceny': 'Księgowość;Kadry'
  });
}

/**
 * Creates mock data for edge case testing
 * Tests boundary conditions and edge cases
 */
function createEdgeCaseScenarios() {
  return {
    zeroDocuments: createMockDealProperties({
      'faktury_rachunki_sprzedazowe___ile_': '0',
      'faktury_rachunki_zakupu___ile_': '0',
      'faktury_walutowe___ile_miesiecznie_': '0',
      'dokumenty_wewnetrzne_wdt__wnt_itp': '0',
      'operacje_kp_kw_walutowe': '0',
      'kp_kw___banki_': '0',
      'kp_kw_gotowka': '0',
      'rodzaj_ksiegowosci': 'Pełna księgowość',
      'uslugi_do_wyceny': 'Księgowość'
    }),

    negativeValues: createMockDealProperties({
      'faktury_rachunki_sprzedazowe___ile_': '-5',
      'faktury_rachunki_zakupu___ile_': '-3',
      'umowa_o_prace___liczba_osob': '-2',
      'umowy_cywilnoprawne___liczba_pracownikow': '-1',
      'rodzaj_ksiegowosci': 'Pełna księgowość',
      'pakiet_kadrowo_placowy': 'Płace',
      'uslugi_do_wyceny': 'Księgowość;Kadry'
    }),

    boundaryConditions: {
      silverBoundary: createMockDealProperties({
        'faktury_rachunki_sprzedazowe___ile_': '104', // Critical boundary for SILVER package bug
        'rodzaj_ksiegowosci': 'Pełna księgowość',
        'uslugi_do_wyceny': 'Księgowość'
      }),

      goldBoundary: createMockDealProperties({
        'faktury_rachunki_sprzedazowe___ile_': '110', // GOLD package minimum
        'rodzaj_ksiegowosci': 'Pełna księgowość',
        'uslugi_do_wyceny': 'Księgowość'
      }),

      platinumBoundary: createMockDealProperties({
        'faktury_rachunki_sprzedazowe___ile_': '200', // PLATINUM package minimum
        'rodzaj_ksiegowosci': 'Pełna księgowość',
        'uslugi_do_wyceny': 'Księgowość'
      })
    }
  };
}

/**
 * Creates mock data for VAT testing scenarios
 * Tests different VAT status combinations
 */
function createVatTestingScenarios() {
  return {
    vatEU: createMockDealProperties({
      'faktury_rachunki_sprzedazowe___ile_': '15',
      'rodzaj_ksiegowosci': 'Pełna księgowość',
      'vat___status_podatnika': 'VAT EU',
      'uslugi_do_wyceny': 'Księgowość'
    }),

    vatOSS: createMockDealProperties({
      'faktury_rachunki_sprzedazowe___ile_': '15',
      'rodzaj_ksiegowosci': 'Pełna księgowość',
      'vat___status_podatnika': 'VAT OSS',
      'uslugi_do_wyceny': 'Księgowość'
    }),

    multipleVAT: createMockDealProperties({
      'faktury_rachunki_sprzedazowe___ile_': '15',
      'rodzaj_ksiegowosci': 'Pełna księgowość',
      'vat___status_podatnika': 'VAT EU;VAT OSS;VAT 8;VAT 9M',
      'uslugi_do_wyceny': 'Księgowość'
    }),

    noVAT: createMockDealProperties({
      'faktury_rachunki_sprzedazowe___ile_': '15',
      'rodzaj_ksiegowosci': 'Pełna księgowość',
      'vat___status_podatnika': '',
      'uslugi_do_wyceny': 'Księgowość'
    })
  };
}

/**
 * Creates mock data for payroll package testing scenarios
 * Tests different payroll package configurations
 */
function createPayrollTestingScenarios() {
  return {
    premium: createMockDealProperties({
      'faktury_rachunki_sprzedazowe___ile_': '15',
      'rodzaj_ksiegowosci': 'Pełna księgowość',
      'pakiet_kadrowo_placowy': 'Kadry i płace PREMIUM',
      'umowa_o_prace___liczba_osob': '10',
      'umowy_cywilnoprawne___liczba_pracownikow': '5',
      'ppk___ile_osob_': '8',
      'pfron___ile_osob_': '2',
      'a1___czy_wystepuja_': '1',
      'kto_robi_import_do_moje_ppk': 'Biuro',
      'uslugi_do_wyceny': 'Księgowość;Kadry'
    }),

    payroll: createMockDealProperties({
      'faktury_rachunki_sprzedazowe___ile_': '15',
      'rodzaj_ksiegowosci': 'Pełna księgowość',
      'pakiet_kadrowo_placowy': 'Płace',
      'umowa_o_prace___liczba_osob': '8',
      'umowy_cywilnoprawne___liczba_pracownikow': '3',
      'ppk___ile_osob_': '6',
      'pfron___ile_osob_': '1',
      'a1___czy_wystepuja_': '1',
      'kto_robi_import_do_moje_ppk': 'Biuro',
      'uslugi_do_wyceny': 'Księgowość;Kadry'
    }),

    payrollWithFiles: createMockDealProperties({
      'faktury_rachunki_sprzedazowe___ile_': '15',
      'rodzaj_ksiegowosci': 'Pełna księgowość',
      'pakiet_kadrowo_placowy': 'Kadry i płace - z teczkami',
      'umowa_o_prace___liczba_osob': '6',
      'umowy_cywilnoprawne___liczba_pracownikow': '4',
      'ppk___ile_osob_': '5',
      'pfron___ile_osob_': '2',
      'a1___czy_wystepuja_': '1',
      'uslugi_do_wyceny': 'Księgowość;Kadry'
    }),

    ryczalt: createMockDealProperties({
      'faktury_rachunki_sprzedazowe___ile_': '15',
      'rodzaj_ksiegowosci': 'Pełna księgowość',
      'pakiet_kadrowo_placowy': 'Ryczałt',
      'umowa_o_prace___liczba_osob': '4',
      'umowy_cywilnoprawne___liczba_pracownikow': '2',
      'ppk___ile_osob_': '3',
      'pfron___ile_osob_': '1',
      'a1___czy_wystepuja_': '1',
      'uslugi_do_wyceny': 'Księgowość;Kadry'
    })
  };
}

/**
 * Creates mock data for MSP testing scenarios
 * Tests MSP-specific business logic
 */
function createMSPTestingScenarios() {
  return {
    mspWithAudit: createMockDealProperties({
      'faktury_rachunki_sprzedazowe___ile_': '15',
      'rodzaj_ksiegowosci': 'Pełna księgowość',
      'pytania_do_msp': 'Biegły rewident?;Inne pytania',
      'uslugi_do_wyceny': 'Księgowość;Kadry'
    }),

    mspWithMarkup: createMockDealProperties({
      'faktury_rachunki_sprzedazowe___ile_': '15',
      'faktury_rachunki_zakupu___ile_': '8',
      'faktury_walutowe___ile_miesiecznie_': '2',
      'dokumenty_wewnetrzne_wdt__wnt_itp': '3',
      'rodzaj_ksiegowosci': 'Pełna księgowość',
      'pytania_do_msp': 'Konta zespołu 5?;Inne pytania',
      'uslugi_do_wyceny': 'Księgowość;Kadry'
    }),

    mspBoth: createMockDealProperties({
      'faktury_rachunki_sprzedazowe___ile_': '15',
      'faktury_rachunki_zakupu___ile_': '8',
      'rodzaj_ksiegowosci': 'Pełna księgowość',
      'pytania_do_msp': 'Biegły rewident?;Konta zespołu 5?',
      'uslugi_do_wyceny': 'Księgowość;Kadry'
    }),

    mspSimplifiedAccounting: createMockDealProperties({
      'faktury_rachunki_sprzedazowe___ile_': '15',
      'rodzaj_ksiegowosci': 'Księgowość uproszczona',
      'pytania_do_msp': 'Biegły rewident?',
      'uslugi_do_wyceny': 'Księgowość'
    })
  };
}

/**
 * Creates mock data for e-commerce testing scenarios
 * Tests e-commerce package optimization
 */
function createEcommerceTestingScenarios() {
  return {
    lowTransactions: createMockDealProperties({
      'faktury_rachunki_sprzedazowe___ile_': '15',
      'rodzaj_ksiegowosci': 'Pełna księgowość',
      'ile_transakcji_sprzedazy_w_miesiacu_': '150',
      'branza': 'E-commerce',
      'uslugi_do_wyceny': 'Księgowość;E-commerce'
    }),

    mediumTransactions: createMockDealProperties({
      'faktury_rachunki_sprzedazowe___ile_': '15',
      'rodzaj_ksiegowosci': 'Pełna księgowość',
      'ile_transakcji_sprzedazy_w_miesiacu_': '750',
      'branza': 'E-commerce',
      'uslugi_do_wyceny': 'Księgowość;E-commerce'
    }),

    highTransactions: createMockDealProperties({
      'faktury_rachunki_sprzedazowe___ile_': '15',
      'rodzaj_ksiegowosci': 'Pełna księgowość',
      'ile_transakcji_sprzedazy_w_miesiacu_': '3500',
      'branza': 'E-commerce',
      'uslugi_do_wyceny': 'Księgowość;E-commerce'
    }),

    veryHighTransactions: createMockDealProperties({
      'faktury_rachunki_sprzedazowe___ile_': '15',
      'rodzaj_ksiegowosci': 'Pełna księgowość',
      'ile_transakcji_sprzedazy_w_miesiacu_': '15000',
      'branza': 'E-commerce',
      'uslugi_do_wyceny': 'Księgowość;E-commerce'
    })
  };
}

/**
 * Creates mock line items data for line item manager testing
 * Used for line item management testing
 */
function createMockLineItems() {
  return [
    {
      id: 'line1',
      properties: {
        hs_sku: 'BR00013',
        name: 'Banking Operations',
        quantity: '5',
        price: '15'
      }
    },
    {
      id: 'line2',
      properties: {
        hs_sku: 'BR00032',
        name: 'VAT EU',
        quantity: '1',
        price: '150'
      }
    },
    {
      id: 'line3',
      properties: {
        name: 'BR00069', // Test name-based matching
        quantity: '1',
        price: '200'
      }
    }
  ];
}

/**
 * Creates mock product data for line item manager testing
 * Used for product-based line item creation
 */
function createMockProduct() {
  return {
    id: 'product123',
    properties: {
      name: 'Test Product',
      hs_sku: 'BR00013',
      price: '15'
    }
  };
}

// ============================================================================
// MAIN TEST SUITE - Consolidated unit tests for all business logic
// ============================================================================
describe('Consolidated Business Logic Unit Tests', () => {
  const mockAccessToken = 'test-token';
  const mockDealId = '123456789';

  // Mock fetch for unit tests only
  const mockFetch = vi.fn();
  global.fetch = mockFetch;

  beforeEach(() => {
    // Clear all mocks and setup console mocking
    vi.clearAllMocks();
    vi.spyOn(console, 'log').mockImplementation(() => { });
    vi.spyOn(console, 'warn').mockImplementation(() => { });
    vi.spyOn(console, 'error').mockImplementation(() => { });

    // Reset fetch mock
    mockFetch.mockClear();

    // Setup fetch mock for this test suite only
    global.fetch = mockFetch;

    // Setup standard mock implementations
    getDealProperties.mockResolvedValue(createMockDealProperties());
    updateLineItemsForDeal.mockResolvedValue({ success: true });
    fetchPricesFromHubSpot.mockResolvedValue(createMockPrices());

    getAccountingPackagePrices.mockImplementation((isFullAccounting) => {
      if (isFullAccounting) {
        return Promise.resolve({
          packages: {
            BASE: { price: 499 },
            SILVER: { price: 764 },
            GOLD: { price: 1374 },
            PLATINUM: { price: 2000 }
          },
          individualPrices: {
            BASE: 12.9,
            SILVER: 15.3,
            GOLD: 18.7,
            PLATINUM: 22.1
          },
          additionalPackages: {
            goldPack50: { price: 100 },
            platinumPack50: { price: 120 },
            platinumPack200: { price: 400 }
          },
          prices: createMockPrices()
        });
      } else {
        // Simplified accounting pricing
        return Promise.resolve({
          packages: {
            BASE: { price: 234 },
            SILVER: { price: 294 },
            GOLD: { price: 524 },
            PLATINUM: { price: 999 }
          },
          individualPrices: {
            BASE: 12.9,
            SILVER: 15.3,
            GOLD: 18.7,
            PLATINUM: 22.1
          },
          additionalPackages: {
            goldPack50: { price: 100 },
            platinumPack50: { price: 120 },
            platinumPack200: { price: 400 }
          },
          prices: createMockPrices()
        });
      }
    });

    getPayrollPrices.mockResolvedValue({
      'BR00070': 80,
      'BR00071': 70,
      'BR00080': 50,
      'BR00165': 120
    });

    getEcommercePackagePrices.mockResolvedValue(createMockFullAccountingEcommercePrices());

    findProductBySku.mockResolvedValue(createMockProduct());
  });

  // ============================================================================
  // ACCOUNTING PACKAGES - Package selection and cost calculation logic
  // ============================================================================
  describe('Accounting Package Selection Logic', () => {
    test('should select BASE package for low document count', async () => {
      const result = await selectOptimalAccountingPackage(7, true, mockAccessToken);

      expect(result.selectedPackage).toBe('BASE');
      expect(result.packageSku).toBe('BR00003');
      expect(result.extraDocuments).toBe(2); // 7 - 5 (BASE minimum)
      expect(result.extraSku).toBe('BR00022');
    });

    test('should jump to SILVER package due to 95% rule', async () => {
      const result = await selectOptimalAccountingPackage(23, true, mockAccessToken);

      expect(result.selectedPackage).toBe('SILVER');
      expect(result.packageSku).toBe('BR00004');
      expect(result.extraDocuments).toBe(0); // 23 fits in SILVER (50)
    });

    test('should select GOLD package for high document count', async () => {
      const result = await selectOptimalAccountingPackage(105, true, mockAccessToken);

      expect(result.selectedPackage).toBe('GOLD');
      expect(result.packageSku).toBe('BR00005');
      expect(result.extraDocuments).toBe(0); // 105 fits in GOLD (110)
    });

    test('should select PLATINUM package for very high document count', async () => {
      const result = await selectOptimalAccountingPackage(250, true, mockAccessToken);

      expect(result.selectedPackage).toBe('PLATINUM');
      expect(result.packageSku).toBe('BR00006');
      expect(result.extraDocuments).toBe(50); // 250 - 200 (PLATINUM minimum)
    });

    test('should select simplified accounting BASE package correctly', async () => {
      const result = await selectOptimalAccountingPackage(8, false, mockAccessToken);

      expect(result.selectedPackage).toBe('BASE');
      expect(result.packageSku).toBe('BR00007'); // Simplified BASE
      expect(result.extraDocuments).toBe(3); // 8 - 5
    });

    test('should select simplified accounting SILVER package correctly', async () => {
      // Test with 22 documents - should stay at SILVER
      const result = await selectOptimalAccountingPackage(22, false, mockAccessToken);

      expect(result.selectedPackage).toBe('SILVER');
      expect(result.packageSku).toBe('BR00008'); // Simplified SILVER
      expect(result.extraDocuments).toBe(2); // 22 - 20
    });

    test('should handle simplified accounting SILVER package boundaries correctly', async () => {
      // Test exactly at SILVER minimum (20 documents)
      const resultAt20 = await selectOptimalAccountingPackage(20, false, mockAccessToken);
      expect(resultAt20.selectedPackage).toBe('SILVER');
      expect(resultAt20.packageSku).toBe('BR00008');
      expect(resultAt20.extraDocuments).toBe(0); // 20 - 20

      // Test just below SILVER minimum (19 documents) - should select SILVER due to 95% rule
      const resultAt19 = await selectOptimalAccountingPackage(19, false, mockAccessToken);
      expect(resultAt19.selectedPackage).toBe('SILVER');
      expect(resultAt19.packageSku).toBe('BR00008');
      expect(resultAt19.extraDocuments).toBe(0); // 19 < 20, so no extra documents needed

      // Test mid-range (40 documents) - should select GOLD due to 95% rule
      const resultAt40 = await selectOptimalAccountingPackage(40, false, mockAccessToken);
      expect(resultAt40.selectedPackage).toBe('GOLD');
      expect(resultAt40.packageSku).toBe('BR00009');
      expect(resultAt40.extraDocuments).toBe(0); // 40 < 80, so no extra documents needed
    });



    test('should calculate package cost correctly with extras', async () => {
      const cost = await calculateAccountingPackageCost('BASE', 'BR00003', 10, 'BR00022', mockAccessToken);

      // BASE (499) + 10 extra (10 * 12.9 = 129) = 628
      expect(cost).toBe(628);
    });

    test('should throw error for deprecated getAccountingPackageConfig function', () => {
      expect(() => getAccountingPackageConfig(true))
        .toThrow('getAccountingPackageConfig is deprecated');
      expect(() => getAccountingPackageConfig(false))
        .toThrow('getAccountingPackageConfig is deprecated');
    });

  });

  // ============================================================================
  // BR00013 CALCULATION - Document quantity and package-based logic
  // ============================================================================
  describe('BR00013 Document Calculation Logic', () => {
    test('should use package minimums when documents below package minimum (full accounting)', () => {
      expect(calculateBR00013QuantityForPackage(2, 'BASE', '', true)).toBe(5); // BASE minimum
      expect(calculateBR00013QuantityForPackage(30, 'SILVER', '', true)).toBe(50); // SILVER minimum
      expect(calculateBR00013QuantityForPackage(80, 'GOLD', '', true)).toBe(110); // GOLD minimum
      expect(calculateBR00013QuantityForPackage(150, 'PLATINUM', '', true)).toBe(200); // PLATINUM minimum
    });

    test('should use package minimums when documents below package minimum (simplified accounting)', () => {
      expect(calculateBR00013QuantityForPackage(2, 'BASE', '', false)).toBe(5); // BASE minimum
      expect(calculateBR00013QuantityForPackage(15, 'SILVER', '', false)).toBe(20); // SILVER minimum (simplified)
      expect(calculateBR00013QuantityForPackage(60, 'GOLD', '', false)).toBe(80); // GOLD minimum (simplified)
      expect(calculateBR00013QuantityForPackage(100, 'PLATINUM', '', false)).toBe(150); // PLATINUM minimum (simplified)
    });

    test('should use actual document count when above package minimum (full accounting)', () => {
      expect(calculateBR00013QuantityForPackage(7, 'BASE', '', true)).toBe(7);
      expect(calculateBR00013QuantityForPackage(55, 'SILVER', '', true)).toBe(55);
      expect(calculateBR00013QuantityForPackage(120, 'GOLD', '', true)).toBe(120);
      expect(calculateBR00013QuantityForPackage(250, 'PLATINUM', '', true)).toBe(250);
    });

    test('should use actual document count when above package minimum (simplified accounting)', () => {
      expect(calculateBR00013QuantityForPackage(7, 'BASE', '', false)).toBe(7);
      expect(calculateBR00013QuantityForPackage(25, 'SILVER', '', false)).toBe(25);
      expect(calculateBR00013QuantityForPackage(90, 'GOLD', '', false)).toBe(90);
      expect(calculateBR00013QuantityForPackage(200, 'PLATINUM', '', false)).toBe(200);
    });



    test('should throw error for unknown package names', () => {
      expect(() => calculateBR00013QuantityForPackage(10, 'UNKNOWN', '', true))
        .toThrow('Unknown package name: UNKNOWN. Valid packages are: BASE, SILVER, GOLD, PLATINUM');
      expect(() => calculateBR00013QuantityForPackage(2, 'INVALID', '', false))
        .toThrow('Unknown package name: INVALID. Valid packages are: BASE, SILVER, GOLD, PLATINUM');
      expect(() => calculateBR00013QuantityForPackage(10, '', '', true))
        .toThrow('Unknown package name: . Valid packages are: BASE, SILVER, GOLD, PLATINUM');
      expect(() => calculateBR00013QuantityForPackage(10, null, '', true))
        .toThrow('Unknown package name: null. Valid packages are: BASE, SILVER, GOLD, PLATINUM');
    });

    test('should handle all package boundary conditions correctly (full accounting)', () => {
      // BASE package boundaries (5-49 documents for full accounting)
      expect(calculateBR00013QuantityForPackage(4, 'BASE', '', true)).toBe(5);   // Below minimum → use minimum
      expect(calculateBR00013QuantityForPackage(5, 'BASE', '', true)).toBe(5);   // At minimum → use minimum
      expect(calculateBR00013QuantityForPackage(25, 'BASE', '', true)).toBe(25); // Above minimum → use count
      expect(calculateBR00013QuantityForPackage(49, 'BASE', '', true)).toBe(49); // Near upper boundary → use count

      // SILVER package boundaries (50-109 documents for full accounting)
      expect(calculateBR00013QuantityForPackage(49, 'SILVER', '', true)).toBe(50);  // Below minimum → use minimum
      expect(calculateBR00013QuantityForPackage(50, 'SILVER', '', true)).toBe(50);  // At minimum → use minimum
      expect(calculateBR00013QuantityForPackage(75, 'SILVER', '', true)).toBe(75);  // Mid-range → use count
      expect(calculateBR00013QuantityForPackage(103, 'SILVER', '', true)).toBe(103); // Just before bug threshold → use count
      expect(calculateBR00013QuantityForPackage(104, 'SILVER', '', true)).toBe(104); // At bug threshold → should use count (will fail due to bug)
      expect(calculateBR00013QuantityForPackage(105, 'SILVER', '', true)).toBe(105); // Above bug threshold → should use count (will fail due to bug)
      expect(calculateBR00013QuantityForPackage(109, 'SILVER', '', true)).toBe(109); // Near upper boundary → should use count (will fail due to bug)

      // GOLD package boundaries (110-199 documents for full accounting)
      expect(calculateBR00013QuantityForPackage(109, 'GOLD', '', true)).toBe(110); // Below minimum → use minimum
      expect(calculateBR00013QuantityForPackage(110, 'GOLD', '', true)).toBe(110); // At minimum → use minimum
      expect(calculateBR00013QuantityForPackage(150, 'GOLD', '', true)).toBe(150); // Mid-range → use count
      expect(calculateBR00013QuantityForPackage(199, 'GOLD', '', true)).toBe(199); // Near upper boundary → use count

      // PLATINUM package boundaries (200+ documents for full accounting)
      expect(calculateBR00013QuantityForPackage(199, 'PLATINUM', '', true)).toBe(200); // Below minimum → use minimum
      expect(calculateBR00013QuantityForPackage(200, 'PLATINUM', '', true)).toBe(200); // At minimum → use minimum
      expect(calculateBR00013QuantityForPackage(300, 'PLATINUM', '', true)).toBe(300); // Above minimum → use count
      expect(calculateBR00013QuantityForPackage(1000, 'PLATINUM', '', true)).toBe(1000); // High count → use count
    });

    test('should handle all package boundary conditions correctly (simplified accounting)', () => {
      // BASE package boundaries (5-19 documents for simplified accounting)
      expect(calculateBR00013QuantityForPackage(4, 'BASE', '', false)).toBe(5);   // Below minimum → use minimum
      expect(calculateBR00013QuantityForPackage(5, 'BASE', '', false)).toBe(5);   // At minimum → use minimum
      expect(calculateBR00013QuantityForPackage(15, 'BASE', '', false)).toBe(15); // Above minimum → use count
      expect(calculateBR00013QuantityForPackage(19, 'BASE', '', false)).toBe(19); // Near upper boundary → use count

      // SILVER package boundaries (20-79 documents for simplified accounting)
      expect(calculateBR00013QuantityForPackage(19, 'SILVER', '', false)).toBe(20);  // Below minimum → use minimum
      expect(calculateBR00013QuantityForPackage(20, 'SILVER', '', false)).toBe(20);  // At minimum → use minimum
      expect(calculateBR00013QuantityForPackage(50, 'SILVER', '', false)).toBe(50);  // Mid-range → use count
      expect(calculateBR00013QuantityForPackage(79, 'SILVER', '', false)).toBe(79);  // Near upper boundary → use count

      // GOLD package boundaries (80-149 documents for simplified accounting)
      expect(calculateBR00013QuantityForPackage(79, 'GOLD', '', false)).toBe(80); // Below minimum → use minimum
      expect(calculateBR00013QuantityForPackage(80, 'GOLD', '', false)).toBe(80); // At minimum → use minimum
      expect(calculateBR00013QuantityForPackage(120, 'GOLD', '', false)).toBe(120); // Mid-range → use count
      expect(calculateBR00013QuantityForPackage(149, 'GOLD', '', false)).toBe(149); // Near upper boundary → use count

      // PLATINUM package boundaries (150+ documents for simplified accounting)
      expect(calculateBR00013QuantityForPackage(149, 'PLATINUM', '', false)).toBe(150); // Below minimum → use minimum
      expect(calculateBR00013QuantityForPackage(150, 'PLATINUM', '', false)).toBe(150); // At minimum → use minimum
      expect(calculateBR00013QuantityForPackage(200, 'PLATINUM', '', false)).toBe(200); // Above minimum → use count
      expect(calculateBR00013QuantityForPackage(500, 'PLATINUM', '', false)).toBe(500); // High count → use count
    });

    test('should detect inconsistencies between package selection and BR00013 calculation (full accounting)', () => {
      // This test specifically targets the bug where BR00013 calculation doesn't match package selection
      // Test the critical boundary where SILVER package should handle 104-109 documents

      // These should all use document count, not jump to GOLD minimum
      const criticalSilverRange = [104, 105, 106, 107, 108, 109];

      criticalSilverRange.forEach(docCount => {
        const result = calculateBR00013QuantityForPackage(docCount, 'SILVER', '', true);
        // For SILVER package, if document count > 50, should use document count, not GOLD minimum (110)
        expect(result).toBe(docCount); // This will fail due to the bug at 104+
      });
    });

    test('should correctly differentiate between full and simplified accounting in BR00013 calculation', () => {
      // Test SILVER package with 25 documents
      const fullAccountingResult = calculateBR00013QuantityForPackage(25, 'SILVER', '', true);
      const simplifiedAccountingResult = calculateBR00013QuantityForPackage(25, 'SILVER', '', false);

      expect(fullAccountingResult).toBe(50); // Full accounting SILVER minimum is 50
      expect(simplifiedAccountingResult).toBe(25); // Simplified accounting SILVER minimum is 20, so use document count

      // Test SILVER package with 15 documents
      const fullAccountingResult15 = calculateBR00013QuantityForPackage(15, 'SILVER', '', true);
      const simplifiedAccountingResult15 = calculateBR00013QuantityForPackage(15, 'SILVER', '', false);

      expect(fullAccountingResult15).toBe(50); // Full accounting SILVER minimum is 50
      expect(simplifiedAccountingResult15).toBe(20); // Simplified accounting SILVER minimum is 20
    });

    test('should handle edge cases for simplified accounting package minimums', () => {
      // Test all packages at their exact minimums for simplified accounting
      expect(calculateBR00013QuantityForPackage(5, 'BASE', '', false)).toBe(5);
      expect(calculateBR00013QuantityForPackage(20, 'SILVER', '', false)).toBe(20);
      expect(calculateBR00013QuantityForPackage(80, 'GOLD', '', false)).toBe(80);
      expect(calculateBR00013QuantityForPackage(150, 'PLATINUM', '', false)).toBe(150);

      // Test all packages just below their minimums for simplified accounting
      expect(calculateBR00013QuantityForPackage(4, 'BASE', '', false)).toBe(5);
      expect(calculateBR00013QuantityForPackage(19, 'SILVER', '', false)).toBe(20);
      expect(calculateBR00013QuantityForPackage(79, 'GOLD', '', false)).toBe(80);
      expect(calculateBR00013QuantityForPackage(149, 'PLATINUM', '', false)).toBe(150);
    });
  });

  // ============================================================================
  // DYNAMIC PRICING TESTS - Verify 95% rule and dynamic maxExtra calculation
  // ============================================================================
  describe('Dynamic Pricing and 95% Rule Logic', () => {
    test('should calculate maxExtra dynamically based on 95% rule', async () => {
      // This test verifies that maxExtra is calculated dynamically rather than hardcoded
      // We can't test the exact values without knowing the real prices, but we can test the behavior

      // Test that packages are selected optimally based on pricing
      const result = await selectOptimalAccountingPackage(23, true, mockAccessToken);

      // With our mock prices, this should trigger the 95% rule and jump to SILVER
      expect(result.selectedPackage).toBe('SILVER');
      expect(result.totalCost).toBeDefined();
      expect(result.items).toBeDefined();
      expect(result.items.length).toBeGreaterThan(0);
    });

    test('should handle simplified accounting with dynamic pricing', async () => {
      // Test that simplified accounting uses correct package sizes and pricing
      const baseResult = await selectOptimalAccountingPackage(8, false, mockAccessToken);
      const silverResult = await selectOptimalAccountingPackage(25, false, mockAccessToken);

      expect(baseResult.selectedPackage).toBe('BASE');
      expect(baseResult.packageSku).toBe('BR00007'); // Simplified BASE

      expect(silverResult.selectedPackage).toBe('SILVER');
      expect(silverResult.packageSku).toBe('BR00008'); // Simplified SILVER
      expect(silverResult.extraDocuments).toBe(5); // 25 - 20 (simplified SILVER minimum)
    });

    test('should throw error when pricing data is invalid', async () => {
      // Mock invalid pricing data
      const originalMock = getAccountingPackagePrices;
      getAccountingPackagePrices.mockResolvedValueOnce(null);

      await expect(selectOptimalAccountingPackage(10, true, mockAccessToken))
        .rejects.toThrow();

      // Restore original mock
      getAccountingPackagePrices.mockImplementation(originalMock);
    });
  });

  // ============================================================================
  // DOCUMENT CALCULATION - Sum calculations and accounting type detection
  // ============================================================================
  describe('Document Calculation Logic', () => {
    test('should calculate document sums correctly', () => {
      const dealProperties = createMockDealProperties({
        'operacje_kp_kw_walutowe': '5',
        'kp_kw___banki_': '10',
        'kp_kw_gotowka': '3',
        'faktury_rachunki_sprzedazowe___ile_': '7',
        'faktury_rachunki_zakupu___ile_': '3',
        'faktury_walutowe___ile_miesiecznie_': '2',
        'dokumenty_wewnetrzne_wdt__wnt_itp': '1'
      });

      const cashBankSum = calculateDocumentSum(dealProperties, DOCUMENT_FIELDS.CASH_BANK_OPERATIONS);
      const bookingsSum = calculateDocumentSum(dealProperties, DOCUMENT_FIELDS.BOOKING_OPERATIONS);

      expect(cashBankSum).toBe(18); // 5 + 10 + 3
      expect(bookingsSum).toBe(13); // 7 + 3 + 2 + 1
    });

    test('should detect accounting types correctly', () => {
      expect(isFullAccountingType('Pełna księgowość')).toBe(true);
      expect(isFullAccountingType('Pełna księgowość + biegły rewident')).toBe(true);
      expect(isFullAccountingType('Fundacje rodzinne')).toBe(true);
      expect(isFullAccountingType('Księgowość uproszczona')).toBe(false);
      expect(isFullAccountingType('')).toBe(false);
    });

    test('should calculate base document quantity correctly (package selection uses bookings only)', () => {
      expect(calculateBaseDocumentQuantity(18, 12, true)).toBe(12); // bookings only for package selection
      expect(calculateBaseDocumentQuantity(18, 12, false)).toBe(12); // bookings only for package selection
      expect(calculateBaseDocumentQuantity(0, 0, true)).toBe(0);
    });

    test('should calculate BR00013 document quantity correctly (uses max of bookings or cash-bank)', () => {
      expect(calculateBR00013DocumentQuantity(18, 12, true)).toBe(18); // max for BR00013 calculation
      expect(calculateBR00013DocumentQuantity(18, 12, false)).toBe(18); // max for BR00013 calculation
      expect(calculateBR00013DocumentQuantity(12, 18, true)).toBe(18); // max for BR00013 calculation
      expect(calculateBR00013DocumentQuantity(0, 0, true)).toBe(0);
    });

    test('should handle empty/null/undefined document values as 0', () => {
      const dealProperties = createMockDealProperties({
        'faktury_rachunki_sprzedazowe___ile_': '',
        'faktury_rachunki_zakupu___ile_': null,
        'faktury_walutowe___ile_miesiecznie_': undefined,
        'dokumenty_wewnetrzne_wdt__wnt_itp': '0'
      });

      const sum = calculateDocumentSum(dealProperties, DOCUMENT_FIELDS.BOOKING_OPERATIONS);
      expect(sum).toBe(0);
    });

    test('should throw error for invalid document values', () => {
      const dealProperties = createMockDealProperties({
        'faktury_rachunki_sprzedazowe___ile_': '5',
        'faktury_rachunki_zakupu___ile_': '3',
        'faktury_walutowe___ile_miesiecznie_': '2',
        'dokumenty_wewnetrzne_wdt__wnt_itp': 'invalid'
      });

      expect(() => {
        calculateDocumentSum(dealProperties, DOCUMENT_FIELDS.BOOKING_OPERATIONS);
      }).toThrow('Invalid numeric value for field \'dokumenty_wewnetrzne_wdt__wnt_itp\': invalid');
    });
  });

  // ============================================================================
  // BR00013 EMPLOYEE COUNT ADDITION - New functionality tests
  // ============================================================================
  describe('BR00013 Employee Count Addition Logic', () => {
    test('should add employee counts to BR00013 when kadry is selected and full accounting is present', async () => {
      const mockProperties = createMockDealProperties({
        'rodzaj_ksiegowosci': 'Pełna księgowość',
        'uslugi_do_wyceny': 'Księgowość;Kadry',
        'faktury_rachunki_sprzedazowe___ile_': '10',
        'faktury_rachunki_zakupu___ile_': '5',
        'umowa_o_prace___liczba_osob': '8',
        'umowy_cywilnoprawne___liczba_pracownikow': '3'
      });

      getDealProperties.mockResolvedValue(mockProperties);

      const result = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      // New BR00013 calculation logic: max of Sum A (invoices+employees) or Sum B (cash-bank)
      // Sum A: 10 (faktury_sprzedazowe) + 5 (faktury_zakupu) + 0 (faktury_walutowe) + 0 (dokumenty_wewnetrzne) + 8 (umowa_o_prace) + 3 (umowy_cywilnoprawne) = 26
      // Sum B: 0 (kp_kw_banki) + 0 (kp_kw_gotowka) + 0 (operacje_walutowe) = 0
      // BR00013 document quantity = max(26, 0) = 26
      // With BASE package minimum (5), BR00013 quantity = max(26, 5) = 26
      expect(result.br00013Quantity).toBe(26);
    });

    test('should not add employee counts when Księgowość is not selected', async () => {
      const mockProperties = createMockDealProperties({
        'rodzaj_ksiegowosci': 'Pełna księgowość',
        'uslugi_do_wyceny': 'Kadry;Inne usługi', // No Księgowość
        'faktury_rachunki_sprzedazowe___ile_': '10',
        'faktury_rachunki_zakupu___ile_': '5',
        'umowa_o_prace___liczba_osob': '8',
        'umowy_cywilnoprawne___liczba_pracownikow': '3'
      });

      getDealProperties.mockResolvedValue(mockProperties);

      const result = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      // When Księgowość is not in uslugi_do_wyceny, accounting services are not active
      // BR00013 quantity should be 0 (no accounting packages)
      expect(result.br00013Quantity).toBe(0);
    });

    test('should not add employee counts when Pełna księgowość is not selected', async () => {
      const mockProperties = createMockDealProperties({
        'rodzaj_ksiegowosci': 'Uproszczona księgowość', // Not Pełna księgowość
        'uslugi_do_wyceny': 'Księgowość;Kadry', // Księgowość is selected
        'faktury_rachunki_sprzedazowe___ile_': '10',
        'faktury_rachunki_zakupu___ile_': '5',
        'umowa_o_prace___liczba_osob': '8',
        'umowy_cywilnoprawne___liczba_pracownikow': '3'
      });

      getDealProperties.mockResolvedValue(mockProperties);

      const result = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      // BR00013 should NOT be added for simplified accounting (empty accounting type)
      // Only full accounting types get BR00013: "Pełna księgowość", "Fundacje rodzinne", "Fundacje non profit i NGO"
      expect(result.br00013Quantity).toBe(0);
    });

    test('should not add employee counts when simplified accounting is used', async () => {
      const mockProperties = createMockDealProperties({
        'rodzaj_ksiegowosci': 'Uproszczona księgowość',
        'uslugi_do_wyceny': 'Księgowość;Kadry',
        'faktury_rachunki_sprzedazowe___ile_': '10',
        'faktury_rachunki_zakupu___ile_': '5',
        'umowa_o_prace___liczba_osob': '8',
        'umowy_cywilnoprawne___liczba_pracownikow': '3'
      });

      getDealProperties.mockResolvedValue(mockProperties);

      const result = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      // BR00013 should NOT be added for simplified accounting ("Uproszczona księgowość")
      // Only full accounting types get BR00013: "Pełna księgowość", "Fundacje rodzinne", "Fundacje non profit i NGO"
      expect(result.br00013Quantity).toBe(0);
    });

    test('should handle zero employee counts correctly', async () => {
      const mockProperties = createMockDealProperties({
        'rodzaj_ksiegowosci': 'Pełna księgowość',
        'uslugi_do_wyceny': 'Księgowość;Kadry',
        'faktury_rachunki_sprzedazowe___ile_': '10',
        'faktury_rachunki_zakupu___ile_': '5',
        'umowa_o_prace___liczba_osob': '0',
        'umowy_cywilnoprawne___liczba_pracownikow': '0'
      });

      getDealProperties.mockResolvedValue(mockProperties);

      const result = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      // New BR00013 calculation logic: max of Sum A (invoices+employees) or Sum B (cash-bank)
      // Sum A: 10 + 5 + 0 + 0 + 0 + 0 = 15 (zero employees)
      // Sum B: 0 + 0 + 0 = 0
      // BR00013 document quantity = max(15, 0) = 15
      // With BASE package minimum (5), BR00013 quantity = max(15, 5) = 15
      expect(result.br00013Quantity).toBe(15);
    });

    test('should add employee counts with MSP markup scenario', async () => {
      const mockProperties = createMockDealProperties({
        'rodzaj_ksiegowosci': 'Pełna księgowość',
        'uslugi_do_wyceny': 'Księgowość;Kadry',
        'pytania_do_msp': 'Konta zespołu 5?',
        'faktury_rachunki_sprzedazowe___ile_': '20',
        'faktury_rachunki_zakupu___ile_': '10',
        'umowa_o_prace___liczba_osob': '5',
        'umowy_cywilnoprawne___liczba_pracownikow': '2'
      });

      getDealProperties.mockResolvedValue(mockProperties);

      const result = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      // New BR00013 calculation logic with MSP: max of Sum A (invoices+employees) or Sum B (cash-bank)
      // Sum A: 20 + 10 + 0 + 0 + 5 + 2 = 37 (employees included in calculation)
      // Sum B: 0 + 0 + 0 = 0
      // BR00013 document quantity = max(37, 0) = 37
      // MSP markup for package selection: 30 (bookings only) * 1.5 = 45 → SILVER package
      // With SILVER package minimum (50), BR00013 quantity = max(37, 50) = 50
      // Note: MSP uses base value for BR00013 calculation, not the markup
      expect(result.br00013Quantity).toBe(50);
    });
  });

  // ============================================================================
  // BANKING FIELD COMBINATION LOGIC - New functionality tests
  // ============================================================================
  describe('Banking Field Combination Logic', () => {
    test('should sum payment channels with monthly reports in full accounting', async () => {
      const mockProperties = createMockDealProperties({
        'rodzaj_ksiegowosci': 'Pełna księgowość',
        'ilosc_kont_bankowych___raporty_miesieczne': '3',
        'ile_kanalow_platniczych_': '2',
        'faktury_rachunki_sprzedazowe___ile_': '10'
      });

      getDealProperties.mockResolvedValue(mockProperties);

      const result = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      // Full accounting: monthly reports (3) + payment channels (2) = 5
      expect(result.br00012Quantity).toBe(5);
    });

    test('should ensure monthly reports is at least 1 in full accounting', async () => {
      const mockProperties = createMockDealProperties({
        'rodzaj_ksiegowosci': 'Pełna księgowość',
        'ilosc_kont_bankowych___raporty_miesieczne': '0',
        'ile_kanalow_platniczych_': '2',
        'faktury_rachunki_sprzedazowe___ile_': '10'
      });

      getDealProperties.mockResolvedValue(mockProperties);

      const result = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      // Full accounting: monthly reports (min 1) + payment channels (2) = 3
      expect(result.br00012Quantity).toBe(3);
    });

    test('should ignore payment channels in simplified accounting', async () => {
      const mockProperties = createMockDealProperties({
        'rodzaj_ksiegowosci': 'Uproszczona księgowość',
        'ilosc_kont_bankowych___raporty_miesieczne': '3',
        'ile_kanalow_platniczych_': '5', // Should be ignored
        'faktury_rachunki_sprzedazowe___ile_': '10'
      });

      getDealProperties.mockResolvedValue(mockProperties);

      const result = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      // BR00012 should NOT be added for simplified accounting ("Uproszczona księgowość")
      // Only full accounting types get BR00012: "Pełna księgowość", "Fundacje rodzinne", "Fundacje non profit i NGO"
      expect(result.br00012Quantity).toBe(0);
    });

    test('should handle zero values correctly in both accounting types', async () => {
      // Test full accounting with zeros
      const fullAccountingProperties = createMockDealProperties({
        'rodzaj_ksiegowosci': 'Pełna księgowość',
        'ilosc_kont_bankowych___raporty_miesieczne': '0',
        'ile_kanalow_platniczych_': '0',
        'faktury_rachunki_sprzedazowe___ile_': '10'
      });

      getDealProperties.mockResolvedValue(fullAccountingProperties);

      const fullResult = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      // Full accounting: monthly reports (min 1) + payment channels (0) = 1
      expect(fullResult.br00012Quantity).toBe(1);

      // Test simplified accounting with zeros
      const simplifiedAccountingProperties = createMockDealProperties({
        'rodzaj_ksiegowosci': 'Uproszczona księgowość',
        'ilosc_kont_bankowych___raporty_miesieczne': '0',
        'ile_kanalow_platniczych_': '0',
        'faktury_rachunki_sprzedazowe___ile_': '10'
      });

      getDealProperties.mockResolvedValue(simplifiedAccountingProperties);

      const simplifiedResult = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      // Simplified accounting: only monthly reports (0), no minimum requirement
      expect(simplifiedResult.br00012Quantity).toBe(0);
    });

    test('should handle high values correctly', async () => {
      const mockProperties = createMockDealProperties({
        'rodzaj_ksiegowosci': 'Pełna księgowość',
        'ilosc_kont_bankowych___raporty_miesieczne': '10',
        'ile_kanalow_platniczych_': '15',
        'faktury_rachunki_sprzedazowe___ile_': '10'
      });

      getDealProperties.mockResolvedValue(mockProperties);

      const result = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      // Full accounting: monthly reports (10) + payment channels (15) = 25
      expect(result.br00012Quantity).toBe(25);
    });
  });

  // ============================================================================
  // PAYROLL PACKAGES - Employee management and package logic
  // ============================================================================
  describe('Payroll Package Logic', () => {
    let mockResult;

    beforeEach(() => {
      mockResult = createMockPayrollResult();
    });

    test('should handle PREMIUM package correctly', async () => {
      const dealProperties = createMockDealProperties({
        'pakiet_kadrowo_placowy': 'Kadry i płace PREMIUM',
        'umowa_o_prace___liczba_osob': '5',
        'umowy_cywilnoprawne___liczba_pracownikow': '3',
        'ppk___ile_osob_': '2',
        'pfron___ile_osob_': '1',
        'a1___czy_wystepuja_': '1',
        'kto_robi_import_do_moje_ppk': 'Biuro',
        'dodatkowe_skladniki_wynagrodzenia': '2'
      });

      const result = await processPayrollPackage(mockResult, dealProperties, 'test-deal', mockPayrollPrices);

      expect(result.br00070Quantity).toBe(5); // Employment contracts
      expect(result.br00071Quantity).toBe(3); // Civil contracts
      expect(result.br00077Quantity).toBe(0); // PPK not added for PREMIUM
      expect(result.br00080Quantity).toBe(1); // PFRON
      expect(result.br00165Quantity).toBe(1); // A1 certificates
      expect(result.shouldHaveBR00078).toBe(false); // No PPK presence for PREMIUM
      expect(result.shouldHaveBR00079).toBe(false); // No Biuro import for PREMIUM
    });

    test('should handle regular Płace package correctly', async () => {
      const dealProperties = createMockDealProperties({
        'pakiet_kadrowo_placowy': 'Płace',
        'umowa_o_prace___liczba_osob': '3',
        'umowy_cywilnoprawne___liczba_pracownikow': '2',
        'ppk___ile_osob_': '2',
        'pfron___ile_osob_': '1',
        'a1___czy_wystepuja_': '1',
        'kto_robi_import_do_moje_ppk': 'Biuro',
        'dodatkowe_skladniki_wynagrodzenia': '3'
      });

      const result = await processPayrollPackage(mockResult, dealProperties, 'test-deal', mockPayrollPrices);

      expect(result.br00072Quantity).toBe(3); // Employment contracts
      expect(result.br00073Quantity).toBe(2); // Civil contracts
      expect(result.br00077Quantity).toBe(2); // PPK allowed for non-PREMIUM
      expect(result.br00080Quantity).toBe(1); // PFRON
      expect(result.br00165Quantity).toBe(1); // A1 certificates
      expect(result.shouldHaveBR00078).toBe(true); // PPK presence
      expect(result.shouldHaveBR00079).toBe(true); // Biuro import
    });

    test('should handle Kadry i płace - z teczkami correctly', async () => {
      const dealProperties = createMockDealProperties({
        'pakiet_kadrowo_placowy': 'Kadry i płace - z teczkami',
        'umowa_o_prace___liczba_osob': '4',
        'umowy_cywilnoprawne___liczba_pracownikow': '3',
        'ppk___ile_osob_': '3',
        'pfron___ile_osob_': '2',
        'a1___czy_wystepuja_': '1',
        'dodatkowe_skladniki_wynagrodzenia': '4',
        'kto_robi_import_do_moje_ppk': 'Biuro'
      });

      const result = await processPayrollPackage(mockResult, dealProperties, 'test-deal', mockPayrollPrices);

      expect(result.br00072Quantity).toBe(4); // Employment contracts
      expect(result.br00073Quantity).toBe(3); // Civil contracts
      expect(result.br00074Quantity).toBe(7); // Sum of employment + civil (4 + 3)
      expect(result.br00077Quantity).toBe(3); // PPK
      expect(result.br00080Quantity).toBe(2); // PFRON
      expect(result.br00165Quantity).toBe(1); // A1 certificates
    });

    test('should handle Ryczałt package correctly', async () => {
      const dealProperties = createMockDealProperties({
        'pakiet_kadrowo_placowy': 'Ryczałt',
        'umowa_o_prace___liczba_osob': '2',
        'umowy_cywilnoprawne___liczba_pracownikow': '1',
        'ppk___ile_osob_': '1',
        'pfron___ile_osob_': '1',
        'a1___czy_wystepuja_': '1',
        'dodatkowe_skladniki_wynagrodzenia': '1',
        'kto_robi_import_do_moje_ppk': 'Biuro'
      });

      const result = await processPayrollPackage(mockResult, dealProperties, 'test-deal', mockPayrollPrices);

      expect(result.br00069Quantity).toBe(1); // Always quantity 1 for Ryczałt
      expect(result.br00069OverridePrice).toBe(440); // (2×80 + 1×70 + 1×50 + 1×120) × 1.1 = 400 × 1.1 = 440
      expect(result.br00070Quantity).toBe(0); // Cleared for Ryczałt
      expect(result.br00071Quantity).toBe(0); // Cleared for Ryczałt
      expect(result.br00080Quantity).toBe(0); // PFRON cleared for Ryczałt
      expect(result.br00165Quantity).toBe(0); // A1 included in BR00069 for Ryczałt
    });

    test('should clear all quantities when no package selected', async () => {
      const dealProperties = createMockDealProperties({
        'pakiet_kadrowo_placowy': '',
        'umowa_o_prace___liczba_osob': '5',
        'umowy_cywilnoprawne___liczba_pracownikow': '3',
        'dodatkowe_skladniki_wynagrodzenia': '',
        'kto_robi_import_do_moje_ppk': ''
      });

      const result = await processPayrollPackage(mockResult, dealProperties, 'test-deal', mockPayrollPrices);

      expect(result.br00069Quantity).toBe(0);
      expect(result.br00070Quantity).toBe(0);
      expect(result.br00071Quantity).toBe(0);
      expect(result.br00072Quantity).toBe(0);
      expect(result.br00073Quantity).toBe(0);
      expect(result.br00074Quantity).toBe(0);
      expect(result.br00077Quantity).toBe(0);
      expect(result.br00080Quantity).toBe(0);
      expect(result.br00165Quantity).toBe(0);
    });



    test('should have correct payroll configuration constants', () => {
      expect(PAYROLL_CONFIG.PACKAGES.PREMIUM).toBe('Kadry i płace PREMIUM');
      expect(PAYROLL_CONFIG.PACKAGES.PAYROLL).toBe('Płace');
      expect(PAYROLL_CONFIG.PACKAGES.PAYROLL_WITH_FILES).toBe('Kadry i płace - z teczkami');
      expect(PAYROLL_CONFIG.PACKAGES.RYCZALT).toBe('Ryczałt');
      expect(Array.isArray(PAYROLL_CONFIG.REQUIRED_PROPERTIES)).toBe(true);
    });
  });

  // ============================================================================
  // VAT AND LANGUAGE PROCESSING - Status validation and language handling
  // ============================================================================
  describe('VAT Status Processing', () => {
    test('should handle individual VAT statuses correctly', () => {
      expect(processVatStatus('VAT EU')).toEqual({
        shouldHaveBR00032: true,
        shouldHaveBR00033: false,
        br00032Quantity: 1,
        br00033Quantity: 0
      });

      expect(processVatStatus('VAT OSS')).toEqual({
        shouldHaveBR00032: false,
        shouldHaveBR00033: true,
        br00032Quantity: 0,
        br00033Quantity: 1
      });

      expect(processVatStatus('VAT 8')).toEqual({
        shouldHaveBR00032: true,
        shouldHaveBR00033: false,
        br00032Quantity: 1,
        br00033Quantity: 0
      });

      expect(processVatStatus('VAT 9M')).toEqual({
        shouldHaveBR00032: true,
        shouldHaveBR00033: false,
        br00032Quantity: 1,
        br00033Quantity: 0
      });
    });

    test('should handle combined VAT statuses correctly', () => {
      const result = processVatStatus('VAT EU;VAT OSS;VAT INNE KRAJE;VAT 8');

      expect(result.shouldHaveBR00032).toBe(true);
      expect(result.shouldHaveBR00033).toBe(true);
      expect(result.br00032Quantity).toBe(2); // VAT EU + VAT 8
      expect(result.br00033Quantity).toBe(1); // VAT OSS
    });

    test('should handle multiple BR00032 VAT statuses correctly', () => {
      const result = processVatStatus('VAT EU;VAT 8;VAT 9M');

      expect(result.shouldHaveBR00032).toBe(true);
      expect(result.shouldHaveBR00033).toBe(false);
      expect(result.br00032Quantity).toBe(3); // VAT EU + VAT 8 + VAT 9M
      expect(result.br00033Quantity).toBe(0);
    });

    test('should handle empty and invalid VAT statuses', () => {
      expect(processVatStatus('')).toEqual({
        shouldHaveBR00032: false,
        shouldHaveBR00033: false,
        br00032Quantity: 0,
        br00033Quantity: 0
      });

      expect(processVatStatus('INVALID_STATUS')).toEqual({
        shouldHaveBR00032: false,
        shouldHaveBR00033: false,
        br00032Quantity: 0,
        br00033Quantity: 0
      });
    });

    test('should handle malformed VAT status with double semicolons', () => {
      const result = processVatStatus('VAT EU;;VAT OSS');

      expect(result.shouldHaveBR00032).toBe(true);
      expect(result.shouldHaveBR00033).toBe(true);
      expect(result.br00032Quantity).toBe(1);
      expect(result.br00033Quantity).toBe(1);
    });
  });

  describe('Language Processing', () => {
    test('should handle Polish language correctly', () => {
      expect(processLanguageProperty('Polski')).toEqual({
        shouldHaveBR00129: false
      });
    });

    test('should handle non-Polish languages correctly', () => {
      const nonPolishLanguages = ['Angielski', 'Niemiecki', 'Francuski', 'Hiszpański', 'Ukraiński'];

      nonPolishLanguages.forEach(language => {
        expect(processLanguageProperty(language)).toEqual({
          shouldHaveBR00129: true
        });
      });
    });

    test('should handle case sensitivity correctly', () => {
      expect(processLanguageProperty('polski')).toEqual({
        shouldHaveBR00129: true // Case-sensitive, 'polski' !== 'Polski'
      });

      expect(processLanguageProperty('POLSKI')).toEqual({
        shouldHaveBR00129: true // Case-sensitive, 'POLSKI' !== 'Polski'
      });
    });

    test('should handle empty or null values as non-Polish', () => {
      expect(processLanguageProperty('')).toEqual({ shouldHaveBR00129: true });
      expect(processLanguageProperty(null)).toEqual({ shouldHaveBR00129: true });
      expect(processLanguageProperty(undefined)).toEqual({ shouldHaveBR00129: true });
      expect(processLanguageProperty('   ')).toEqual({ shouldHaveBR00129: true });
    });

    test('should handle partial matches as non-Polish', () => {
      expect(processLanguageProperty('Polski + Angielski')).toEqual({ shouldHaveBR00129: true });
      expect(processLanguageProperty('Angielski/Polski')).toEqual({ shouldHaveBR00129: true });
      expect(processLanguageProperty('Język polski')).toEqual({ shouldHaveBR00129: true });
      expect(processLanguageProperty('Polish')).toEqual({ shouldHaveBR00129: true });
    });

    test('should handle special characters correctly', () => {
      const specialLanguages = ['Français', 'Español', 'Português', 'Русский', 'Українська', '中文', 'العربية'];

      specialLanguages.forEach(language => {
        expect(processLanguageProperty(language)).toEqual({ shouldHaveBR00129: true });
      });
    });
  });

  // ============================================================================
  // MSP SETTINGS - "Konta zespołu 5?" base value calculation functionality
  // ============================================================================
  describe('MSP Settings - Konta zespołu 5? Base Value Calculation', () => {
    test('should calculate BR00013 from base value when "Konta zespołu 5?" is present in MSP field', async () => {
      const mockProperties = createMockDealProperties({
        // Set up document counts for calculation
        'faktury_rachunki_sprzedazowe___ile_': '10',
        'faktury_rachunki_zakupu___ile_': '8',
        'faktury_walutowe___ile_miesiecznie_': '2',
        'dokumenty_wewnetrzne_wdt__wnt_itp': '4',
        'rodzaj_ksiegowosci': 'Pełna księgowość',
        // MSP field with "Konta zespołu 5?" should trigger package selection based on 50% markup but BR00013 from base value
        'pytania_do_msp': 'Biegły rewident?;Konta zespołu 5?;Inne pytania',
        // IMPORTANT: Include "Kadry" in services to prevent field clearing
        'uslugi_do_wyceny': 'Księgowość;Kadry;Inne usługi'
      });
      getDealProperties.mockResolvedValue(mockProperties);

      const result = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      expect(result).toBeDefined();
      expect(result.completeRecalculationPerformed).toBe(true);

      // Calculate expected values
      const bookingsSum = 10 + 8 + 2 + 4; // 24 documents (base value)
      const expectedMarkup = Math.ceil(bookingsSum * 1.5); // 24 * 1.5 = 36 (for package selection only)

      // BR00013 should be calculated from base value (24), not markup (36)
      // Package selection uses markup (36) → SILVER package → max(24, 50) = 50
      expect(result.br00013Quantity).toBe(50); // Should use SILVER minimum with base value (24)
      expect(result.baseDocumentQuantity).toBe(bookingsSum); // Should be base value, not markup

      // MSP audit should also be enabled
      expect(result.shouldHaveBR00111).toBe(true);

      // Verify that MSP audit is properly handled in comprehensive updates
      expect(result.completeRecalculationPerformed).toBe(true);

      console.log('MSP base value calculation test result:', {
        originalBookingsSum: bookingsSum,
        markupForPackageSelection: expectedMarkup,
        actualBR00013: result.br00013Quantity,
        baseDocumentQuantity: result.baseDocumentQuantity,
        shouldHaveBR00111: result.shouldHaveBR00111,
        selectedPackageName: result.selectedPackageName,
        explanation: `BR00013 = max(${bookingsSum}, SILVER_minimum_50) = ${result.br00013Quantity} (calculated from base value, not markup)`
      });
    });

    test('should not apply 50% markup when "Konta zespołu 5?" is not present', async () => {
      const mockProperties = createMockDealProperties({
        // Use smaller document counts to avoid 95% rule triggering SILVER package
        'faktury_rachunki_sprzedazowe___ile_': '5',
        'faktury_rachunki_zakupu___ile_': '3',
        'faktury_walutowe___ile_miesiecznie_': '1',
        'dokumenty_wewnetrzne_wdt__wnt_itp': '1',
        'rodzaj_ksiegowosci': 'Pełna księgowość',
        // MSP field without "Konta zespołu 5?" should not trigger markup
        'pytania_do_msp': 'Biegły rewident?;Inne pytania',
        // IMPORTANT: Include "Kadry" in services to prevent field clearing
        'uslugi_do_wyceny': 'Księgowość;Kadry;Inne usługi'
      });
      getDealProperties.mockResolvedValue(mockProperties);

      const result = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      expect(result).toBeDefined();
      expect(result.completeRecalculationPerformed).toBe(true);

      // Calculate expected values without markup
      const bookingsSum = 5 + 3 + 1 + 1; // 10 documents

      // BR00013 should be calculated normally (max of document count and package minimum)
      // For 10 documents with full accounting, BASE package should be selected
      // BASE minimum is 5, so max(10, 5) = 10
      expect(result.br00013Quantity).toBe(Math.max(10, 5)); // Should be 10

      // MSP audit should still be enabled due to "Biegły rewident?"
      expect(result.shouldHaveBR00111).toBe(true);

      console.log('MSP no markup test result:', {
        bookingsSum: bookingsSum,
        actualBR00013: result.br00013Quantity,
        shouldHaveBR00111: result.shouldHaveBR00111,
        selectedPackageName: result.selectedPackageName
      });
    });

    test('should recalculate accounting packages when MSP markup is applied but calculate BR00013 from base value', async () => {
      const mockProperties = createMockDealProperties({
        // Use document count that would normally select BASE package
        'faktury_rachunki_sprzedazowe___ile_': '15',
        'rodzaj_ksiegowosci': 'Pełna księgowość',
        // MSP field with "Konta zespołu 5?" should trigger package selection based on 50% markup
        'pytania_do_msp': 'Konta zespołu 5?',
        // IMPORTANT: Include "Kadry" in services to prevent field clearing
        'uslugi_do_wyceny': 'Księgowość;Kadry;Inne usługi'
      });
      getDealProperties.mockResolvedValue(mockProperties);

      const result = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      expect(result).toBeDefined();
      expect(result.completeRecalculationPerformed).toBe(true);

      // Original: 15 documents → BASE package
      // With MSP markup: 15 * 1.5 = 23 documents → triggers 95% rule and jumps to SILVER package
      const originalDocuments = 15;
      const markupDocuments = Math.ceil(originalDocuments * 1.5); // 23

      // BR00013 should be calculated from base value (15), not markup (23)
      // Package selection uses markup (23) → SILVER package → max(15, 50) = 50 (SILVER minimum)
      expect(result.br00013Quantity).toBe(50); // Should use SILVER minimum with base value (15)

      // Base document quantity should remain as original value, not markup
      expect(result.baseDocumentQuantity).toBe(originalDocuments); // Should be 15, not 23

      // Package should be recalculated based on markup quantity for selection
      // 23 documents: BASE cost = 499 + (18 × 12.9) = 731.2
      // SILVER 95% threshold = 764 × 0.95 = 725.8
      // Since 731.2 ≥ 725.8, should jump to SILVER package due to 95% rule
      expect(result.selectedPackageName).toBe('SILVER');
      expect(result.accountingPackages).toBeDefined();
      expect(result.accountingPackages.length).toBeGreaterThan(0);

      console.log('MSP accounting package recalculation with base value test result:', {
        originalDocuments: originalDocuments,
        markupForPackageSelection: markupDocuments,
        actualBR00013: result.br00013Quantity,
        baseDocumentQuantity: result.baseDocumentQuantity,
        selectedPackageName: result.selectedPackageName,
        accountingPackagesCount: result.accountingPackages.length,
        explanation: `BR00013 = max(${originalDocuments}, SILVER_minimum_50) = ${result.br00013Quantity} (calculated from base value, package selected using markup)`
      });
    });
  });

  // ============================================================================
  // MSP SIMPLIFIED ACCOUNTING TESTS - Verify MSP options are ignored for simplified accounting
  // ============================================================================
  describe('MSP Simplified Accounting Logic', () => {
    test('should ignore "Biegły rewident" for simplified accounting', async () => {
      const mockProperties = createMockDealProperties({
        'faktury_rachunki_sprzedazowe___ile_': '10',
        'rodzaj_ksiegowosci': 'Księgowość uproszczona', // Simplified accounting
        'pytania_do_msp': 'Biegły rewident?;Inne pytania', // Contains "Biegły rewident?"
        'uslugi_do_wyceny': 'Księgowość;Inne usługi'
      });
      getDealProperties.mockResolvedValue(mockProperties);

      const result = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      expect(result).toBeDefined();
      expect(result.completeRecalculationPerformed).toBe(true);
      expect(result.isFullAccounting).toBe(false);

      // BR00111 should NOT be added for simplified accounting, even if "Biegły rewident?" is selected
      expect(result.shouldHaveBR00111).toBe(false);

      console.log('Simplified accounting MSP test result:', {
        accountingType: 'Księgowość uproszczona',
        mspValue: 'Biegły rewident?;Inne pytania',
        shouldHaveBR00111: result.shouldHaveBR00111,
        explanation: 'BR00111 should be false for simplified accounting regardless of MSP selection'
      });
    });

    test('should ignore "Konta zespołu 5" for simplified accounting', async () => {
      const mockProperties = createMockDealProperties({
        'faktury_rachunki_sprzedazowe___ile_': '15',
        'rodzaj_ksiegowosci': 'Księgowość uproszczona', // Simplified accounting
        'pytania_do_msp': 'Konta zespołu 5?;Inne pytania', // Contains "Konta zespołu 5?"
        'uslugi_do_wyceny': 'Księgowość;Inne usługi'
      });
      getDealProperties.mockResolvedValue(mockProperties);

      const result = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      expect(result).toBeDefined();
      expect(result.completeRecalculationPerformed).toBe(true);
      expect(result.isFullAccounting).toBe(false);

      // For simplified accounting, "Konta zespołu 5?" should be ignored
      // BR00013 should NOT be added for simplified accounting
      expect(result.br00013Quantity).toBe(0);
      expect(result.baseDocumentQuantity).toBe(15); // Should be the original document count, not markup

      console.log('Simplified accounting Konta zespołu 5 test result:', {
        accountingType: 'Księgowość uproszczona',
        mspValue: 'Konta zespołu 5?;Inne pytania',
        originalDocuments: 15,
        br00013Quantity: result.br00013Quantity,
        baseDocumentQuantity: result.baseDocumentQuantity,
        explanation: 'Konta zespołu 5? should be ignored for simplified accounting - no MSP markup applied'
      });
    });

    test('should ignore both "Biegły rewident" and "Konta zespołu 5" for simplified accounting', async () => {
      const mockProperties = createMockDealProperties({
        'faktury_rachunki_sprzedazowe___ile_': '20',
        'rodzaj_ksiegowosci': 'Księgowość uproszczona', // Simplified accounting
        'pytania_do_msp': 'Biegły rewident?;Konta zespołu 5?;Inne pytania', // Contains both MSP options
        'uslugi_do_wyceny': 'Księgowość;Inne usługi'
      });
      getDealProperties.mockResolvedValue(mockProperties);

      const result = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      expect(result).toBeDefined();
      expect(result.completeRecalculationPerformed).toBe(true);
      expect(result.isFullAccounting).toBe(false);

      // Both MSP options should be ignored for simplified accounting
      expect(result.shouldHaveBR00111).toBe(false);

      // BR00013 should NOT be added for simplified accounting
      expect(result.br00013Quantity).toBe(0);
      expect(result.baseDocumentQuantity).toBe(20); // Should be the original document count, not markup

      console.log('Simplified accounting both MSP options test result:', {
        accountingType: 'Księgowość uproszczona',
        mspValue: 'Biegły rewident?;Konta zespołu 5?;Inne pytania',
        shouldHaveBR00111: result.shouldHaveBR00111,
        originalDocuments: 20,
        br00013Quantity: result.br00013Quantity,
        baseDocumentQuantity: result.baseDocumentQuantity,
        explanation: 'Both MSP options should be ignored for simplified accounting'
      });
    });

    test('should still apply MSP options for full accounting', async () => {
      const mockProperties = createMockDealProperties({
        'faktury_rachunki_sprzedazowe___ile_': '15',
        'rodzaj_ksiegowosci': 'Pełna księgowość', // Full accounting
        'pytania_do_msp': 'Biegły rewident?;Konta zespołu 5?;Inne pytania', // Contains both MSP options
        'uslugi_do_wyceny': 'Księgowość;Kadry;Inne usługi'
      });
      getDealProperties.mockResolvedValue(mockProperties);

      const result = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      expect(result).toBeDefined();
      expect(result.completeRecalculationPerformed).toBe(true);
      expect(result.isFullAccounting).toBe(true);

      // For full accounting, MSP options should still apply
      expect(result.shouldHaveBR00111).toBe(true); // "Biegły rewident?" should apply

      // "Konta zespołu 5?" should trigger MSP markup for package selection but BR00013 from base value
      expect(result.baseDocumentQuantity).toBe(15); // Should be the original document count
      // BR00013 should be calculated from base value but package selection should use markup
      const expectedBR00013 = Math.max(15, 50); // SILVER package minimum for full accounting is 50
      expect(result.br00013Quantity).toBe(expectedBR00013);

      console.log('Full accounting MSP options test result:', {
        accountingType: 'Pełna księgowość',
        mspValue: 'Biegły rewident?;Konta zespołu 5?;Inne pytania',
        shouldHaveBR00111: result.shouldHaveBR00111,
        originalDocuments: 15,
        br00013Quantity: result.br00013Quantity,
        baseDocumentQuantity: result.baseDocumentQuantity,
        explanation: 'MSP options should still apply for full accounting'
      });
    });
  });

  // ============================================================================
  // FIELD CLEARING LOGIC - Service and industry removal field clearing
  // ============================================================================
  describe('Field Clearing Logic', () => {
    test('should clear payroll fields when "Kadry" is removed from uslugi_do_wyceny', async () => {
      const mockProperties = createMockDealProperties({
        'faktury_rachunki_sprzedazowe___ile_': '10',
        'rodzaj_ksiegowosci': 'Pełna księgowość',
        // uslugi_do_wyceny without "Kadry" should trigger field clearing
        'uslugi_do_wyceny': 'Księgowość;Inne usługi', // No "Kadry"
        // These payroll fields should be cleared
        'umowa_o_prace___liczba_osob': '5',
        'umowy_cywilnoprawne___liczba_pracownikow': '3',
        'ppk___ile_osob_': '2',
        'kto_robi_import_do_moje_ppk': 'Biuro',
        'pfron___ile_osob_': '1',
        'a1___czy_wystepuja_': '2',
        'pytania_do_msp': 'Biegły rewident?'
      });
      getDealProperties.mockResolvedValue(mockProperties);

      // Mock updateDealProperties to capture field clearing
      updateDealProperties.mockResolvedValue({});

      const result = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      expect(result).toBeDefined();
      expect(result.completeRecalculationPerformed).toBe(true);

      // Verify that updateDealProperties was called to clear payroll fields
      expect(updateDealProperties).toHaveBeenCalledWith(
        mockDealId,
        mockAccessToken,
        expect.objectContaining({
          'umowa_o_prace___liczba_osob': '',
          'umowy_cywilnoprawne___liczba_pracownikow': '',
          'ppk___ile_osob_': '',
          'kto_robi_import_do_moje_ppk': '',
          'pfron___ile_osob_': '',
          'a1___czy_wystepuja_': '',
        })
      );

      // Payroll quantities should be zero since fields were cleared
      expect(result.br00070Quantity).toBe(0);
      expect(result.br00071Quantity).toBe(0);
      expect(result.br00072Quantity).toBe(0);
      expect(result.br00073Quantity).toBe(0);
      expect(result.br00165Quantity).toBe(0);

      console.log('Payroll field clearing test result:', {
        payrollFieldsCleared: true,
        br00070Quantity: result.br00070Quantity,
        br00071Quantity: result.br00071Quantity,
        updateDealPropertiesCalled: updateDealProperties.mock.calls.length > 0
      });
    });

    test('should remove ALL existing payroll line items when "Kadry" is removed from services', async () => {
      // Mock existing line items that include payroll items (with proper structure for getDealLineItemsWithDetails)
      const existingLineItems = [
        { id: 'line1', properties: { hs_sku: 'BR00070', name: 'Payroll Premium 1', quantity: '2' } },
        { id: 'line2', properties: { hs_sku: 'BR00071', name: 'Payroll Premium 2', quantity: '3' } },
        { id: 'line3', properties: { hs_sku: 'BR00072', name: 'Payroll Regular', quantity: '1' } },
        { id: 'line4', properties: { hs_sku: 'BR00003', name: 'Accounting Base', quantity: '1' } }
      ];

      // Mock getDealLineItemsWithDetails to return existing payroll items (used by field clearing logic)
      getDealLineItemsWithDetails.mockResolvedValue(existingLineItems);

      const mockProperties = createMockDealProperties({
        'faktury_rachunki_sprzedazowe___ile_': '10',
        'rodzaj_ksiegowosci': 'Pełna księgowość',
        // uslugi_do_wyceny without "Kadry" should trigger payroll line item removal
        'uslugi_do_wyceny': 'Księgowość;Inne usługi', // No "Kadry"
        // These payroll fields should be cleared
        'umowa_o_prace___liczba_osob': '5',
        'umowy_cywilnoprawne___liczba_pracownikow': '3'
      });
      getDealProperties.mockResolvedValue(mockProperties);

      // Mock updateDealProperties and deleteLineItem
      updateDealProperties.mockResolvedValue({});
      deleteLineItem.mockResolvedValue({});

      const result = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      expect(result).toBeDefined();
      expect(result.completeRecalculationPerformed).toBe(true);

      // Verify that deleteLineItem was called for each payroll line item
      expect(deleteLineItem).toHaveBeenCalled();

      // Check that deleteLineItem was called for the payroll items
      const deleteLineItemCalls = deleteLineItem.mock.calls;
      const deletedLineItemIds = deleteLineItemCalls.map(call => call[0]); // First parameter is line item ID

      // Should have called deleteLineItem for payroll items (line1, line2, line3)
      expect(deletedLineItemIds).toContain('line1'); // BR00070
      expect(deletedLineItemIds).toContain('line2'); // BR00071
      expect(deletedLineItemIds).toContain('line3'); // BR00072

      // Should NOT have called deleteLineItem for accounting items (line4)
      expect(deletedLineItemIds).not.toContain('line4'); // BR00003

      console.log('Payroll line item removal test result:', {
        totalDeleteCalls: deleteLineItemCalls.length,
        deletedLineItemIds: deletedLineItemIds,
        payrollItemsDeleted: deletedLineItemIds.filter(id => ['line1', 'line2', 'line3'].includes(id)).length
      });
    });

    test('should clear e-commerce fields when "E-commerce" is removed from branza', async () => {
      const mockProperties = createMockDealProperties({
        'faktury_rachunki_sprzedazowe___ile_': '10',
        'rodzaj_ksiegowosci': 'Pełna księgowość',
        // branza without "E-commerce" should trigger field clearing
        'branza': 'Handel;Usługi', // No "E-commerce"
        // These e-commerce fields should be cleared
        'ile_transakcji_sprzedazy_w_miesiacu_': '150',
        'ile_kanalow_platniczych_': '3'
      });
      getDealProperties.mockResolvedValue(mockProperties);

      // Mock updateDealProperties to capture field clearing
      updateDealProperties.mockResolvedValue({});

      const result = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      expect(result).toBeDefined();
      expect(result.completeRecalculationPerformed).toBe(true);

      // Verify that updateDealProperties was called to clear e-commerce fields
      expect(updateDealProperties).toHaveBeenCalledWith(
        mockDealId,
        mockAccessToken,
        expect.objectContaining({
          'ile_transakcji_sprzedazy_w_miesiacu_': '',
          'ile_kanalow_platniczych_': ''
        })
      );

      // E-commerce should be disabled since fields were cleared
      expect(result.ecommerceRefreshed).toBe(false);
      expect(result.ecommerceTransactionCount).toBe(0);

      console.log('E-commerce field clearing test result:', {
        ecommerceFieldsCleared: true,
        ecommerceRefreshed: result.ecommerceRefreshed,
        ecommerceTransactionCount: result.ecommerceTransactionCount,
        updateDealPropertiesCalled: updateDealProperties.mock.calls.length > 0
      });
    });

    test('should remove ALL existing e-commerce line items when "E-commerce" is removed from branza', async () => {
      // Mock existing line items that include e-commerce items (with proper structure for getDealLineItemsWithDetails)
      const existingLineItems = [
        { id: 'line1', properties: { hs_sku: 'BR00160', name: 'E-commerce package 1', quantity: '1' } },
        { id: 'line2', properties: { hs_sku: 'BR00166', name: 'Additional transactions 1', quantity: '50' } },
        { id: 'line3', properties: { hs_sku: 'BR00167', name: 'Additional transactions 2', quantity: '100' } },
        { id: 'line4', properties: { hs_sku: 'BR00003', name: 'Accounting Base', quantity: '1' } }
      ];

      // Mock getDealLineItemsWithDetails to return existing e-commerce items (used by field clearing logic)
      getDealLineItemsWithDetails.mockResolvedValue(existingLineItems);

      const mockProperties = createMockDealProperties({
        'faktury_rachunki_sprzedazowe___ile_': '10',
        'rodzaj_ksiegowosci': 'Pełna księgowość',
        // branza without "E-commerce" should trigger e-commerce line item removal
        'branza': 'Handel;Usługi', // No "E-commerce"
        // These e-commerce fields should be cleared
        'ile_transakcji_sprzedazy_w_miesiacu_': '150',
        'ile_kanalow_platniczych_': '3'
      });
      getDealProperties.mockResolvedValue(mockProperties);

      // Mock updateDealProperties and deleteLineItem
      updateDealProperties.mockResolvedValue({});
      deleteLineItem.mockResolvedValue({});

      const result = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      expect(result).toBeDefined();
      expect(result.completeRecalculationPerformed).toBe(true);

      // Verify that deleteLineItem was called for each e-commerce line item
      expect(deleteLineItem).toHaveBeenCalled();

      // Check that deleteLineItem was called for the e-commerce items
      const deleteLineItemCalls = deleteLineItem.mock.calls;
      const deletedLineItemIds = deleteLineItemCalls.map(call => call[0]); // First parameter is line item ID

      // Should have called deleteLineItem for e-commerce items (line1, line2, line3)
      expect(deletedLineItemIds).toContain('line1'); // BR00160
      expect(deletedLineItemIds).toContain('line2'); // BR00166
      expect(deletedLineItemIds).toContain('line3'); // BR00167

      // Should NOT have called deleteLineItem for accounting items (line4)
      expect(deletedLineItemIds).not.toContain('line4'); // BR00003

      console.log('E-commerce line item removal test result:', {
        totalDeleteCalls: deleteLineItemCalls.length,
        deletedLineItemIds: deletedLineItemIds,
        ecommerceItemsDeleted: deletedLineItemIds.filter(id => ['line1', 'line2', 'line3'].includes(id)).length
      });
    });

    test('should clear accounting fields when "Księgowość" is removed from uslugi_do_wyceny', async () => {
      const mockProperties = createMockDealProperties({
        'faktury_rachunki_sprzedazowe___ile_': '25',
        'faktury_rachunki_zakupu___ile_': '15',
        'kasy_fiskalne___ile_': '2',
        'vat___status_podatnika': 'VAT EU',
        'kp_kw___banki_': '5',
        'rodzaj_ksiegowosci': 'Pełna księgowość',
        // uslugi_do_wyceny without "Księgowość" should trigger field clearing
        'uslugi_do_wyceny': 'Kadry;Inne usługi', // No "Księgowość"
        'branza': 'Handel'
      });

      getDealProperties.mockResolvedValue(mockProperties);

      // Mock updateDealProperties to capture field clearing
      updateDealProperties.mockResolvedValue({});

      const result = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      expect(result).toBeDefined();
      expect(result.completeRecalculationPerformed).toBe(true);

      // Verify that the accounting field clearing was called
      expect(updateDealProperties).toHaveBeenCalledWith(
        mockDealId,
        mockAccessToken,
        expect.objectContaining({
          'faktury_rachunki_sprzedazowe___ile_': '',
          'faktury_rachunki_zakupu___ile_': '',
          'kasy_fiskalne___ile_': '',
          'vat___status_podatnika': '',
          'kp_kw___banki_': ''
        })
      );
    });

    test('should clear accounting fields when "rodzaj_ksiegowosci" is empty', async () => {
      const mockProperties = createMockDealProperties({
        'faktury_rachunki_sprzedazowe___ile_': '25',
        'faktury_rachunki_zakupu___ile_': '15',
        'kasy_fiskalne___ile_': '2',
        'vat___status_podatnika': 'VAT EU',
        'kp_kw___banki_': '5',
        'rodzaj_ksiegowosci': '', // Empty accounting type should trigger field clearing
        'uslugi_do_wyceny': 'Księgowość;Kadry', // Has "Księgowość" but empty rodzaj_ksiegowosci
        'branza': 'Handel'
      });

      getDealProperties.mockResolvedValue(mockProperties);

      // Mock updateDealProperties to capture field clearing
      updateDealProperties.mockResolvedValue({});

      const result = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      expect(result).toBeDefined();
      expect(result.completeRecalculationPerformed).toBe(true);

      // Verify that the accounting field clearing was called
      expect(updateDealProperties).toHaveBeenCalledWith(
        mockDealId,
        mockAccessToken,
        expect.objectContaining({
          'faktury_rachunki_sprzedazowe___ile_': '',
          'faktury_rachunki_zakupu___ile_': '',
          'kasy_fiskalne___ile_': '',
          'vat___status_podatnika': '',
          'kp_kw___banki_': ''
        })
      );
    });

    test('should remove ALL existing accounting line items when accounting service is removed', async () => {
      // Mock existing line items including accounting items
      const existingLineItems = [
        { id: 'line1', properties: { hs_sku: 'BR00013', name: 'Bank statement processing', quantity: '10' } },
        { id: 'line2', properties: { hs_sku: 'BR00030', name: 'Fixed assets', quantity: '2' } },
        { id: 'line3', properties: { hs_sku: 'BR00032', name: 'VAT EU', quantity: '1' } },
        { id: 'line4', properties: { hs_sku: 'BR00003', name: 'Accounting Base', quantity: '1' } },
        { id: 'line5', properties: { hs_sku: 'BR00070', name: 'Payroll Premium', quantity: '5' } } // Should NOT be deleted
      ];

      // Mock getDealLineItemsWithDetails to return existing accounting items
      getDealLineItemsWithDetails.mockResolvedValue(existingLineItems);

      const mockProperties = createMockDealProperties({
        'faktury_rachunki_sprzedazowe___ile_': '25',
        'rodzaj_ksiegowosci': 'Pełna księgowość',
        // uslugi_do_wyceny without "Księgowość" should trigger accounting line item removal
        'uslugi_do_wyceny': 'Kadry;Inne usługi', // No "Księgowość"
        'branza': 'Handel'
      });

      getDealProperties.mockResolvedValue(mockProperties);

      // Mock updateDealProperties to capture field clearing
      updateDealProperties.mockResolvedValue({});

      const result = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      expect(result).toBeDefined();
      expect(result.completeRecalculationPerformed).toBe(true);

      // Verify that deleteLineItem was called for accounting line items
      expect(deleteLineItem).toHaveBeenCalled();

      // Check that deleteLineItem was called for the accounting items
      const deleteLineItemCalls = deleteLineItem.mock.calls;
      const deletedLineItemIds = deleteLineItemCalls.map(call => call[0]); // First parameter is line item ID

      // Should have called deleteLineItem for accounting items (line1, line2, line3, line4)
      expect(deletedLineItemIds).toContain('line1'); // BR00013
      expect(deletedLineItemIds).toContain('line2'); // BR00030
      expect(deletedLineItemIds).toContain('line3'); // BR00032
      expect(deletedLineItemIds).toContain('line4'); // BR00003

      // Should NOT have called deleteLineItem for payroll items (line5)
      expect(deletedLineItemIds).not.toContain('line5'); // BR00070

      console.log('Accounting line item removal test result:', {
        totalDeleteCalls: deleteLineItemCalls.length,
        deletedLineItemIds: deletedLineItemIds,
        accountingItemsDeleted: deletedLineItemIds.filter(id => ['line1', 'line2', 'line3', 'line4'].includes(id)).length
      });
    });
  });

  // ============================================================================
  // 95% RULE EDGE CASES - Package optimization threshold testing
  // ============================================================================
  describe('95% Rule Edge Cases', () => {
    test('should trigger 95% rule and jump from BASE to SILVER package', async () => {
      // Set up pricing where BASE + extras >= 95% of SILVER
      // BASE: 499 + (19 * 12.9) = 499 + 245.1 = 744.1
      // SILVER: 764, 95% threshold = 725.8
      // Since 744.1 >= 725.8, should jump to SILVER

      const mockProperties = createMockDealProperties({
        'faktury_rachunki_sprzedazowe___ile_': '20', // 20 documents
        'faktury_rachunki_zakupu___ile_': '4',       // 4 documents
        'rodzaj_ksiegowosci': 'Pełna księgowość'     // Total: 24 documents
      });
      getDealProperties.mockResolvedValue(mockProperties);

      const result = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      expect(result).toBeDefined();
      expect(result.completeRecalculationPerformed).toBe(true);

      // Should jump to SILVER due to 95% rule
      expect(result.selectedPackageName).toBe('SILVER');
      expect(result.br00013Quantity).toBe(50); // SILVER minimum

      console.log('95% rule BASE→SILVER test result:', {
        baseDocumentQuantity: result.baseDocumentQuantity,
        selectedPackageName: result.selectedPackageName,
        br00013Quantity: result.br00013Quantity,
        explanation: 'BASE cost (744.1) >= 95% of SILVER (725.8), so jumped to SILVER'
      });
    });

    test('should trigger 95% rule and jump from SILVER to GOLD package', async () => {
      // Set up pricing where SILVER + extras >= 95% of GOLD
      // SILVER: 764 + (extras * 15.3)
      // GOLD: 1374, 95% threshold = 1305.3
      // Need: 764 + (extras * 15.3) >= 1305.3
      // extras >= (1305.3 - 764) / 15.3 = 35.4, so need 36+ extra documents
      // SILVER handles 50 base + 60 max extra = 110 total
      // So 86 documents = 50 base + 36 extra = 764 + (36 * 15.3) = 764 + 550.8 = 1314.8

      const mockProperties = createMockDealProperties({
        'faktury_rachunki_sprzedazowe___ile_': '70',
        'faktury_rachunki_zakupu___ile_': '16',
        'rodzaj_ksiegowosci': 'Pełna księgowość'     // Total: 86 documents
      });
      getDealProperties.mockResolvedValue(mockProperties);

      const result = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      expect(result).toBeDefined();
      expect(result.completeRecalculationPerformed).toBe(true);

      // Should jump to GOLD due to 95% rule
      expect(result.selectedPackageName).toBe('GOLD');
      expect(result.br00013Quantity).toBe(110); // GOLD minimum

      console.log('95% rule SILVER→GOLD test result:', {
        baseDocumentQuantity: result.baseDocumentQuantity,
        selectedPackageName: result.selectedPackageName,
        br00013Quantity: result.br00013Quantity,
        explanation: 'SILVER cost (1314.8) >= 95% of GOLD (1305.3), so jumped to GOLD'
      });
    });

    test('should trigger 95% rule and jump from GOLD to PLATINUM package', async () => {
      // Set up pricing where GOLD + extras >= 95% of PLATINUM
      // GOLD: 1374 + (extras * 18.7)
      // PLATINUM: 2000, 95% threshold = 1900
      // Need: 1374 + (extras * 18.7) >= 1900
      // extras >= (1900 - 1374) / 18.7 = 28.1, so need 29+ extra documents
      // GOLD handles 110 base + 88 max extra = 198 total
      // So 139 documents = 110 base + 29 extra = 1374 + (29 * 18.7) = 1374 + 542.3 = 1916.3

      const mockProperties = createMockDealProperties({
        'faktury_rachunki_sprzedazowe___ile_': '100',
        'faktury_rachunki_zakupu___ile_': '39',
        'rodzaj_ksiegowosci': 'Pełna księgowość'     // Total: 139 documents
      });
      getDealProperties.mockResolvedValue(mockProperties);

      const result = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      expect(result).toBeDefined();
      expect(result.completeRecalculationPerformed).toBe(true);

      // Should jump to PLATINUM due to 95% rule
      expect(result.selectedPackageName).toBe('PLATINUM');
      expect(result.br00013Quantity).toBe(200); // PLATINUM minimum

      console.log('95% rule GOLD→PLATINUM test result:', {
        baseDocumentQuantity: result.baseDocumentQuantity,
        selectedPackageName: result.selectedPackageName,
        br00013Quantity: result.br00013Quantity,
        explanation: 'GOLD cost (1916.3) >= 95% of PLATINUM (1900), so jumped to PLATINUM'
      });
    });

    test('should NOT trigger 95% rule when just below threshold', async () => {
      // Test case where we're just below the 95% threshold
      // BASE: 499 + (18 * 12.9) = 499 + 232.2 = 731.2
      // SILVER 95% threshold: 764 * 0.95 = 725.8
      // Since 731.2 >= 725.8, this will actually trigger the rule
      // Let's use fewer documents: 17 extra = 499 + (17 * 12.9) = 499 + 219.3 = 718.3
      // Since 718.3 < 725.8, should stay at BASE

      const mockProperties = createMockDealProperties({
        'faktury_rachunki_sprzedazowe___ile_': '18', // 18 documents
        'faktury_rachunki_zakupu___ile_': '4',       // 4 documents
        'rodzaj_ksiegowosci': 'Pełna księgowość'     // Total: 22 documents = 5 base + 17 extra
      });
      getDealProperties.mockResolvedValue(mockProperties);

      const result = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      expect(result).toBeDefined();
      expect(result.completeRecalculationPerformed).toBe(true);

      // Should stay at BASE since below 95% threshold
      expect(result.selectedPackageName).toBe('BASE');
      expect(result.br00013Quantity).toBe(22); // Document count (above BASE minimum of 5)

      console.log('95% rule NOT triggered test result:', {
        baseDocumentQuantity: result.baseDocumentQuantity,
        selectedPackageName: result.selectedPackageName,
        br00013Quantity: result.br00013Quantity,
        explanation: 'BASE cost (718.3) < 95% of SILVER (725.8), so stayed at BASE'
      });
    });

    test('should handle exact 95% threshold boundary', async () => {
      // Test the exact boundary case
      // We need BASE cost to be exactly 95% of SILVER = 725.8
      // BASE: 499 + (extras * 12.9) = 725.8
      // extras = (725.8 - 499) / 12.9 = 17.58
      // So 18 extra documents should trigger the rule (rounded up)
      // Total: 5 base + 18 extra = 23 documents

      const mockProperties = createMockDealProperties({
        'faktury_rachunki_sprzedazowe___ile_': '19', // 19 documents
        'faktury_rachunki_zakupu___ile_': '4',       // 4 documents
        'rodzaj_ksiegowosci': 'Pełna księgowość'     // Total: 23 documents = 5 base + 18 extra
      });
      getDealProperties.mockResolvedValue(mockProperties);

      const result = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      expect(result).toBeDefined();
      expect(result.completeRecalculationPerformed).toBe(true);

      // Should jump to SILVER at the exact threshold
      expect(result.selectedPackageName).toBe('SILVER');
      expect(result.br00013Quantity).toBe(50); // SILVER minimum

      console.log('95% rule exact threshold test result:', {
        baseDocumentQuantity: result.baseDocumentQuantity,
        selectedPackageName: result.selectedPackageName,
        br00013Quantity: result.br00013Quantity,
        explanation: 'BASE cost (731.2) >= 95% of SILVER (725.8), so jumped to SILVER at threshold'
      });
    });

    test('should handle multiple 95% rule jumps in sequence', async () => {
      // Test a case where the cost might trigger multiple jumps
      // Use a very high document count that could potentially jump multiple levels

      const mockProperties = createMockDealProperties({
        'faktury_rachunki_sprzedazowe___ile_': '180',
        'faktury_rachunki_zakupu___ile_': '20',
        'rodzaj_ksiegowosci': 'Pełna księgowość'     // Total: 200 documents
      });
      getDealProperties.mockResolvedValue(mockProperties);

      const result = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      expect(result).toBeDefined();
      expect(result.completeRecalculationPerformed).toBe(true);

      // With 200 documents, should select PLATINUM package
      expect(result.selectedPackageName).toBe('PLATINUM');
      expect(result.br00013Quantity).toBe(200); // PLATINUM minimum or document count

      console.log('95% rule multiple jumps test result:', {
        baseDocumentQuantity: result.baseDocumentQuantity,
        selectedPackageName: result.selectedPackageName,
        br00013Quantity: result.br00013Quantity,
        explanation: 'High document count (200) resulted in PLATINUM package selection'
      });
    });
  });

  // ============================================================================
  // E-COMMERCE PACKAGES - Transaction volume optimization and pricing logic
  // ============================================================================
  describe('E-commerce Package Logic', () => {
    test('should return success with pricing data for full accounting', async () => {
      getEcommercePackagePrices.mockResolvedValue(createMockFullAccountingEcommercePrices());

      const result = await testEcommercePricing(true, mockAccessToken);

      expect(result).toEqual({
        success: true,
        data: createMockFullAccountingEcommercePrices(),
        accountingType: 'full'
      });
      expect(getEcommercePackagePrices).toHaveBeenCalledWith(true, mockAccessToken);
    });

    test('should return success with pricing data for simplified accounting', async () => {
      const simplifiedPrices = createMockSimplifiedAccountingEcommercePrices();
      getEcommercePackagePrices.mockResolvedValue(simplifiedPrices);

      const result = await testEcommercePricing(false, mockAccessToken);

      expect(result).toEqual({
        success: true,
        data: simplifiedPrices,
        accountingType: 'simplified'
      });
      expect(getEcommercePackagePrices).toHaveBeenCalledWith(false, mockAccessToken);
    });

    test('should return error when pricing data fetch fails', async () => {
      getEcommercePackagePrices.mockResolvedValue(null);

      const result = await testEcommercePricing(true, mockAccessToken);

      expect(result).toEqual({
        success: false,
        error: 'Failed to fetch pricing data'
      });
    });

    test('should handle API errors gracefully', async () => {
      getEcommercePackagePrices.mockRejectedValue(new Error('API Error'));

      await expect(testEcommercePricing(true, mockAccessToken)).rejects.toThrow('API Error');
    });



    test('should select BR00058 package for 150 transactions (full accounting)', async () => {
      getEcommercePackagePrices.mockResolvedValue(createMockFullAccountingEcommercePrices());

      const result = await calculateOptimalEcommercePackage(150, true, mockAccessToken);

      expect(result.selectedPackage).toBe('BR00058');
      expect(result.additionalTransactions).toBe(0);
      expect(result.totalCost).toBe(250);
      expect(result.additionalCostPerTransaction).toBe(1.25);
      expect(result.additionalSku).toBe('BR00170');
    });

    test('should select BR00058 with additional transactions for 300 transactions (full accounting)', async () => {
      getEcommercePackagePrices.mockResolvedValue(createMockFullAccountingEcommercePrices());

      const result = await calculateOptimalEcommercePackage(300, true, mockAccessToken);

      expect(result.selectedPackage).toBe('BR00058');
      expect(result.additionalTransactions).toBe(100); // 300 - 200
      expect(result.totalCost).toBe(375); // 250 + (100 * 1.25)
      expect(result.additionalCostPerTransaction).toBe(1.25);
    });



    test('should work correctly with simplified accounting packages', async () => {
      getEcommercePackagePrices.mockResolvedValue(createMockSimplifiedAccountingEcommercePrices());

      const result = await calculateOptimalEcommercePackage(150, false, mockAccessToken);

      expect(result.selectedPackage).toBe('BR00090');
      expect(result.additionalTransactions).toBe(0);
      expect(result.totalCost).toBe(180);
      expect(result.additionalCostPerTransaction).toBe(0.90);
      expect(result.additionalSku).toBe('BR00166');
    });






  });

  // Line Item Manager tests moved to separate file: tests/unit/line-item-manager.test.js

  // ============================================================================
  // ERROR HANDLING AND EDGE CASES - Robustness and error scenarios
  // ============================================================================
  describe('Error Handling and Edge Cases', () => {
    // Line Item Manager error tests moved to tests/unit/line-item-manager.test.js

    // Payroll Package Edge Cases
    test('should handle empty/null/undefined employee counts in payroll', async () => {
      const dealProperties = createMockDealProperties({
        'pakiet_kadrowo_placowy': 'Płace',
        'umowa_o_prace___liczba_osob': '', // Empty
        'umowy_cywilnoprawne___liczba_pracownikow': null, // Null
        'ppk___ile_osob_': undefined, // Undefined
        'pfron___ile_osob_': '0', // Valid zero
        'a1___czy_wystepuja_': '2',
        'dodatkowe_skladniki_wynagrodzenia': '0',
        'kto_robi_import_do_moje_ppk': 'Biuro'
      });

      const mockResult = createMockPayrollResult();
      const result = await processPayrollPackage(mockResult, dealProperties, 'test-deal', mockPayrollPrices);

      expect(result.br00072Quantity).toBe(0); // Empty treated as 0
      expect(result.br00073Quantity).toBe(0); // Null treated as 0
      expect(result.br00077Quantity).toBe(0); // Undefined treated as 0
      expect(result.br00080Quantity).toBe(0); // Valid zero
      expect(result.br00165Quantity).toBe(2); // Valid number
    });

    test('should throw error for invalid employee counts in payroll', async () => {
      const dealProperties = createMockDealProperties({
        'pakiet_kadrowo_placowy': 'Płace',
        'umowa_o_prace___liczba_osob': '5',
        'umowy_cywilnoprawne___liczba_pracownikow': '3',
        'ppk___ile_osob_': '2',
        'pfron___ile_osob_': 'invalid', // Invalid string
        'a1___czy_wystepuja_': '1',
        'dodatkowe_skladniki_wynagrodzenia': '5',
        'kto_robi_import_do_moje_ppk': 'Biuro'
      });

      const mockResult = createMockPayrollResult();

      await expect(async () => {
        await processPayrollPackage(mockResult, dealProperties, 'test-deal', mockPayrollPrices);
      }).rejects.toThrow('Invalid numeric value for field \'pfron___ile_osob_\': invalid');
    });

    // Document Calculation Edge Cases
    test('should throw error for invalid document values', () => {
      const dealProperties = createMockDealProperties({
        'faktury_rachunki_sprzedazowe___ile_': '5',
        'faktury_rachunki_zakupu___ile_': '3',
        'faktury_walutowe___ile_miesiecznie_': '2',
        'dokumenty_wewnetrzne_wdt__wnt_itp': 'invalid'
      });

      expect(() => {
        calculateDocumentSum(dealProperties, DOCUMENT_FIELDS.BOOKING_OPERATIONS);
      }).toThrow('Invalid numeric value for field \'dokumenty_wewnetrzne_wdt__wnt_itp\': invalid');
    });



    test('should handle utility functions correctly', () => {
      const mockResult = createMockPayrollResult();
      mockResult.br00070Quantity = 10;
      mockResult.br00077Quantity = 5;
      mockResult.shouldHaveBR00078 = true;

      clearAllPayrollQuantities(mockResult);

      expect(mockResult.br00069Quantity).toBe(0);
      expect(mockResult.br00070Quantity).toBe(0);
      expect(mockResult.br00071Quantity).toBe(0);
      expect(mockResult.br00072Quantity).toBe(0);
      expect(mockResult.br00073Quantity).toBe(0);
      expect(mockResult.br00074Quantity).toBe(0);
      expect(mockResult.br00075Quantity).toBe(0);
      expect(mockResult.br00077Quantity).toBe(0);
      expect(mockResult.br00080Quantity).toBe(0);
      expect(mockResult.br00165Quantity).toBe(0);
      expect(mockResult.shouldHaveBR00078).toBe(false);
      expect(mockResult.shouldHaveBR00079).toBe(false);
    });
  });

  // ============================================================================
  // COMPREHENSIVE BUSINESS SCENARIOS - Simulating real-world integration scenarios
  // ============================================================================
  describe('Comprehensive Business Scenarios ', () => {
    test('Small business comprehensive update scenario', async () => {
      const mockProperties = createSmallBusinessScenario();
      getDealProperties.mockResolvedValue(mockProperties);

      const result = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      // Verify the result structure
      expect(result).toHaveProperty('br00013Quantity');
      expect(result).toHaveProperty('baseDocumentQuantity');
      expect(result).toHaveProperty('selectedPackageName');
      expect(result).toHaveProperty('completeRecalculationPerformed');

      // Verify comprehensive recalculation was performed
      expect(result.completeRecalculationPerformed).toBe(true);

      // Small business should use BASE package
      expect(result.selectedPackageName).toBe('BASE');
      expect(result.br00013Quantity).toBe(Math.max(12, 5)); // max(document_sum, BASE_minimum)
      expect(result.baseDocumentQuantity).toBe(12); // 7+3+1+1 = 12

      console.log('Small business comprehensive update result:', {
        br00013Quantity: result.br00013Quantity,
        baseDocumentQuantity: result.baseDocumentQuantity,
        selectedPackageName: result.selectedPackageName,
        isFullAccounting: result.isFullAccounting,
        shouldHaveBR00032: result.shouldHaveBR00032,
        shouldHaveBR00033: result.shouldHaveBR00033,
        shouldHaveBR00129: result.shouldHaveBR00129
      });
    });

    test('Medium business comprehensive update scenario', async () => {
      const mockProperties = createMediumBusinessScenario();
      getDealProperties.mockResolvedValue(mockProperties);

      const result = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      // Verify the result structure and that it contains real data
      expect(result).toHaveProperty('completeRecalculationPerformed');
      expect(result.completeRecalculationPerformed).toBe(true);

      // Verify that all business logic areas were processed
      expect(result).toHaveProperty('br00013Quantity');
      expect(result).toHaveProperty('isFullAccounting');

      // VAT settings should be defined (even if false)
      expect(result).toHaveProperty('shouldHaveBR00032');
      expect(result).toHaveProperty('shouldHaveBR00033');

      // Language settings should be defined
      expect(result).toHaveProperty('shouldHaveBR00129');

      // Medium business should trigger SILVER package due to 95% rule
      expect(result.selectedPackageName).toBe('SILVER');
      expect(result.shouldHaveBR00032).toBe(true); // VAT EU
      expect(result.shouldHaveBR00129).toBe(true); // Non-Polish language

      console.log('Medium business comprehensive update result:', {
        completeRecalculationPerformed: result.completeRecalculationPerformed,
        br00013Quantity: result.br00013Quantity,
        isFullAccounting: result.isFullAccounting,
        vatSettings: {
          shouldHaveBR00032: result.shouldHaveBR00032,
          shouldHaveBR00033: result.shouldHaveBR00033
        },
        languageSettings: {
          shouldHaveBR00129: result.shouldHaveBR00129
        }
      });
    });


    test('Enterprise comprehensive update scenario', async () => {
      const mockProperties = createEnterpriseScenario();
      getDealProperties.mockResolvedValue(mockProperties);

      const result = await handleComprehensiveUpdate(
        'aktualizuj_dane',
        'true',
        mockDealId,
        mockAccessToken
      );

      // Verify the result structure
      expect(result).toHaveProperty('br00013Quantity');
      expect(result).toHaveProperty('baseDocumentQuantity');
      expect(result).toHaveProperty('completeRecalculationPerformed');

      // Verify comprehensive recalculation was performed
      expect(result.completeRecalculationPerformed).toBe(true);

      // Enterprise should use PLATINUM package
      expect(result.selectedPackageName).toBe('PLATINUM');
      expect(result.shouldHaveBR00032).toBe(true); // Multiple VAT types
      expect(result.shouldHaveBR00033).toBe(true); // VAT OSS
      expect(result.shouldHaveBR00129).toBe(true); // German language
      expect(result.shouldHaveBR00111).toBe(true); // MSP audit

      console.log('Enterprise comprehensive update result:', {
        br00013Quantity: result.br00013Quantity,
        baseDocumentQuantity: result.baseDocumentQuantity,
        isFullAccounting: result.isFullAccounting,
        completeRecalculationPerformed: result.completeRecalculationPerformed
      });
    });
  });

  // ============================================================================
  // PIT PACKAGE UNIT TESTS - Personal Income Tax package logic
  // ============================================================================
  describe('PIT Package Unit Tests', () => {
    beforeEach(() => {
      // Mock getProductPrice for PIT package price fetching
      getProductPrice.mockImplementation((sku) => {
        const pitPrices = {
          'BR00094': 234, // BASE PIT
          'BR00095': 294, // SILVER PIT
          'BR00096': 524, // GOLD PIT
          'BR00097': 684  // PLATINUM PIT
        };
        return Promise.resolve(pitPrices[sku] || 0);
      });
    });

    test('should fetch PIT package prices correctly', async () => {
      const mockAccessToken = 'test-token';

      const result = await getPitPackagePrices(mockAccessToken);

      expect(result).toBeDefined();
      expect(result.packages).toBeDefined();
      expect(result.packages.BASE).toEqual({ sku: 'BR00094', price: 234 });
      expect(result.packages.SILVER).toEqual({ sku: 'BR00095', price: 294 });
      expect(result.packages.GOLD).toEqual({ sku: 'BR00096', price: 524 });
      expect(result.packages.PLATINUM).toEqual({ sku: 'BR00097', price: 684 });

      // Verify that getProductPrice was called for each PIT SKU
      expect(getProductPrice).toHaveBeenCalledWith('BR00094', mockAccessToken);
      expect(getProductPrice).toHaveBeenCalledWith('BR00095', mockAccessToken);
      expect(getProductPrice).toHaveBeenCalledWith('BR00096', mockAccessToken);
      expect(getProductPrice).toHaveBeenCalledWith('BR00097', mockAccessToken);
    });

    test('should select correct PIT package for BASE accounting package', async () => {
      const mockAccessToken = 'test-token';

      const result = await selectPitPackageForSimplifiedAccounting('BASE', mockAccessToken);

      expect(result).toBeDefined();
      expect(result.length).toBe(1);
      expect(result[0]).toEqual({
        sku: 'BR00094',
        quantity: 1,
        description: 'PIT BASE package for simplified accounting',
        price: 234
      });
    });

    test('should select correct PIT package for SILVER accounting package', async () => {
      const mockAccessToken = 'test-token';

      const result = await selectPitPackageForSimplifiedAccounting('SILVER', mockAccessToken);

      expect(result).toBeDefined();
      expect(result.length).toBe(1);
      expect(result[0]).toEqual({
        sku: 'BR00095',
        quantity: 1,
        description: 'PIT SILVER package for simplified accounting',
        price: 294
      });
    });

    test('should select correct PIT package for GOLD accounting package', async () => {
      const mockAccessToken = 'test-token';

      const result = await selectPitPackageForSimplifiedAccounting('GOLD', mockAccessToken);

      expect(result).toBeDefined();
      expect(result.length).toBe(1);
      expect(result[0]).toEqual({
        sku: 'BR00096',
        quantity: 1,
        description: 'PIT GOLD package for simplified accounting',
        price: 524
      });
    });

    test('should select correct PIT package for PLATINUM accounting package', async () => {
      const mockAccessToken = 'test-token';

      const result = await selectPitPackageForSimplifiedAccounting('PLATINUM', mockAccessToken);

      expect(result).toBeDefined();
      expect(result.length).toBe(1);
      expect(result[0]).toEqual({
        sku: 'BR00097',
        quantity: 1,
        description: 'PIT PLATINUM package for simplified accounting',
        price: 684
      });
    });

    test('should return empty array when no accounting package selected', async () => {
      const mockAccessToken = 'test-token';

      const result = await selectPitPackageForSimplifiedAccounting('', mockAccessToken);

      expect(result).toEqual([]);
    });

    test('should return empty array for invalid accounting package', async () => {
      const mockAccessToken = 'test-token';

      const result = await selectPitPackageForSimplifiedAccounting('INVALID', mockAccessToken);

      expect(result).toEqual([]);
    });

    test('should handle PIT package price fetching errors', async () => {
      const mockAccessToken = 'test-token';

      // Mock getProductPrice to throw an error
      getProductPrice.mockRejectedValue(new Error('Price fetch failed'));

      await expect(getPitPackagePrices(mockAccessToken)).rejects.toThrow('Failed to fetch PIT package prices: Price fetch failed');
    });

    test('should handle PIT package selection errors', async () => {
      const mockAccessToken = 'test-token';

      // Mock getProductPrice to throw an error
      getProductPrice.mockRejectedValue(new Error('Price fetch failed'));

      await expect(selectPitPackageForSimplifiedAccounting('BASE', mockAccessToken)).rejects.toThrow('Failed to select PIT package: Failed to fetch PIT package prices: Price fetch failed');
    });

    test('should validate PIT package mapping consistency', async () => {
      const mockAccessToken = 'test-token';

      // Test all package mappings
      const mappings = [
        { packageName: 'BASE', expectedSku: 'BR00094', expectedPrice: 234 },
        { packageName: 'SILVER', expectedSku: 'BR00095', expectedPrice: 294 },
        { packageName: 'GOLD', expectedSku: 'BR00096', expectedPrice: 524 },
        { packageName: 'PLATINUM', expectedSku: 'BR00097', expectedPrice: 684 }
      ];

      for (const mapping of mappings) {
        const result = await selectPitPackageForSimplifiedAccounting(mapping.packageName, mockAccessToken);

        expect(result.length).toBe(1);
        expect(result[0].sku).toBe(mapping.expectedSku);
        expect(result[0].price).toBe(mapping.expectedPrice);
        expect(result[0].quantity).toBe(1);
        expect(result[0].description).toBe(`PIT ${mapping.packageName} package for simplified accounting`);
      }
    });
  });
});
