#!/usr/bin/env node

/**
 * Comprehensive test runner for the HubSpot Line Item Management System
 * 
 * This script runs all tests in the correct order:
 * 1. Unit tests (fast, isolated)
 * 2. Integration tests (slower, requires HubSpot API)
 * 3. End-to-end tests (comprehensive scenarios)
 * 
 * Usage:
 *   npm run test:all
 *   npm run test:unit
 *   npm run test:integration
 *   npm run test:coverage
 */

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function runCommand(command, args, options = {}) {
  return new Promise((resolve, reject) => {
    const child = spawn(command, args, {
      stdio: 'inherit',
      cwd: projectRoot,
      ...options
    });

    child.on('close', (code) => {
      if (code === 0) {
        resolve(code);
      } else {
        reject(new Error(`Command failed with exit code ${code}`));
      }
    });

    child.on('error', (error) => {
      reject(error);
    });
  });
}

async function runTests() {
  const args = process.argv.slice(2);
  const testType = args[0] || 'all';

  log('\n🧪 HubSpot Line Item Management System - Test Suite', 'cyan');
  log('=' .repeat(60), 'cyan');

  // Validate environment before running tests
  if (!args.includes('--skip-validation')) {
    log('\n🔍 Validating test environment...', 'yellow');
    try {
      const { validateEnvironment } = await import('./validate-test-environment.js');
      const isValid = await validateEnvironment();

      if (!isValid) {
        log('\n❌ Environment validation failed. Fix issues before running tests.', 'red');
        log('Use --skip-validation flag to bypass validation (not recommended)', 'yellow');
        process.exit(1);
      }
    } catch (error) {
      log(`⚠️  Environment validation error: ${error.message}`, 'yellow');
      log('Continuing with tests...', 'yellow');
    }
  }

  try {
    switch (testType) {
      case 'unit':
        await runUnitTests();
        break;
      case 'integration':
        await runIntegrationTests();
        break;
      case 'coverage':
        await runCoverageTests();
        break;
      case 'all':
      default:
        await runAllTests();
        break;
    }

    log('\n✅ All tests completed successfully!', 'green');
    log('🎉 Your code is ready for production!', 'green');

  } catch (error) {
    log('\n❌ Tests failed!', 'red');
    log(`Error: ${error.message}`, 'red');
    process.exit(1);
  }
}

async function runUnitTests() {
  log('\n📋 Running Unit Tests...', 'yellow');
  log('Testing individual components and comprehensive business scenarios', 'yellow');

  // Use node to run vitest directly from node_modules
  await runCommand('node', ['node_modules/vitest/vitest.mjs', 'run', 'tests/unit', '--reporter=verbose']);

  log('✅ Unit tests completed', 'green');
  log('  • Core business logic validation', 'green');
  log('  • Comprehensive scenario testing', 'green');
  log('  • Edge case and boundary condition testing', 'green');
}

async function runIntegrationTests() {
  log('\n🔗 Running Integration Tests...', 'yellow');
  log('Testing HubSpot API integration with real data (optional)', 'yellow');

  // Check if HubSpot access token is available
  if (!process.env.HUBSPOT_ACCESS_TOKEN) {
    log('⚠️  Warning: HUBSPOT_ACCESS_TOKEN not found', 'yellow');
    log('Integration tests will be skipped', 'yellow');
    log('To run integration tests, set HUBSPOT_ACCESS_TOKEN environment variable', 'yellow');
    log('💡 Note: Comprehensive business logic is now tested in unit tests', 'blue');
    return;
  }

  await runCommand('node', ['node_modules/vitest/vitest.mjs', 'run', 'tests/integration', '--reporter=verbose']);

  log('✅ Integration tests completed', 'green');
  log('  • Real HubSpot API validation', 'green');
}

async function runCoverageTests() {
  log('\n📊 Running Tests with Coverage Analysis...', 'yellow');
  log('Generating comprehensive code coverage report', 'yellow');
  
  await runCommand('node', ['node_modules/vitest/vitest.mjs', 'run', '--coverage', '--reporter=verbose']);
  
  log('✅ Coverage analysis completed', 'green');
  log('📁 Coverage report generated in ./coverage directory', 'blue');
}

async function runAllTests() {
  log('\n🚀 Running Complete Test Suite...', 'bright');
  
  // 1. Unit Tests
  await runUnitTests();
  
  // 2. Integration Tests
  await runIntegrationTests();
  
  // 3. Coverage Report
  log('\n📊 Generating Final Coverage Report...', 'yellow');
  await runCommand('node', ['node_modules/vitest/vitest.mjs', 'run', '--coverage', '--reporter=silent']);
  
  // 4. Test Summary
  log('\n📋 Test Summary:', 'bright');
  log('✅ Unit Tests: Individual component testing', 'green');
  log('✅ Integration Tests: HubSpot API integration', 'green');
  log('✅ Coverage Analysis: Code coverage metrics', 'green');
  
  log('\n📁 Generated Reports:', 'blue');
  log('  • Coverage Report: ./coverage/lcov-report/index.html', 'blue');
  log('  • Test Results: Console output above', 'blue');
}

// Handle command line arguments
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  log('\n🧪 HubSpot Line Item Management System - Test Runner', 'cyan');
  log('\nUsage:', 'bright');
  log('  npm run test:all        # Run all tests', 'blue');
  log('  npm run test:unit       # Run unit tests only', 'blue');
  log('  npm run test:integration # Run integration tests only', 'blue');
  log('  npm run test:coverage   # Run tests with coverage', 'blue');
  log('\nEnvironment Variables:', 'bright');
  log('  HUBSPOT_ACCESS_TOKEN    # Required for integration tests', 'blue');
  log('\nTest Categories:', 'bright');
  log('  • Unit Tests: Fast, isolated component testing', 'green');
  log('  • Integration Tests: Real HubSpot API calls', 'yellow');
  log('  • Coverage Tests: Code coverage analysis', 'magenta');
  process.exit(0);
}

// Run the tests
runTests().catch((error) => {
  log(`\n💥 Fatal error: ${error.message}`, 'red');
  process.exit(1);
});
